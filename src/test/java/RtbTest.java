import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.http.apache.SimpleHttpClient;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.compress.GzipUtils;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.logic.media.taken.dto.*;
import com.google.gson.reflect.TypeToken;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class RtbTest {

    //        public static String url = "http://ssp-rtb.jm-ssp.cn";
//    public static String url = "http://ssp-api.3fahudong.com";
    public static String url = "http://127.0.0.1:9090";
//    public static String url = "http://***************:9090";
//        public static String url = "http://ssp-api.adkun.com";
    //测试-安卓-开屏-
    public static String mediaCode = "7b6fb536";
    public static String appId = "0aa40fdd";
    public static String tagId = "e5f83bbe";

    //信息流
//    public static String mediaCode = "6e9004c3";
//    public static String appId = "de5aee97";
//    public static String tagId = "144cb8f7";

    // ios-开屏
//    public static String tagId = "9060e435";
    public static Double price = 100d;
    public static String priceKey = "10630feac87f4bb4";

    //public static ExecutorService executorService = Executors.newFixedThreadPool(4);
    //public static final AtomicLong total = new AtomicLong();


    public static void main(String[] args) throws InterruptedException {
        try {
            rtb(url);
//            String eventUrl = "http://***************:9090/event/1?s=uuD88sFk1R5N4abjZ9khf4iuKhVLmKWuXPcpWVtftVl95f4uBO2UcrZ0S22fgVRU5nTvI5Fx0%2Fv4GJTX%2BK%2FxUmJ8vIj%2FZaIp2BXr1qcJRaRc3jyh1vWoMADliugiWFayqxtOgwa%2FziAXTxBf1%2FRBJCazDlftM03knnf6%2F4f6M%2F3dv98VWVeRlNjwsm5Rf0CpAXZCPOvzZmY10otnWzg8TQROhwUgl6eYEDjgzePLjkzc0vO9yczuEyzra4tlBY18NOwt0%2FkgD%2FBjHhTHSfSrPhRd9lKVQtSVaouGCPs33z8e9TnQTQrzFv1%2Bayv4UAn0&price=100";
//            List<String> urls = new ArrayList<>();
//            urls.add(eventUrl);


//            String click="http://***************:9090/event/2?s=uuD88sFk1R5N4abjZ9khf4iuKhVLmKWuXPcpWVtftVl95f4uBO2UcrZ0S22fgVRU5nTvI5Fx0%2Fv4GJTX%2BK%2FxUmJ8vIj%2FZaIp2BXr1qcJRaRc3jyh1vWoMADliugiWFayqxtOgwa%2FziAXTxBf1%2FRBJCazDlftM03knnf6%2F4f6M%2F3dv98VWVeRlNjwsm5Rf0CpAXZCPOvzZmY10otnWzg8TQROhwUgl6eYEDjgzePLjkzc0vO9yczuEyzra4tlBY18NOwt0%2FkgD%2FBjHhTHSfSrPhRd9lKVQtSVaouGCPs33z8e9TnQTQrzFv1%2Bayv4UAn0";
//            urls.add(click);
//            event(2, urls, "100");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void rtb(String reqUrl) {
        String rtbUrl = reqUrl + "/rtb/" + mediaCode;
        String reqJson = convertRtbRequest(false);
//        String reqJson = convertJson();
        Header[] headers = new Header[2];
        headers[0] = new BasicHeader("Content-Type", "application/json; charset=utf-8");
        headers[1] = new BasicHeader("Accept-Encoding", "gzip");
        HttpResult httpResult = SimpleHttpClient.posBytes(rtbUrl, GzipUtils.compress(reqJson.getBytes(StandardCharsets.UTF_8)), headers, null, 30000, 30000);
        if (httpResult.isSuccess()) {
            String respJson = httpResult.getDataStringUTF8();
            System.out.println(respJson);
            TakenMediaResponse response = JsonHelper.fromJson(TakenMediaResponse.class, respJson);
            Integer code = response.getCode();
            if (200 == code) {
                System.out.printf("TagSize:%s%n", response.getTags().size());
            }
        } else {
            System.out.printf("HttpCode:%s,Msg:%s%n", httpResult.getStatusLine(), httpResult.getThrowable());
        }
    }

    private static String convertJson() {
        return "{\"reqId\":\"83dd8237-85c9-4a08-af87-08fadc737796\",\"app\":{\"appId\":\"4dad5c18\",\"appName\":\"market\",\"appVersion\":\"1.1\",\"bundle\":\"com.maker.market\",\"appstoreUrl\":\"http://ssp-api.adkun.com/rtb/071b9ca9\"},\"tag\":{\"tagId\":\"b3afb79e\",\"width\":300,\"height\":300,\"price\":10},\"device\":{\"osType\":1,\"osVersion\":\"10.1\",\"deviceType\":3,\"brand\":\"OPPO\",\"model\":\"OPPO A7\",\"imei\":\"867905025513807\",\"caIds\":[{\"vendor\":0,\"timestamp\":0}],\"width\":1080,\"height\":1920,\"orientation\":2,\"screenDensity\":3.0,\"userAgent\":\"Mozilla/5.0 (Linux; Android 8.0.0; HWI-AL00 Build/HUAWEIHWI-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/62.0.3202.84 Mobile Safari/537.36\",\"ppi\":0,\"deviceMemory\":0,\"deviceHardDisk\":0,\"timeZone\":\"GMT+08:00\",\"language\":\"zh\",\"country\":\"cn\",\"cpuNum\":0,\"cpuFreq\":0.0,\"vendor\":\"OPPO\",\"appStoreVersion\":\"5500\",\"sysCompileTime\":\"0\",\"serialNO\":\"CUYDU19528018809\",\"androidId\":\"f2a0e1c04175dde6\",\"imeiMd5\":\"b0b1ab6c767beb7264f8f8fdfa8a6abd\",\"oaid\":\"AC0D016EC3D94343A2DD4431CC60B5C3eed7df67ccd7f2e6d83b42b6e2182ca1\",\"imsi\":\"460011418603055\"},\"network\":{\"ip\":\"************\",\"mac\":\"F4:F5:DB:32:B1:00\",\"connectType\":2,\"carrierType\":3,\"macMd5\":\"b0b1ab6c767beb7264f8f8fdfa8a6abd\"},\"geo\":{\"latitude\":34.946659088134766,\"longitude\":115.88764190673828}}";
    }

    public static void event(Integer type, List<String> urls, String price) {
        urls.forEach(url -> {
//            if (url.contains("127.0.0.1")) {
                if (StringUtils.isNotEmpty(price)) {
                    url = url.replace("__WIN_PRICE__", price);
                }
                HttpResult result = SimpleHttpClient.get(url, null);
                System.out.printf("EventUrl:%s,Result:%s,HttpCode:%s%n", url, result.isSuccess(), result.getStatusLine());
//            }
        });
    }

    public static void bill(List<String> urls, String price) {
        urls.forEach(url -> {
            if (StringUtils.isNotEmpty(price)) {
                url = url.replace("__WIN_PRICE__", price);
            }
            HttpResult result = SimpleHttpClient.get(url, null);
            System.out.printf("WinUrl:%s,Result：%s,HttpCode:%s%n", url, result.isSuccess(), result.getStatusLine());
        });
    }


    public static String convertRtbRequest(boolean isIos) {
        TakenMediaRequest request = new TakenMediaRequest();
        request.setReqId(UUID.randomUUID().toString().replace("-", ""));
        TakenMediaRequestApp app = new TakenMediaRequestApp();
        app.setAppId(appId);
        if (isIos) {
            app.setAppName("多闪");
            app.setBundle("my.maya.iphone");
        } else {
            app.setAppName("句读");
            app.setBundle("tech.caicheng.judourili");
        }


        app.setAppVersion("1.1.0");
        app.setAppVersionCode("1");
//        app.setAppDomainUrl("http://127.0.0.1:8080/domain");
//        app.setAppstoreUrl("http://127.0.0.1:8080/store");

        TakenMediaRequestTag tag = new TakenMediaRequestTag();
        tag.setTagId(tagId);
        tag.setWidth(1080);
        tag.setHeight(1920);
        tag.setMinDuration(5);
        tag.setMaxDuration(300);
//        tag.setQuery("游戏");
        if (null != price) {
            tag.setPrice(price);
        }

        TakenMediaRequestNetwork network = new TakenMediaRequestNetwork();
        String mac = "02:00:00:00:00:00";
        network.setIp("*************");
        network.setMac(mac);
        network.setMacMd5(Md5.md5(mac).toLowerCase());
        network.setConnectType(ConnectionType.NETWORK_4G.getType());
        network.setCarrierType(CarrierType.CM.getType());

        TakenMediaRequestGeo geo = new TakenMediaRequestGeo();
        geo.setCoordinateType(CoordinateType.GLOBAL.getType());
        geo.setLatitude(24.69673728942871);
        geo.setLongitude(108.0301742553711);

        TakenMediaRequestUser user = new TakenMediaRequestUser();
        user.setUserId("9000");
        user.setAge(20);
        user.setGender("F");
        user.setInterest(new String[]{"读书", "唱歌"});
        user.setIsMarriage(false);
        user.setIsSchool(true);

        TakenMediaRequestDevice device = new TakenMediaRequestDevice();
        device.setImei("867719069081567");
        device.setImeiMd5(Md5.md5("867719069081567").toLowerCase());

        device.setWidth(1440);
        device.setHeight(3036);
        device.setOrientation(OrientationType.VERTICAL.getType());
        device.setScreenDensity(3.0);
        device.setScreenInch(6.699999809265137);
        device.setPpi(480);
        device.setDeviceMemory(10737418240L);
        device.setDeviceHardDisk(239444426752L);
        device.setTimeZone("28800");
        device.setLocalName("Asia/Shanghai");
        device.setLanguage("zh");
        device.setCountry("CN");
//        device.setCpuNum(4);
//        device.setHmsVersion("61200302");
        device.setDeviceType(DeviceType.PHONE.getType());
//        device.setHmsAgVersion("1.0");
//        device.setAaid("90882");
//        device.setAaidMd5(Md5.md5("90882"));
//        device.setPaid("paid-099");
        device.setCpuFreq(22.8);

//        device.setAppStoreVersion("1.0");
//        device.setAppstoreVersionCode("1");
//        device.setRomVersion("rom-10");
        device.setSysStartTime("1705591478.444164583");
        device.setSysUpdateTime("1705591478.444164583");
        device.setSysCompileTime("1705591478.444164583");
        device.setSysInitTime("1702568592.000000000");
        device.setSysElapseTime("1736182055000");

        if (isIos) {
            device.setOsType(OsType.IOS.getType());
            device.setOsVersion("17.1.1");
            device.setBrand("Apple");
            device.setModel("iPhone13,4");
            device.setVendor("Apple");
            String idfa = "FAEB5E1D-5E10-4D99-BC37-AF9BE35B0D96";
            device.setIdfa(idfa);
            device.setIdfaMd5(Md5.md5(idfa).toLowerCase());
            device.setUserAgent("Mozilla/5.0 (Linux; Android 13; 23090RA98C Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36");
            //String udid = UUID.randomUUID().toString().replace("-","");
            //device.setOpenUdId(udid);
            device.setIdfaPolicy(1);
            device.setBatteryStatus(4);
            device.setBatteryPower(90);
            List<TakenMediaCaid> caids = new ArrayList<>();
            for (int i = 0; i < 1; i++) {
                TakenMediaCaid caid = new TakenMediaCaid();
                caid.setCaid("c162801832170c3d8579b11025f0f465");
                caid.setVersion("20230330");
                caids.add(caid);
            }
            device.setCaIds(caids);
        } else {
            device.setOsType(OsType.ANDROID.getType());
            device.setOsVersion("14");
            device.setDeviceName("2206122SC");
            device.setBrand("Xiaomi");
            device.setModel("2206122SC");

            device.setAndroidId("213e52b37dd4abd6");
            device.setAndroidIdMd5("fb28fc6902f655681ad8461c5d7e820c");
            device.setOaid("a96133ce3a4e08b4");
            device.setOaidMd5("c5d05513e8ea0ee0a3b3ad74e680059f");

            device.setVendor("XIAOMI");
            device.setBootMark("7370f11e-e1f8-431a-a20e-4e2d1007a97a");
            device.setUpdateMark("1736182055.678019164");
            device.setUserAgent("Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36");
        }
        device.setPaid("c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07");
//        String vaId = UUID.randomUUID().toString().replace("-","");
//        device.setVaid(vaId);
//        device.setVaidMd5(Md5.md5(vaId).toLowerCase());
//        String imsi = UUID.randomUUID().toString().replace("-","");
//        device.setImsi(imsi);
//        device.setImsiMd5(Md5.md5(imsi).toLowerCase());
//        device.setSysUiVersion("1.0");
        device.setApiLevel(34);
//        device.setCookie("cookie");
//        device.setReferer("refer");
//        device.setIsRoot(false);

        String json = "[\n" +
                "    {\n" +
                "        \"packageName\": \"com.eg.android.AlipayGphone\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"packageName\": \"com.taobao.taobao\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"packageName\": \"com.xunmeng.pinduoduo\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"packageName\": \"com.baidu.searchbox\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"packageName\": \"com.jingdong.app.mall\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"packageName\": \"com.sankuai.meituan.takeoutnew\"\n" +
                "    }\n" +
                "]";
        List<TakenMediaRequestInstalledApp> installedApps = JsonHelper.fromJson(new TypeToken<List<TakenMediaRequestInstalledApp>>() {
        }, json);
        //device.setInstalledAppInfo(installedApps);


        List<TakenMediaRequestPageInfo> infos = new ArrayList<>();
        infos.add(null);
        TakenMediaRequestPageInfo gameInfo = new TakenMediaRequestPageInfo();
        gameInfo.setCat("游戏");
        gameInfo.setPageUrl("http://x.x.x");
        infos.add(gameInfo);

        TakenMediaRequestPageInfo carInfo = new TakenMediaRequestPageInfo();
        carInfo.setCat("汽车");
        carInfo.setPageUrl("http://x.x.com");
        infos.add(carInfo);

        TakenMediaRequestPageInfo bookInfo = new TakenMediaRequestPageInfo();
        bookInfo.setCat("历史");
        bookInfo.setTitle("资治通鉴");
        bookInfo.setKeywords("小说");
        bookInfo.setUseTime(60 * 60);
        bookInfo.setContentTime(60);
        infos.add(bookInfo);

        TakenMediaRequestPageInfo comicInfo = new TakenMediaRequestPageInfo();
        comicInfo.setCat("奇幻漫画");
        comicInfo.setTitle("漫画名称");
        comicInfo.setKeywords("漫画");
        comicInfo.setUseTime(60 * 60);
        comicInfo.setContentTime(60 * 60);
        infos.add(comicInfo);

        TakenMediaRequestPageInfo videoInfo = new TakenMediaRequestPageInfo();
        videoInfo.setCat("悬疑剧");
        videoInfo.setTitle("视频标题");
        videoInfo.setUseTime(60 * 60);
        videoInfo.setContentTime(60 * 60);
        videoInfo.setKeywords("短剧");
        infos.add(videoInfo);
        request.setPageInfos(infos);

        request.setApp(app);
        request.setTag(tag);
        request.setNetwork(network);
        request.setGeo(geo);
        request.setUser(user);
        request.setDevice(device);

        return JsonHelper.toJsonStringWithoutNull(request);
    }
}
