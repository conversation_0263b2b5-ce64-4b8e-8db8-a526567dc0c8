<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="demo-form-inline" size="mini">
        <el-form-item label="协议" prop="protocolId">
          <el-select
            v-model="listQuery.protocolId"
            filterable
            clearable
            placeholder="请选择协议"
            style="width: 100%"
            remote
            :remote-method="findMediaProtocol"
          >
            <el-option
              v-for="protocol in protocolOption"
              :key="protocol.id"
              :label="protocol.name + ' - ' + protocol.code"
              :value="protocol.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="媒体">
          <el-input v-model="listQuery.name" placeholder="名称或CODE" />
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" icon="el-icon-refresh" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button v-if="AuthUtils.hasAuth('MEDIA_ADD')" type="success" icon="el-icon-circle-plus-outline" @click="handleAdd">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
    >
      <el-table-column align="center" label="序号" min-width="60" type="index" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="CODE" prop="code" align="center" />
      <el-table-column label="协议" align="center">
        <template slot-scope="{ row }"> {{ row.protocolName }} - {{ row.protocolCode }} </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="60">
        <template slot-scope="{ row }">
          <el-button
            v-if="AuthUtils.hasAuth('MEDIA_MODIFY')"
            type="success"
            size="mini"
            icon="el-icon-edit"
            title="编辑"
            circle
            @click="handleModify(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-drawer
      title="新增媒体"
      :show-close="false"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaAdd v-if="addOpen" :is-update.sync="addUpdate" @changePageOpen="changeAddOpen" />
    </el-drawer>

    <el-drawer
      title="修改媒体"
      :show-close="false"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaModify v-if="modifyOpen" :id.sync="infoId" :is-update.sync="modifyUpdate" @changePageOpen="changeModifyOpen" />
    </el-drawer>
  </div>
</template>

<script>
import { pageMedia } from '@/api/media/mediaMain' // 后台接口
import { listMediaProtocol } from '@/api/media/mediaProtocol'
import Pagination from '@/components/Pagination' // 分页
import MediaAdd from './MediaAdd' // 分页
import MediaModify from './MediaModify'
export default {
  name: 'Media', // 路由名字
  components: { Pagination, MediaAdd, MediaModify }, // 分页模块
  data() {
    return {
      tableKey: 0,
      paginationShow: true,
      list: null,
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        start: 0,
        limit: 20,
        name: null,
        protocolId: null
      },
      protocolOption: [],
      infoId: null,
      mediaName: null,
      addOpen: false,
      addUpdate: false,
      modifyOpen: false,
      modifyUpdate: false,
      appOpen: false
    }
  },
  created() {
    this.findMediaProtocol(null)
    this.getList()
  },
  activated() {},
  methods: {
    findMediaProtocol(name) {
      listMediaProtocol({ name: name }).then(response => {
        this.protocolOption = response.result
      })
    },
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      pageMedia(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.name = null
      this.listQuery.protocolId = null
      this.getList()
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return 'success-row'
      }
      return ''
    },
    handleModify(row) {
      // 编辑
      this.infoId = row.id
      this.modifyOpen = true
    },
    changeModifyOpen(open) {
      this.modifyOpen = open
      if (this.modifyUpdate) {
        this.getList()
      }
    },
    handleAdd() {
      // 新增
      this.addOpen = true
    },
    changeAddOpen(open) {
      this.addOpen = open
      if (this.addUpdate) {
        this.getList()
      }
    }
  }
}
</script>

<style scoped></style>
