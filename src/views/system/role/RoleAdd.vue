<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="角色名称" prop="roleName">
      <el-input v-model="ruleForm.roleName" maxlength="20" />
    </el-form-item>
    <el-form-item label="角色描述" prop="remark">
      <el-input v-model="ruleForm.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" maxlength="50" resize="none" />
    </el-form-item>
    <el-form-item label="角色权限">
      <el-tree
        ref="tree"
        :data="data"
        show-checkbox
        default-expand-all
        node-key="auth"
        highlight-current
        :props="defaultProps"
        :render-content="renderContent"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { addRole } from '@/api/system/role' // 接口
import { allroleName } from '@/utils/Validate' // validate 验证
import rtree from '@/router/resource-tree'
export default {
  name: 'RoleAdd',
  props: {
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    // 表单验证规则 角色名称
    var roleNameReg = (rule, value, callback) => {
      if (value === null) {
        callback(new Error('角色名称不能为空'))
      } else if (!allroleName(value)) {
        callback(new Error('角色名不能包含特殊字符'))
      } else {
        callback()
      }
    }
    // 角色描述
    var remarkReg = (rule, value, callback) => {
      if (value === null) {
        callback(new Error('角色描述不能为空'))
      } else {
        callback()
      }
    }
    return {
      btnClicked: false,
      ruleForm: {
        roleName: null,
        remark: null,
        resourceCodes: []
      },
      rules: {
        roleName: [{ required: true, trigger: 'blur', validator: roleNameReg }],
        remark: [{ required: true, trigger: 'blur', validator: remarkReg }]
      },
      data: rtree,
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  computed: {},
  mounted() {
    var nodes = document.getElementsByClassName('last-node')
    for (var i = 0; i < nodes.length; i++) {
      nodes[i].parentElement.parentElement.style.cssFloat = 'left' // 增加css样式
    }
  },
  created() {},
  methods: {
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          return false
        }
        const arr = this.$refs['tree'].getCheckedKeys().concat(this.$refs['tree'].getHalfCheckedKeys())
        arr.forEach(item => {
          if (item) {
            this.ruleForm.resourceCodes.push(item)
          }
        })
        if (this.ruleForm.resourceCodes.length === 0) {
          this.$message({
            type: 'error',
            message: '请至少选择一个角色权限!'
          })
          return false
        }
        this.btnClicked = true
        addRole(this.ruleForm)
          .then(response => {
            if (response.success === true) {
              this.$message({
                message: '新增角色操作成功',
                type: 'success'
              })
              this.$emit('update:isUpdate', true)
              this.closeMe()
            }
          })
          .catch(() => {
            this.btnClicked = false
          })
        return true
      })
    },
    renderContent(h, { node, data, store }) {
      let classname = ''
      if (node.level === 3) {
        // 给最后一个节点，添加class=last-node，根据last-node，寻找父父dom，并添加float：left
        classname = 'last-node'
      }
      return (
        <span class={classname}>
          <span>{node.label}</span>
        </span>
      )
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
