<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="ruleForm" size="mini">
        <el-form-item label="状态" prop="state">
          <el-select v-model="listQuery.state" filterable clearable placeholder="请选择状态" style="width: 100%">
            <el-option label="全部" value="" />
            <el-option label="启动" :value="1" />
            <el-option label="停止" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="listQuery.name" placeholder="名称" />
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button v-if="AuthUtils.hasAuth('DMP_PACK_ADD')" type="success" icon="el-icon-circle-plus-outline" @click="handleAdd">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
    >
      <el-table-column align="center" label="序号" width="60" type="index" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="类型" align="center" prop="type">
        <template slot-scope="{ row }">
          {{ row.type === 1 ? '自定义' : '实时' }}
        </template>
      </el-table-column>
      <el-table-column label="事件" align="center" prop="baseType">
        <template slot-scope="{ row }">
          {{ parseEventType(row.baseType) }}
        </template>
      </el-table-column>
      <el-table-column label="缓存时长" align="center" prop="cacheType">
        <template slot-scope="{ row }">
          {{ row.cacheType === 1 ? '当天' : row.cacheDay + '天' }}
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="total" />
      <el-table-column label="缓存状态" align="center" prop="baseBuildState">
        <template slot-scope="{ row }">
          {{ parseBuildState(row) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state">
        <template slot-scope="{ row }">
          {{ row.state == 0 ? '停止' : '启动' }}
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-popconfirm
            v-if="AuthUtils.hasAuth('DMP_PACK_OPERATOR') && row.state === 0"
            title="确定要启用吗?"
            icon="el-icon-info"
            icon-color="green"
            @confirm="handState(row, 1)"
          >
            <el-button slot="reference" type="success" size="mini" title="启动" circle><svg-icon icon-class="start" /></el-button>
          </el-popconfirm>
          <el-popconfirm
            v-if="AuthUtils.hasAuth('DMP_PACK_OPERATOR') && row.state === 1 && (row.baseBuildState === 2 || row.baseBuildState === -1)"
            title="确定要停用吗?"
            icon="el-icon-info"
            icon-color="#E6A23C"
            @confirm="handState(row, 0)"
          >
            <el-button slot="reference" type="danger" size="mini" title="停止" circle><svg-icon icon-class="stop" /></el-button>
          </el-popconfirm>
          <el-button
            v-if="AuthUtils.hasAuth('DMP_PACK_MODIFY') && row.state === 0"
            size="mini"
            type="success"
            icon="el-icon-edit"
            circle
            title=""
            @click="handleModify(row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-drawer
      title="新增人群包"
      :show-close="false"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <DmpPackageAdd v-if="addOpen" :is-update.sync="addUpdate" @changePageOpen="changeAddOpen" />
    </el-drawer>

    <el-drawer
      title="修改人群包"
      :show-close="false"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <DmpPackageModify v-if="modifyOpen" :id.sync="infoId" :is-update.sync="modifyUpdate" @changePageOpen="changeModifyOpen" />
    </el-drawer>
  </div>
</template>
<script>
import { pageApi, onOffApi } from '@/api/dmp/dmpPackage'
import { listEventApi } from '@/api/public/typeinfo'
import Pagination from '@/components/Pagination'
import DmpPackageAdd from './DmpPackageAdd' // 分页
import DmpPackageModify from './DmpPackageModify'
export default {
  name: 'DmpPackage',
  components: { Pagination, DmpPackageAdd, DmpPackageModify },
  data() {
    return {
      tableKey: 0,
      paginationShow: true,
      list: [],
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        state: null,
        name: null,
        type: null,
        start: 0,
        limit: 20
      },
      eventTypeList: [],
      appLoading: false,
      infoId: null,
      addOpen: false,
      addUpdate: false,
      modifyOpen: false,
      modifyUpdate: false,
      pageProtocol: 'http'
    }
  },
  computed: {},
  created() {
    listEventApi().then(response => {
      this.eventTypeList = response.result.filter(item => item.id !== 999)
    })
    this.getList()
  },
  methods: {
    parseEventType(id) {
      const value = this.eventTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : ''
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.name = null
      this.listQuery.state = ''
      this.getList()
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    createMM() {
      this.$emit('changePageOpen', false)
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return 'success-row'
      }
      return ''
    },
    handleAdd() {
      this.addOpen = true
    },
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      pageApi(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    handleModify(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    changeAddOpen(open) {
      this.addOpen = open
      if (this.addUpdate) {
        this.getList()
      }
    },
    changeModifyOpen(open) {
      this.modifyOpen = open
      if (this.modifyUpdate) {
        this.getList()
      }
    },
    handState(row, s) {
      onOffApi({ id: row.id, status: s }).then(resp => {
        if (resp.success) {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.getList()
        }
      })
    },
    parseBuildState(row) {
      if (row.baseBuildState === 0) {
        return '待处理'
      }
      if (row.baseBuildState === 1) {
        return '处理中'
      }
      if (row.baseBuildState === 2) {
        return '完成'
      }
      if (row.baseBuildState === -1) {
        return '失败'
      }
      return ''
    }
  }
}
</script>

<style scoped>
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
::deep .el-drawer__body {
  overflow: auto;
}
</style>
