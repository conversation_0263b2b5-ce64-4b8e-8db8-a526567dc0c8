<template>
  <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" size="mini" class="ruleForm">
    <el-form-item label="省份:" prop="provinceId">
      <el-select
        v-model="ruleForm.provinceId"
        style="width: 100%"
        filterable
        reserve-keyword
        remote
        :remote-method="searchProvince"
        placeholder="输入省份名称搜索"
      >
        <el-option v-for="item in provinceList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="名称:" prop="name">
      <el-input v-model="ruleForm.name" />
    </el-form-item>
    <el-form-item label="代码:" prop="code">
      <el-input v-model="ruleForm.code" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :disabled="btnClicked" @click="submitForm('ruleForm')">确定</el-button>
      <el-button @click="createMM">取消</el-button>
      <el-button type="danger" @click="createMM">关闭</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { listProvince } from '@/api/base/province'
import { modifyCity, infoCity } from '@/api/base/city'
export default {
  name: 'CityModify',
  props: {
    id: {
      required: true,
      type: Number
    },
    isUpdate: {
      required: true,
      type: Boolean
    }
  },
  data() {
    // 表单验证规则 用户名
    var codeReg = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('代码不能为空'))
      } else if (!/^[0-9]+$/.test(value)) {
        callback(new Error('代码仅支持数字'))
      } else {
        callback()
      }
    }
    return {
      btnClicked: false,
      ruleForm: {
        id: null,
        name: null,
        code: null,
        provinceId: null
      },
      provinceList: [],
      rules: {
        name: [{ required: true, trigger: 'blur', message: '不能为空' }],
        code: [{ required: true, trigger: 'blur', validator: codeReg }],
        provinceId: [{ required: true, trigger: 'blur', message: '未选择' }]
      }
    }
  },
  computed: {},
  created() {
    this.searchProvince(null)
    infoCity({ id: this.id }).then(response => {
      this.ruleForm = response.result
    })
  },
  methods: {
    searchProvince(name) {
      listProvince({ name: name }).then(response => {
        this.provinceList = response.result
      })
    },
    closeMe() {
      this.$emit('changePageOpen', false)
      this.btnClicked = false
    },
    createMM() {
      this.$emit('update:isUpdate', false)
      this.closeMe()
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btnClicked = true
          modifyCity(this.ruleForm)
            .then(response => {
              if (response.success === true) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.$emit('update:isUpdate', true)
                this.closeMe()
              }
            })
            .catch(() => {
              this.btnClicked = false
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.ruleForm {
  margin-left: 2%;
  margin-right: 2%;
}
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
</style>
