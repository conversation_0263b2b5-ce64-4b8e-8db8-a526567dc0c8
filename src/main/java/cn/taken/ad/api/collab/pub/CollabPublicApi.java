package cn.taken.ad.api.collab.pub;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.utils.result.Result;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.configuration.password.CollabPasswordGenerator;
import cn.taken.ad.configuration.web.captcha.CaptchaChecker;
import cn.taken.ad.constant.state.BaseState;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.constant.web.XssIgnore;
import cn.taken.ad.core.dto.web.global.ChangePasswordReq;
import cn.taken.ad.core.dto.web.global.LoginReq;
import cn.taken.ad.core.pojo.system.VisitorUser;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.core.service.system.VisitorUserService;
import cn.taken.ad.utils.web.CollabWebToken;
import cn.taken.ad.utils.web.CollabWebUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 公共API
 */
@RestController
@RequestMapping(value = "/b/pub")
public class CollabPublicApi {
    /**
     * 验证码登录标识
     */
    private final static String CAPTCHA_TAG_LOGIN = "LOGIN";
    @Resource
    private VisitorUserService visitorUserService;
    @Resource
    private OperLogService operLogService;
    @Resource
    private CaptchaChecker captchaChecker;
    @Resource
    private CollabPasswordGenerator collabPasswordGenerator;
    @Resource(name = "BaseRedis")
    private RedisClient baseRedis;

    private static final String MODEL = "用户登录";

    /**
     * 验证码图片
     */
    @Validated
    @XssIgnore
    @RequestMapping(value = "/loginCaptcha/{uuid}/{flush}", method = RequestMethod.GET)
    public void loginCaptcha(@PathVariable @Valid @NotNull(message = "非法请求") String uuid, @PathVariable @Valid @NotNull(message = "非法请求") String flush) throws Exception {
        captchaChecker.writeByRedis(uuid, CAPTCHA_TAG_LOGIN, 180);
    }

    /**
     * 登录
     */
    @XssIgnore
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public SuperResult<CollabWebToken> login(@RequestBody @Valid LoginReq loginReq) {
        String username = loginReq.getUsername().toLowerCase();
        boolean isOk = captchaChecker.checkByRedis(loginReq.getUuid(), CAPTCHA_TAG_LOGIN, loginReq.getCaptcha());
        if (!isOk) {
            return SuperResult.badResult("验证码错误");
        }
        VisitorUser user = visitorUserService.findByUserName(username);
        if (user == null) {
            return SuperResult.badResult("用户名或密码错误");
        }
        if (user.getUserState() == BaseState.DELETE.getState()) {
            return SuperResult.badResult("用户名或密码错误");
        }
        if (user.getUserState() == BaseState.OFF.getState()) {
            return SuperResult.badResult("用户已锁定，请联系管理员解锁");
        }
        if (!collabPasswordGenerator.verifyLogin(loginReq, user)) {
            return SuperResult.badResult("用户名或密码错误");
        }
        CollabWebToken token = CollabWebUtils.login(baseRedis, user);
        operLogService.saveCooperateLog(MODEL, token.getUserId() + " | " + token.getRealname() + " | " + token.getUsername() + " 通过账号登录", token.getUserId());
        return SuperResult.rightResult(token);
    }

    /**
     * 登出
     */
    @XssIgnore
    @RequestMapping(value = "/logout", method = {RequestMethod.POST, RequestMethod.GET})
    public Result logout() {
        CollabWebToken token = CollabWebUtils.getWebToken();
        if (token != null) {
            operLogService.saveCooperateLog(MODEL, token.getUserId() + " | " + token.getRealname() + " | " + token.getUsername() + " 登出", token.getUserId());
        }
        CollabWebUtils.logout(baseRedis);
        return Result.right();
    }

    /**
     * 修改密码
     */
    @XssIgnore
    @WebAuth()
    @RequestMapping(value = "/changePassword", method = RequestMethod.POST)
    public SuperResult<String> changePass(@RequestBody @Valid ChangePasswordReq changePasswordReq) {
        CollabWebToken token = CollabWebUtils.getWebToken();
        VisitorUser user = visitorUserService.findById(token.getUserId());
        if (!collabPasswordGenerator.verifyChangePassword(changePasswordReq, user)) {
            return SuperResult.badResult("密码错误");
        }
        String newPass = collabPasswordGenerator.encrypt(changePasswordReq.getNewPassword());
        SuperResult<String> result = visitorUserService.modifyPassword(user.getId(), newPass);
        if (result.getSuccess()) {
            operLogService.saveCooperateLog(MODEL, token.getUserId() + "|" + token.getRealname() + "|" + token.getUsername() + " 修改密码", token.getUserId());
            CollabWebUtils.logout(baseRedis);
        }
        return result;
    }
}
