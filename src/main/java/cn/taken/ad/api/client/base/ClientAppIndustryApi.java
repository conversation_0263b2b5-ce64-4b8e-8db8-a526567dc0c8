package cn.taken.ad.api.client.base;

import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.dto.web.oper.base.industry.BaseAppIndustryListReq;
import cn.taken.ad.core.pojo.base.BaseAppIndustry;
import cn.taken.ad.core.service.base.BaseAppIndustryService;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.utils.web.ClientWebUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * app行业
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/c/base/app/industry")
public class ClientAppIndustryApi {

    @Resource
    private BaseAppIndustryService baseAppIndustryService;
    @Resource
    private OperLogService operLogService;

    /**
     * 列表
     */
    @WebAuth()
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public SuperResult<List<BaseAppIndustry>> listAppIndustry(@RequestBody @Valid BaseAppIndustryListReq req) {
        List<BaseAppIndustry> page = baseAppIndustryService.findList(req);
        operLogService.saveOperLog("App行业管理", "查询App行业列表", ClientWebUtils.getWebToken().getUserId());
        return SuperResult.rightResult(page);
    }

}
