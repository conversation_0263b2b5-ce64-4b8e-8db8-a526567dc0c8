package cn.taken.ad.core.dto.web.oper.media.main;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class MediaModifyReq {

    @NotNull(message = "未找到信息")
    private Long id;
    @NotNull(message = "公司名称未填写")
    private String name;
    private String code;
    private String linkman;
    private String address;
    private String linkmanMobile;
    @NotNull(message = "AES价格密钥未填写")
    private String priceKey;
    private String pnyParam;
    private Double settlementRatio;
    private Boolean filterOnOff;
    private String filterUrlDomain;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLinkmanMobile() {
        return linkmanMobile;
    }

    public void setLinkmanMobile(String linkmanMobile) {
        this.linkmanMobile = linkmanMobile;
    }

    public String getPriceKey() {
        return priceKey;
    }

    public void setPriceKey(String priceKey) {
        this.priceKey = priceKey;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Double getSettlementRatio() {
        return settlementRatio;
    }

    public void setSettlementRatio(Double settlementRatio) {
        this.settlementRatio = settlementRatio;
    }

    public Boolean getFilterOnOff() {
        return filterOnOff;
    }

    public void setFilterOnOff(Boolean filterOnOff) {
        this.filterOnOff = filterOnOff;
    }

    public String getFilterUrlDomain() {
        return filterUrlDomain;
    }

    public void setFilterUrlDomain(String filterUrlDomain) {
        this.filterUrlDomain = filterUrlDomain;
    }
}
