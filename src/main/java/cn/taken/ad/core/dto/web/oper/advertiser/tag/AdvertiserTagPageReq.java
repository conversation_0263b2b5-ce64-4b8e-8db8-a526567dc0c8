package cn.taken.ad.core.dto.web.oper.advertiser.tag;

import cn.taken.ad.core.dto.global.PageReq;

public class AdvertiserTagPageReq extends PageReq {

    private String name;
    private Long advertiserId;
    private Long advertiserAppId;
    private Integer settlementType;
    private Long mediaTagId;
    private Integer tagType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }
}
