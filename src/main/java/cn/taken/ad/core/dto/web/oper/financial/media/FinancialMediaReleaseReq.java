package cn.taken.ad.core.dto.web.oper.financial.media;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class FinancialMediaReleaseReq {

    @NotNull(message = "记录未选择")
    private Long id;
    @NotNull(message = "状态错误")
    private Integer releaseState;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getReleaseState() {
        return releaseState;
    }

    public void setReleaseState(Integer releaseState) {
        this.releaseState = releaseState;
    }
}
