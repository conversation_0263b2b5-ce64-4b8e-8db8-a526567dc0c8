package cn.taken.ad.core.dto.web.oper.financial.media;

import cn.taken.ad.core.dto.global.PageReq;

import javax.validation.Valid;

@Valid
public class FinancialMediaPageReq extends PageReq {

    private String beginTime;
    private String endTime;
    private Long mediaTagId;
    private Long mediaId;
    private Long mediaAppId;
    private Integer state;
    private Integer releaseState;
    private Integer bidType;
    //查询实际数据为0或者null
    private Integer realData;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getReleaseState() {
        return releaseState;
    }

    public void setReleaseState(Integer releaseState) {
        this.releaseState = releaseState;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public Integer getRealData() {
        return realData;
    }

    public void setRealData(Integer realData) {
        this.realData = realData;
    }
}
