package cn.taken.ad.core.dto.web.oper.tool.conversion;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class ConversionToolsBaseOperReq {

    @NotNull(message = "名称不能为空")
    private String name;

    @NotNull(message = "链接不能为空")
    private String url;

    @NotNull(message = "类型未选择")
    private Integer type;

    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
