package cn.taken.ad.core.dto.web.oper.system.user;

import cn.taken.ad.core.pojo.system.OperUser;

import java.util.Date;

public class OperUserInfo {

    private Long id;
    private String username;
    private String realname;
    private String mobile;
    private String email;
    private Long operatorId;
    private Integer userState;
    private Date createTime;
    private Long[] roleIds;
    private String roleNames;
    private Boolean isLogin;

    public OperUserInfo(OperUser user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.realname = user.getRealname();
        this.mobile = user.getMobile();
        this.email = user.getEmail();
        this.userState = user.getUserState();
        this.createTime = user.getCreateTime();
        this.operatorId = user.getOperatorId();
    }

    public OperUserInfo() {

    }

    public Boolean getIsLogin() {
        return isLogin;
    }

    public void setIsLogin(Boolean isLogin) {
        this.isLogin = isLogin;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getUserState() {
        return userState;
    }

    public void setUserState(Integer userState) {
        this.userState = userState;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public Long[] getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds) {
        this.roleIds = roleIds;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

}
