package cn.taken.ad.core.dto.web.oper.advertiser.tag;


import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Valid
public class AdvertiserTagOperReqBase {

    @NotNull(message = "未输入名称")
    private String name;
    private String code;
    @NotNull(message = "未选择类型")
    private Integer type;
    @NotNull(message = "未输入超时时间")
    private Integer timeout;
    private String pnyParam;

    @NotNull(message = "上报事件未选择")
    private List<Integer> appendEventTypes;

    @NotNull(message = "未选择结算方式")
    private Integer settlementType;
    private Integer bidPriceType;
    private Integer bidRisesRatio;
    private Integer sharingPriceType;
    private Double fixedPrice;

    @NotNull(message = "未选择基础规则是否开启")
    private Boolean limitRuleOpen;
    private Integer limitType;
    private Long quota;
    private Integer startTime;
    private Integer endTime;

    private Long filterExposureNum;
    private Long filterClickNum;
    private Long filterDeviceReqNum;
    private Long filterDeviceExposureNum;

    @NotNull(message = "未选择定向是否开启")
    private Boolean directOnOff;
    private List<String[]> directCitySelection;
    private List<Long[]> industrySelection;
    private String targetAppPackage;
    private String targetDeviceModel;
    private List<Integer> targetDeviceBrandCheck;
    private List<Integer> targetNetworkCheck;
    private List<Integer> targetOperatorCheck;
    private Integer targetOsVersionType;
    private String targetOsVersion;
    private String targetInstalledAppPackage;
    private Long targetPackId;

    @NotNull(message = "未选择过滤是否开启")
    private Boolean filterOnOff;
    private List<String[]> filterCitySelection;
    private List<Long[]> filterIndustrySelection;
    private String filterAppPackage;
    private String filterDeviceModel;
    private String filterUrlDomain;
    private List<Integer> filterDeviceBrandCheck;
    private List<Integer> filterNetworkCheck;
    private List<Integer> filterOperatorCheck;
    private Integer filterEmptyDevice;
    private Integer filterInvalidDevice;
    private Integer filterForeignIp;
    private String filterInstalledAppPackage;
    private Long filterPackId;
    private Integer filterRepeatEvent;


    protected void fillValue(AdvertiserTag tag) {
        tag.setName(this.getName());
        tag.setCode(this.getCode());
        tag.setType(this.getType());
        tag.setTimeout(this.getTimeout());
        tag.setPnyParam(this.getPnyParam());

        tag.setAddEventTypes(JsonHelper.toJsonString(this.getAppendEventTypes()));

        tag.setSettlementType(this.getSettlementType());
        tag.setBidPriceType(this.getBidPriceType());
        tag.setBidRisesRatio(this.getBidRisesRatio());
        tag.setSharingPriceType(this.getSharingPriceType());
        tag.setFixedPrice(this.getFixedPrice());

        tag.setLimitRuleOpen(this.getLimitRuleOpen());
        tag.setLimitType(this.getLimitType());
        tag.setQuota(this.getQuota());
        tag.setStartTime(this.getStartTime());
        tag.setEndTime(this.getEndTime());

        tag.setFilterExposureNum(this.getFilterExposureNum());
        tag.setFilterClickNum(this.getFilterClickNum());
        tag.setFilterDeviceReqNum(this.getFilterDeviceReqNum());
        tag.setFilterDeviceExposureNum(this.getFilterDeviceExposureNum());

        tag.setDirectOnOff(this.getDirectOnOff());
        tag.setTargetArea(JsonHelper.toJsonString(this.getDirectCitySelection()));
        tag.setTargetAppIndustry(JsonHelper.toJsonString(this.getIndustrySelection()));
        tag.setTargetAppPackage(this.getTargetAppPackage());
        tag.setTargetDeviceModel(this.getTargetDeviceModel());
        tag.setTargetDeviceBrand(JsonHelper.toJsonString(this.getTargetDeviceBrandCheck()));
        tag.setTargetNetwork(JsonHelper.toJsonString(this.getTargetNetworkCheck()));
        tag.setTargetOperator(JsonHelper.toJsonString(this.getTargetOperatorCheck()));
        tag.setTargetOsVersionType(this.getTargetOsVersionType());
        tag.setTargetOsVersion(this.getTargetOsVersion());
        tag.setTargetInstalledAppPackage(this.getTargetInstalledAppPackage());
        tag.setTargetPackId(this.getTargetPackId());

        tag.setFilterOnOff(this.getFilterOnOff());
        tag.setFilterArea(JsonHelper.toJsonString(this.getFilterCitySelection()));
        tag.setFilterAppIndustry(JsonHelper.toJsonString(this.getFilterIndustrySelection()));
        tag.setFilterAppPackage(this.getFilterAppPackage());
        tag.setFilterDeviceModel(this.getFilterDeviceModel());
        tag.setFilterUrlDomain(this.getFilterUrlDomain());
        tag.setFilterDeviceBrand(JsonHelper.toJsonString(this.getFilterDeviceBrandCheck()));
        tag.setFilterNetwork(JsonHelper.toJsonString(this.getFilterNetworkCheck()));
        tag.setFilterOperator(JsonHelper.toJsonString(this.getFilterOperatorCheck()));
        tag.setFilterEmptyDevice(this.getFilterEmptyDevice());
        tag.setFilterInvalidDevice(this.getFilterInvalidDevice());
        tag.setFilterForeignIp(this.getFilterForeignIp());
        tag.setFilterInstalledAppPackage(this.getFilterInstalledAppPackage());
        tag.setFilterPackId(this.getFilterPackId());
        tag.setFilterRepeatEvent(this.getFilterRepeatEvent());

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }

    public Integer getBidPriceType() {
        return bidPriceType;
    }

    public void setBidPriceType(Integer bidPriceType) {
        this.bidPriceType = bidPriceType;
    }

    public Integer getBidRisesRatio() {
        return bidRisesRatio;
    }

    public void setBidRisesRatio(Integer bidRisesRatio) {
        this.bidRisesRatio = bidRisesRatio;
    }

    public Integer getSharingPriceType() {
        return sharingPriceType;
    }

    public void setSharingPriceType(Integer sharingPriceType) {
        this.sharingPriceType = sharingPriceType;
    }

    public Double getFixedPrice() {
        return fixedPrice;
    }

    public void setFixedPrice(Double fixedPrice) {
        this.fixedPrice = fixedPrice;
    }

    public Boolean getLimitRuleOpen() {
        return limitRuleOpen;
    }

    public void setLimitRuleOpen(Boolean limitRuleOpen) {
        this.limitRuleOpen = limitRuleOpen;
    }

    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }

    public Long getQuota() {
        return quota;
    }

    public void setQuota(Long quota) {
        this.quota = quota;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Long getFilterExposureNum() {
        return filterExposureNum;
    }

    public void setFilterExposureNum(Long filterExposureNum) {
        this.filterExposureNum = filterExposureNum;
    }

    public Long getFilterClickNum() {
        return filterClickNum;
    }

    public void setFilterClickNum(Long filterClickNum) {
        this.filterClickNum = filterClickNum;
    }

    public Long getFilterDeviceReqNum() {
        return filterDeviceReqNum;
    }

    public void setFilterDeviceReqNum(Long filterDeviceReqNum) {
        this.filterDeviceReqNum = filterDeviceReqNum;
    }

    public Long getFilterDeviceExposureNum() {
        return filterDeviceExposureNum;
    }

    public void setFilterDeviceExposureNum(Long filterDeviceExposureNum) {
        this.filterDeviceExposureNum = filterDeviceExposureNum;
    }

    public Boolean getDirectOnOff() {
        return directOnOff;
    }

    public void setDirectOnOff(Boolean directOnOff) {
        this.directOnOff = directOnOff;
    }

    public List<String[]> getDirectCitySelection() {
        return directCitySelection;
    }

    public void setDirectCitySelection(List<String[]> directCitySelection) {
        this.directCitySelection = directCitySelection;
    }

    public List<Long[]> getIndustrySelection() {
        return industrySelection;
    }

    public void setIndustrySelection(List<Long[]> industrySelection) {
        this.industrySelection = industrySelection;
    }

    public String getTargetAppPackage() {
        return targetAppPackage;
    }

    public void setTargetAppPackage(String targetAppPackage) {
        this.targetAppPackage = targetAppPackage;
    }

    public String getTargetDeviceModel() {
        return targetDeviceModel;
    }

    public void setTargetDeviceModel(String targetDeviceModel) {
        this.targetDeviceModel = targetDeviceModel;
    }

    public List<Integer> getTargetDeviceBrandCheck() {
        return targetDeviceBrandCheck;
    }

    public void setTargetDeviceBrandCheck(List<Integer> targetDeviceBrandCheck) {
        this.targetDeviceBrandCheck = targetDeviceBrandCheck;
    }

    public List<Integer> getTargetNetworkCheck() {
        return targetNetworkCheck;
    }

    public void setTargetNetworkCheck(List<Integer> targetNetworkCheck) {
        this.targetNetworkCheck = targetNetworkCheck;
    }

    public List<Integer> getTargetOperatorCheck() {
        return targetOperatorCheck;
    }

    public void setTargetOperatorCheck(List<Integer> targetOperatorCheck) {
        this.targetOperatorCheck = targetOperatorCheck;
    }

    public Integer getTargetOsVersionType() {
        return targetOsVersionType;
    }

    public void setTargetOsVersionType(Integer targetOsVersionType) {
        this.targetOsVersionType = targetOsVersionType;
    }

    public String getTargetOsVersion() {
        return targetOsVersion;
    }

    public void setTargetOsVersion(String targetOsVersion) {
        this.targetOsVersion = targetOsVersion;
    }

    public Boolean getFilterOnOff() {
        return filterOnOff;
    }

    public void setFilterOnOff(Boolean filterOnOff) {
        this.filterOnOff = filterOnOff;
    }

    public List<String[]> getFilterCitySelection() {
        return filterCitySelection;
    }

    public void setFilterCitySelection(List<String[]> filterCitySelection) {
        this.filterCitySelection = filterCitySelection;
    }

    public List<Long[]> getFilterIndustrySelection() {
        return filterIndustrySelection;
    }

    public void setFilterIndustrySelection(List<Long[]> filterIndustrySelection) {
        this.filterIndustrySelection = filterIndustrySelection;
    }

    public String getFilterAppPackage() {
        return filterAppPackage;
    }

    public void setFilterAppPackage(String filterAppPackage) {
        this.filterAppPackage = filterAppPackage;
    }

    public String getFilterDeviceModel() {
        return filterDeviceModel;
    }

    public void setFilterDeviceModel(String filterDeviceModel) {
        this.filterDeviceModel = filterDeviceModel;
    }

    public String getFilterUrlDomain() {
        return filterUrlDomain;
    }

    public void setFilterUrlDomain(String filterUrlDomain) {
        this.filterUrlDomain = filterUrlDomain;
    }

    public List<Integer> getFilterDeviceBrandCheck() {
        return filterDeviceBrandCheck;
    }

    public void setFilterDeviceBrandCheck(List<Integer> filterDeviceBrandCheck) {
        this.filterDeviceBrandCheck = filterDeviceBrandCheck;
    }

    public List<Integer> getFilterNetworkCheck() {
        return filterNetworkCheck;
    }

    public void setFilterNetworkCheck(List<Integer> filterNetworkCheck) {
        this.filterNetworkCheck = filterNetworkCheck;
    }

    public List<Integer> getFilterOperatorCheck() {
        return filterOperatorCheck;
    }

    public void setFilterOperatorCheck(List<Integer> filterOperatorCheck) {
        this.filterOperatorCheck = filterOperatorCheck;
    }

    public Integer getFilterEmptyDevice() {
        return filterEmptyDevice;
    }

    public void setFilterEmptyDevice(Integer filterEmptyDevice) {
        this.filterEmptyDevice = filterEmptyDevice;
    }

    public Integer getFilterInvalidDevice() {
        return filterInvalidDevice;
    }

    public void setFilterInvalidDevice(Integer filterInvalidDevice) {
        this.filterInvalidDevice = filterInvalidDevice;
    }

    public Integer getFilterForeignIp() {
        return filterForeignIp;
    }

    public void setFilterForeignIp(Integer filterForeignIp) {
        this.filterForeignIp = filterForeignIp;
    }

    public List<Integer> getAppendEventTypes() {
        return appendEventTypes;
    }

    public void setAppendEventTypes(List<Integer> appendEventTypes) {
        this.appendEventTypes = appendEventTypes;
    }

    public String getFilterInstalledAppPackage() {
        return filterInstalledAppPackage;
    }

    public void setFilterInstalledAppPackage(String filterInstalledAppPackage) {
        this.filterInstalledAppPackage = filterInstalledAppPackage;
    }

    public String getTargetInstalledAppPackage() {
        return targetInstalledAppPackage;
    }

    public void setTargetInstalledAppPackage(String targetInstalledAppPackage) {
        this.targetInstalledAppPackage = targetInstalledAppPackage;
    }

    public Long getTargetPackId() {
        return targetPackId;
    }

    public void setTargetPackId(Long targetPackId) {
        this.targetPackId = targetPackId;
    }

    public Long getFilterPackId() {
        return filterPackId;
    }

    public void setFilterPackId(Long filterPackId) {
        this.filterPackId = filterPackId;
    }

    public Integer getFilterRepeatEvent() {
        return filterRepeatEvent;
    }

    public void setFilterRepeatEvent(Integer filterRepeatEvent) {
        this.filterRepeatEvent = filterRepeatEvent;
    }
}
