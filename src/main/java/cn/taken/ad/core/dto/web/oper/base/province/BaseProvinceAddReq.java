package cn.taken.ad.core.dto.web.oper.base.province;

import javax.validation.constraints.NotNull;

public class BaseProvinceAddReq {

    @NotNull(message = "名称未输入")
    private String name;
    @NotNull(message = "代码未输入")
    private String code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
