package cn.taken.ad.core.dto.web.oper.tool.conversion;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class ConversionToolsModifyReq extends ConversionToolsBaseOperReq{

    @NotNull(message = "记录未选择")
    private Long id;

    private String convertUrl;

    private String iosConvertUrl;

    @NotNull(message = "是否需要重新转链")
    private Boolean isReconversion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConvertUrl() {
        return convertUrl;
    }

    public void setConvertUrl(String convertUrl) {
        this.convertUrl = convertUrl;
    }

    public Boolean getIsReconversion() {
        return isReconversion;
    }

    public void setIsReconversion(Boolean reconversion) {
        this.isReconversion = reconversion;
    }

    public String getIosConvertUrl() {
        return iosConvertUrl;
    }

    public void setIosConvertUrl(String iosConvertUrl) {
        this.iosConvertUrl = iosConvertUrl;
    }
}
