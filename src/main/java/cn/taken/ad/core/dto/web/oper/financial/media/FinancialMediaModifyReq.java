package cn.taken.ad.core.dto.web.oper.financial.media;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Valid
public class FinancialMediaModifyReq {

    @NotNull(message = "记录未选择")
    private Long id;
    @NotNull(message = "填充量未填写")
    private Long realParticipatingTotal;
    @NotNull(message = "曝光量未填写")
    private Long realExposureTotal;
    @NotNull(message = "点击量未填写")
    private Long realClickTotal;
    @NotNull(message = "消耗金额未填写")
    private BigDecimal realAmount;
    private Double settlementRatio;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRealParticipatingTotal() {
        return realParticipatingTotal;
    }

    public void setRealParticipatingTotal(Long realParticipatingTotal) {
        this.realParticipatingTotal = realParticipatingTotal;
    }

    public Long getRealExposureTotal() {
        return realExposureTotal;
    }

    public void setRealExposureTotal(Long realExposureTotal) {
        this.realExposureTotal = realExposureTotal;
    }

    public Long getRealClickTotal() {
        return realClickTotal;
    }

    public void setRealClickTotal(Long realClickTotal) {
        this.realClickTotal = realClickTotal;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public Double getSettlementRatio() {
        return settlementRatio;
    }

    public void setSettlementRatio(Double settlementRatio) {
        this.settlementRatio = settlementRatio;
    }
}
