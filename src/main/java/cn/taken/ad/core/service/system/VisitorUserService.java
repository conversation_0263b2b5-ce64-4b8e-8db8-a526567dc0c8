package cn.taken.ad.core.service.system;


import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dto.web.oper.system.visitor.VisitorUserAddReq;
import cn.taken.ad.core.dto.web.oper.system.visitor.VisitorUserDto;
import cn.taken.ad.core.dto.web.oper.system.visitor.VisitorUserModifyReq;
import cn.taken.ad.core.dto.web.oper.system.visitor.VisitorUserPageReq;
import cn.taken.ad.core.pojo.system.VisitorUser;

/**
 * <AUTHOR>
 */
public interface VisitorUserService {

    Page<VisitorUserDto> findPage(VisitorUserPageReq req);

    SuperResult<String> addUser(VisitorUserAddReq req, Long operatorId);

    SuperResult<VisitorUserDto> findUserInfo(Long userId);

    SuperResult<String> modifyUser(VisitorUserModifyReq req);

    VisitorUser findById(Long userId);

    SuperResult<String> deleteByUserId(Long userId);

    SuperResult<String> onUser(Long userId);

    SuperResult<String> offUser(Long userId);

    SuperResult<String> resetPassword(Long userId);

    VisitorUser findByUserName(String username);

    SuperResult<String> modifyPassword(Long userId, String newPass);


}
