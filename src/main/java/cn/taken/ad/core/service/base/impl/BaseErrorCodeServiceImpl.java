package cn.taken.ad.core.service.base.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dao.advertiser.AdvertiserDao;
import cn.taken.ad.core.dao.base.BaseErrorCodeDao;
import cn.taken.ad.core.dao.media.MediaDao;
import cn.taken.ad.core.dto.web.oper.base.error.BaseErrorCodeDto;
import cn.taken.ad.core.dto.web.oper.base.error.BaseErrorCodeInfo;
import cn.taken.ad.core.dto.web.oper.base.error.BaseErrorCodeModify;
import cn.taken.ad.core.dto.web.oper.base.error.BaseErrorCodePageReq;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import cn.taken.ad.core.pojo.base.BaseErrorCode;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.service.base.BaseErrorCodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class BaseErrorCodeServiceImpl implements BaseErrorCodeService {
    @Autowired
    private BaseErrorCodeDao baseErrorCodeDao;
    @Autowired
    private AdvertiserDao advertiserDao;
    @Autowired
    private MediaDao mediaDao;

    @Override
    public Page<BaseErrorCodeInfo> findPage(BaseErrorCodePageReq req) {
        return baseErrorCodeDao.findPage(req);
    }

    @Override
    public BaseErrorCodeInfo findInfoById(Long id) {
        BaseErrorCodeInfo info = new BaseErrorCodeInfo();
        BaseErrorCode errorCode = findById(id);
        BeanUtils.copyProperties(errorCode, info);
        if (errorCode.getType() == 1) {
            Media media = mediaDao.findById(errorCode.getMediaAdvId());
            if (media != null) {
                info.setName(media.getName());
            }
        } else {
            Advertiser adv = advertiserDao.findById(errorCode.getMediaAdvId());
            if (adv != null) {
                info.setName(adv.getName());
            }
        }
        return info;
    }

    @Override
    public BaseErrorCode findById(Long id) {
        return baseErrorCodeDao.findById(id);
    }

    @Override
    public SuperResult<String> add(BaseErrorCodeDto dto, Long userId) {
        BaseErrorCode old = baseErrorCodeDao.findByCode(dto.getMediaAdvId(), dto.getCode(), dto.getType());
        if (old != null) {
            String name = dto.getType() == 1 ? "媒体" : "预算";
            return SuperResult.badResult(name + "错误码【" + dto.getCode() + "】已存在");
        }
        BaseErrorCode errorCode = new BaseErrorCode();
        errorCode.setCode(dto.getCode());
        errorCode.setRemark(dto.getRemark());
        errorCode.setIsDelete(0);
        errorCode.setType(dto.getType());
        errorCode.setMediaAdvId(dto.getMediaAdvId());
        errorCode.setCreateTime(new Date());
        errorCode.setOperUserId(userId);
        baseErrorCodeDao.save(errorCode);
        return SuperResult.rightResult(errorCode.getId().toString());
    }

    @Override
    public SuperResult<String> modify(BaseErrorCodeModify modify, Long userId) {
        BaseErrorCode errorCode = findById(modify.getId());
        if (errorCode == null) {
            return SuperResult.badResult("主数据不存在");
        }
        if (!errorCode.getCode().equals(modify.getCode())) {
            BaseErrorCode old = baseErrorCodeDao.findByCode(modify.getMediaAdvId(), modify.getCode(), modify.getType());
            if (old != null) {
                String name = modify.getType() == 1 ? "媒体" : "预算";
                return SuperResult.badResult(name + "错误码【" + modify.getCode() + "】已存在");
            }
        }
        errorCode.setOperUserId(userId);
        errorCode.setCode(modify.getCode());
        errorCode.setType(modify.getType());
        errorCode.setRemark(modify.getRemark());
        errorCode.setMediaAdvId(modify.getMediaAdvId());
        baseErrorCodeDao.update(errorCode);
        return SuperResult.rightResult();
    }

    @Override
    public void removeByIds(List<Long> ids) {

    }
}
