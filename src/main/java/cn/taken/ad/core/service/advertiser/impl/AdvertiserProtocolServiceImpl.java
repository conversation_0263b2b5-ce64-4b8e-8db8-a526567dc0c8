package cn.taken.ad.core.service.advertiser.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dao.advertiser.AdvertiserProtocolDao;
import cn.taken.ad.core.dto.web.oper.advertiser.protocol.AdvertiserProtocolAddReq;
import cn.taken.ad.core.dto.web.oper.advertiser.protocol.AdvertiserProtocolListReq;
import cn.taken.ad.core.dto.web.oper.advertiser.protocol.AdvertiserProtocolModifyReq;
import cn.taken.ad.core.dto.web.oper.advertiser.protocol.AdvertiserProtocolPageReq;
import cn.taken.ad.core.pojo.advertiser.AdvertiserProtocol;
import cn.taken.ad.core.service.advertiser.AdvertiserProtocolService;
import cn.taken.ad.utils.check.ParamChecker;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class AdvertiserProtocolServiceImpl implements AdvertiserProtocolService {

    @Resource
    private AdvertiserProtocolDao advertiserProtocolDao;

    @Override
    public Page<AdvertiserProtocol> findPage(AdvertiserProtocolPageReq req) {
        return advertiserProtocolDao.findPage(req);
    }

    @Override
    public AdvertiserProtocol findById(Long id) {
        return advertiserProtocolDao.findById(id);
    }

    @Override
    public List<AdvertiserProtocol> findList(AdvertiserProtocolListReq req) {
        return advertiserProtocolDao.findList(req);
    }

    @Override
    public List<AdvertiserProtocol> findByLastUpdateTime(Date lastTime, int start, int limit) {
        return advertiserProtocolDao.findByLastUpdateTime(lastTime, start, limit);
    }

    @Override
    public SuperResult<String> save(AdvertiserProtocolAddReq req, Long operatorId) {
        SuperResult<String> result = ParamChecker.checkProtocolParams(req.getParam(), req.getAppParam(), req.getTagParam());
        if (!result.getSuccess()) {
            return result;
        }
        if (advertiserProtocolDao.findByCode(req.getCode()) != null) {
            return SuperResult.badResult("存在重复的CODE");
        }
        AdvertiserProtocol protocol = new AdvertiserProtocol();
        protocol.setCode(req.getCode());
        protocol.setName(req.getName());
        protocol.setAppParam(req.getAppParam());
        protocol.setTagParam(req.getTagParam());
        protocol.setParam(req.getParam());
        protocol.setRemark(req.getRemark());
        advertiserProtocolDao.save(protocol);
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> update(AdvertiserProtocolModifyReq req) {
        AdvertiserProtocol protocol = advertiserProtocolDao.findById(req.getId());
        if (protocol == null) {
            return SuperResult.badResult("数据未找到");
        }
        SuperResult<String> result = ParamChecker.checkProtocolParams(req.getParam(), req.getAppParam(), req.getTagParam());
        if (!result.getSuccess()) {
            return result;
        }
        AdvertiserProtocol has = advertiserProtocolDao.findByCodeAndNotId(protocol.getCode(), protocol.getId());
        if (has != null) {
            return SuperResult.badResult("存在重复的CODE");
        }
        protocol.setName(req.getName());
        protocol.setAppParam(req.getAppParam());
        protocol.setTagParam(req.getTagParam());
        protocol.setParam(req.getParam());
        protocol.setRemark(req.getRemark());
        advertiserProtocolDao.update(protocol);
        return SuperResult.rightResult();
    }

}
