package cn.taken.ad.core.service.strategy;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dto.web.oper.strategy.main.*;
import cn.taken.ad.core.pojo.strategy.Strategy;

import java.util.Date;
import java.util.List;


public interface StrategyService {

    Page<StrategyPageResp> findPage(StrategyPageReq req);

    SuperResult<String> save(StrategyAddReq req, Long operatorId);

    SuperResult<String>  update(StrategyModifyReq req);

    void updateState(Integer state, Long id);

    List<Strategy> findByLastUpDateTime(Date lastTime, int start, int limit);

    StrategyInfo findInfoById(Long id);

    StrategyInfo findInfoByTagId(Long id);
}
