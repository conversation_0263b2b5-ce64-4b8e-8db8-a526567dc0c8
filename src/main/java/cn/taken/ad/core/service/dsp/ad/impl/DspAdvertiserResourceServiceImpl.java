package cn.taken.ad.core.service.dsp.ad.impl;

import cn.taken.ad.component.utils.result.Result;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dao.dsp.ad.DspAdvertiserResourceDao;
import cn.taken.ad.core.pojo.dsp.ad.DspAdvertiserResource;
import cn.taken.ad.core.service.dsp.ad.DspAdvertiserResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class DspAdvertiserResourceServiceImpl implements DspAdvertiserResourceService {
    @Autowired
    private DspAdvertiserResourceDao dspAdvertiserResourceDao;

    @Override
    public List<DspAdvertiserResource> findByLastUpdateTime(Date lastTime, int start, int limit) {
        return dspAdvertiserResourceDao.findByLastUpdateTime(lastTime, start, limit);
    }

    @Override
    public SuperResult<String> save(DspAdvertiserResource resource) {
        dspAdvertiserResourceDao.save(resource);
        return SuperResult.rightResult();
    }

    @Override
    public Result remove(Long id) {
        DspAdvertiserResource resource = dspAdvertiserResourceDao.findById(id);
        if (resource != null) {
            resource.setIsDelete(1);
            dspAdvertiserResourceDao.update(resource);
            return Result.right();
        }
        return Result.bad("资源不存在");
    }

    @Override
    public void delete(Long id) {
        dspAdvertiserResourceDao.deleteById(id);
    }

    @Override
    public void saveAll(List<DspAdvertiserResource> resources) {
        dspAdvertiserResourceDao.saveBatch(resources);
    }
}
