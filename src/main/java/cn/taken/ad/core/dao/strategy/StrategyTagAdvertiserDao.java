package cn.taken.ad.core.dao.strategy;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.core.dto.web.oper.strategy.tag.StrategyTagAdvertiserInfo;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;

import java.util.List;

public interface StrategyTagAdvertiserDao extends BaseSuperDao<StrategyTagAdvertiser> {

    List<StrategyTagAdvertiserInfo> findByStrategyId(Long id);

    List<StrategyTagAdvertiser> findStrategyTags(Long id);

    StrategyTagAdvertiser findByStrategyAndAdvTag(Long strategyId, Long advTagId);

    StrategyTagAdvertiserInfo findInfoById(Long id);
}
