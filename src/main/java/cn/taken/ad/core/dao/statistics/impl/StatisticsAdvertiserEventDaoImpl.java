package cn.taken.ad.core.dao.statistics.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsAdvertiserEventDao;
import cn.taken.ad.core.pojo.statistics.StatisticsAdvertiserEvent;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class StatisticsAdvertiserEventDaoImpl extends BasePojoSuperDaoImpl<StatisticsAdvertiserEvent> implements StatisticsAdvertiserEventDao {

    @Override
    public List<StatisticsAdvertiserEvent> findStatisticsAdvertiserEvent(String beginTime, String endTime, StatisticsType statisticsType) {
        String sql = "select " +
                " advertiser_id,advertiser_app_id,advertiser_tag_id,event_type, " +
                " sum(total) as total,sum(repeat_total) as repeat_total " +
                " from statistics_advertiser_event where statistics_type = ? and statistics_time >= ? and statistics_time <= ? " +
                " group by advertiser_id,advertiser_app_id,advertiser_tag_id,event_type ";
        List<Object> params = new ArrayList<>();
        params.add(statisticsType.getCode());
        params.add(beginTime);
        params.add(endTime);
        return this.findObjectListByClass(StatisticsAdvertiserEvent.class, sql, params.toArray());
    }

    @Override
    public void deleteByTime(StatisticsType type, String day) {
        String hql = "delete from StatisticsAdvertiserEvent where statisticsType=:statisticsType and statisticsTime=:statisticsTime";
        Map<String, Object> params = new HashMap<>();
        params.put("statisticsType", type.getCode());
        params.put("statisticsTime", day);
        this.execByHql(hql, params);
    }

    @Override
    public List<StatisticsAdvertiserEvent> findShowList(StatisticsType statisticsType, String begin, String end, Long advertiserId, Long appId, Long tagId) {
        String sql = "select " +
                "statistics_time, " +
                "event_type, " +
                "sum(total) total " +
                "from statistics_advertiser_event " +
                "where statistics_type = ? and statistics_time >= ? and statistics_time <= ? ";
        List<Object> params = new LinkedList<>();
        params.add(statisticsType.getCode());
        params.add(begin);
        params.add(end);
        if (advertiserId != null) {
            sql += " and advertiser_id = ? ";
            params.add(advertiserId);
        }
        if (appId != null) {
            sql += " and advertiser_app_id = ? ";
            params.add(appId);
        }
        if (tagId != null) {
            sql += " and advertiser_tag_id = ? ";
            params.add(tagId);
        }
        sql += "group by statistics_time , event_type ";
        return this.findObjectListByClass(StatisticsAdvertiserEvent.class, sql, params.toArray());
    }
}
