package cn.taken.ad.core.dao.advertiser;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.advertiser.app.AdvertiserAppInfo;
import cn.taken.ad.core.dto.web.oper.advertiser.app.AdvertiserAppListReq;
import cn.taken.ad.core.dto.web.oper.advertiser.app.AdvertiserAppPageReq;
import cn.taken.ad.core.pojo.advertiser.AdvertiserApp;

import java.util.List;

public interface AdvertiserAppDao extends BaseSuperDao<AdvertiserApp> {

    Page<AdvertiserAppInfo> findPage(AdvertiserAppPageReq pageReq);

    AdvertiserApp findByCodeAndAdvertiserId(String code, Long advId);

    AdvertiserAppInfo findInfoById(Long id);

    List<AdvertiserApp> findList(AdvertiserAppListReq req);

    List<AdvertiserApp> findCollabList(AdvertiserAppListReq req, CollabUserDataAuthDto collabUserDataAuthDto);
}
