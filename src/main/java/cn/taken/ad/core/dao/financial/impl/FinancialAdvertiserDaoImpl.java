package cn.taken.ad.core.dao.financial.impl;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.financial.FinancialAdvertiserDao;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserInfo;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserInfoReq;
import cn.taken.ad.core.dto.web.oper.financial.advertiser.FinancialAdvertiserPageReq;
import cn.taken.ad.core.dto.web.oper.financial.merge.FinancialMediaAdvTaskReq;
import cn.taken.ad.core.pojo.financial.FinancialAdvertiser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

@Repository
public class FinancialAdvertiserDaoImpl extends BasePojoSuperDaoImpl<FinancialAdvertiser> implements FinancialAdvertiserDao {

    @Override
    public Page<FinancialAdvertiserInfo> page(FinancialAdvertiserPageReq req) {
        String sql = buildSelectSql();
        List<Object> params = new LinkedList<>();
        if (StringUtils.isNotBlank(req.getBeginTime())) {
            sql += " and a.report_time >= ? ";
            params.add(req.getBeginTime());
        }
        if (StringUtils.isNotBlank(req.getEndTime())) {
            sql += " and a.report_time <= ? ";
            params.add(req.getEndTime());
        }
        if (null != req.getAdvertiserId()) {
            sql += " and a.advertiser_id = ? ";
            params.add(req.getAdvertiserId());
        }
        if (null != req.getAdvertiserAppId()) {
            sql += " and a.advertiser_app_id = ? ";
            params.add(req.getAdvertiserAppId());
        }
        if (null != req.getAdvertiserTagId()) {
            sql += " and a.advertiser_tag_id = ? ";
            params.add(req.getAdvertiserTagId());
        }
        if (null != req.getState()) {
            sql += " and a.state = ? ";
            params.add(req.getState());
        }
        if (null != req.getSettlementType()) {
            sql += " and d.settlement_type = ? ";
            params.add(req.getSettlementType());
        }
        if (req.getRealData() != null) {
            if (req.getRealData() == 1) { //0
                sql += "and (a.real_exposure_total=0 or a.real_click_total=0 or a.real_amount=0 or a.real_exposure_total is null or a.real_click_total is null or a.real_amount is null) ";
            }else if (req.getRealData() == 2) {
                sql += "and (a.real_exposure_total > 0 and a.real_click_total  > 0 and a.real_amount > 0 and a.real_exposure_total is not null and a.real_click_total is not null and a.real_amount is not null) ";
            }
        }
        sql += " order by a.report_time desc ";
        return this.findObjectPageByClassInMysql(FinancialAdvertiserInfo.class, sql, req.getStart(), req.getLimit(), params.toArray());
    }

    @Override
    public List<FinancialAdvertiserInfo> findExport(String beginTime, String endTime, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Integer state, Integer settlementType) {
        String sql = buildSelectSql();
        List<Object> params = new ArrayList<>();
        if (StringUtils.isNotBlank(beginTime)) {
            sql += " and a.report_time >= ? ";
            params.add(beginTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            sql += " and a.report_time <= ? ";
            params.add(endTime);
        }
        if (null != advertiserId) {
            sql += " and a.advertiser_id = ? ";
            params.add(advertiserId);
        }
        if (null != advertiserAppId) {
            sql += " and a.advertiser_app_id = ? ";
            params.add(advertiserAppId);
        }
        if (null != advertiserTagId) {
            sql += " and a.advertiser_tag_id = ? ";
            params.add(advertiserTagId);
        }
        if (null != state) {
            sql += " and a.state = ? ";
            params.add(state);
        }
        if (null != settlementType) {
            sql += " and d.settlement_type = ? ";
            params.add(settlementType);
        }
        sql += " order by a.report_time desc ";
        return this.findObjectListByClass(FinancialAdvertiserInfo.class, sql, params.toArray());
    }

    private String buildSelectSql() {
        return "select a.*," +
                " b.name as advertiser_name,  " +
                " c.name as advertiser_app_name,  " +
                " c.code as advertiser_app_code,  " +
                " c.type as advertiser_app_type,  " +
                " d.name as advertiser_tag_name,  " +
                " d.code as advertiser_tag_code,  " +
                " d.type as advertiser_tag_type,  " +
                " d.settlement_type as advertiser_tag_settlement_type,  " +
                " smr.req_total  " +
                " from financial_advertiser a " +
                " left join advertiser b on b.id = a.advertiser_id " +
                " left join advertiser_app c on c.id = a.advertiser_app_id " +
                " left join advertiser_tag d on d.id = a.advertiser_tag_id " +
                " left join statistics_advertiser_request smr on smr.advertiser_id = a.advertiser_id and smr.advertiser_app_id = a.advertiser_app_id and smr.advertiser_tag_id = a.advertiser_tag_id and smr.statistics_time = a.report_time and smr.statistics_type='"+ StatisticsType.DAY.getCode() +"'  " +
                " where 1=1 ";
    }

    @Override
    public void updateBatchForImport(List<FinancialAdvertiser> list) {
        String sql = "update financial_advertiser " +
                " set real_participating_total = ?, " +
                " real_exposure_total = ?, " +
                " real_click_total = ?, " +
                " real_amount=? " +
                " where report_time = ? and advertiser_id=? and advertiser_app_id=? and advertiser_tag_id=? ";
        List<Object[]> param = new ArrayList<>();
        for (FinancialAdvertiser financialAdv : list) {
            param.add(new Object[]{financialAdv.getRealParticipatingTotal(), financialAdv.getRealExposureTotal(), financialAdv.getRealClickTotal(), financialAdv.getRealAmount(), financialAdv.getReportTime(), financialAdv.getAdvertiserId(), financialAdv.getAdvertiserAppId(), financialAdv.getAdvertiserTagId()});
        }
        this.getJdbcTemplate().batchUpdate(sql, param);
    }

    @Override
    public List<FinancialAdvertiserInfo> findForTask(FinancialMediaAdvTaskReq taskReq) {
        String sql = buildSelectSql();
        List<Object> params = new ArrayList<>();
        if (StringUtils.isNotBlank(taskReq.getReportTime())) {
            sql += " and a.report_time >= ? ";
            params.add(taskReq.getReportTime());
        }
        if (StringUtils.isNotBlank(taskReq.getReportTime())) {
            sql += " and a.report_time <= ? ";
            params.add(taskReq.getReportTime());
        }
        if (taskReq.getAdvId() != null) {
            sql += " and a.advertiser_id = ? ";
            params.add(taskReq.getAdvId());
        }
        if (taskReq.getAdvAppId() != null) {
            sql += " and a.advertiser_app_id = ? ";
            params.add(taskReq.getAdvAppId());
        }
        if (taskReq.getAdvTagId() != null) {
            sql += " and a.advertiser_tag_id = ? ";
            params.add(taskReq.getAdvTagId());
        }
        return this.findObjectListByClass(FinancialAdvertiserInfo.class, sql, params.toArray());
    }

    @Override
    public void updateStateByIds(List<Long> ids, int type) {
        String sql = "update financial_advertiser set state = ? where id in( " + StringUtils.join(ids, ",") + " )";
        this.getJdbcTemplate().update(sql, type);
    }

    @Override
    public void updateForTask(List<FinancialAdvertiser> list) {
        String sql = "update financial_advertiser " +
                " set revenue_amount = ?, state = ? " +
                " where report_time = ? and advertiser_id=? and advertiser_app_id=? and advertiser_tag_id=? ";
        List<Object[]> param = new ArrayList<>();
        for (FinancialAdvertiser financialAdv : list) {
            param.add(new Object[]{financialAdv.getRevenueAmount(), financialAdv.getState(), financialAdv.getReportTime(), financialAdv.getAdvertiserId(), financialAdv.getAdvertiserAppId(), financialAdv.getAdvertiserTagId()});
        }
        this.getJdbcTemplate().batchUpdate(sql, param);
    }

    @Override
    public List<FinancialAdvertiserInfo> findByIdsForTask(String reportTime, Set<Long> advertiserIds, Set<Long> advertiserAppIds, Set<Long> advertiserTagIds) {
        String sql = buildSelectSql();
        List<Object> param = new ArrayList<>();
        if (StringUtils.isNotBlank(reportTime)) {
            sql += " and a.report_time >= ? ";
            param.add(reportTime);
        }
        if (null != advertiserIds && !advertiserIds.isEmpty()) {
            sql += " and a.advertiser_id in (" + StringUtils.join(advertiserIds, ",") + ")";
        }
        if (null != advertiserAppIds && !advertiserAppIds.isEmpty()) {
            sql += " and a.advertiser_app_id in (" + StringUtils.join(advertiserAppIds, ",") + ")";
        }
        if (null != advertiserTagIds && !advertiserTagIds.isEmpty()) {
            sql += " and a.advertiser_tag_id in (" + StringUtils.join(advertiserTagIds, ",") + ")";
        }
        return this.findObjectListByClass(FinancialAdvertiserInfo.class, sql, param.toArray());
    }

    @Override
    public FinancialAdvertiserInfo findInfo(FinancialAdvertiserInfoReq req) {
        String sql = buildSelectSql();
        sql += " and a.id = ? ";
        return this.findObjectUnique(FinancialAdvertiserInfo.class, sql, req.getId());
    }
}
