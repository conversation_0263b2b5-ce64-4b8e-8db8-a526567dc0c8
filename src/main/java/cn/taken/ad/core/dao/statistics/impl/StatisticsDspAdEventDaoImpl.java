package cn.taken.ad.core.dao.statistics.impl;

import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsDspAdEventDao;
import cn.taken.ad.core.pojo.statistics.StatisticsDspAdEvent;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class StatisticsDspAdEventDaoImpl extends BasePojoSuperDaoImpl<StatisticsDspAdEvent> implements StatisticsDspAdEventDao {

    @Override
    public List<StatisticsDspAdEvent> findStatisticsAdvertiserEvent(String beginTime, String endTime, StatisticsType statisticsType) {
        String sql = "select " +
                " ad_id,event_type, " +
                " sum(total) as total,sum(repeat_total) as repeat_total " +
                " from statistics_dsp_adv_ad_event where statistics_type = ? and statistics_time >= ? and statistics_time <= ? " +
                " group by event_type,ad_id ";
        List<Object> params = new ArrayList<>();
        params.add(statisticsType.getCode());
        params.add(beginTime);
        params.add(endTime);
        return this.findObjectListByClass(StatisticsDspAdEvent.class, sql, params.toArray());
    }

    @Override
    public void deleteByTime(StatisticsType type, String day) {
        String hql = "delete from StatisticsDspAdEvent where statisticsType=:statisticsType and statisticsTime=:statisticsTime";
        Map<String, Object> params = new HashMap<>();
        params.put("statisticsType", type.getCode());
        params.put("statisticsTime", day);
        this.execByHql(hql, params);
    }

    @Override
    public List<StatisticsDspAdEvent> findShowList(StatisticsType statisticsType, String begin, String end, String adId) {
        String sql = "select " +
                "statistics_time, " +
                "event_type, " +
                "sum(total) total " +
                "from statistics_dsp_ad_event " +
                "where statistics_type = ? and statistics_time >= ? and statistics_time <= ? ";
        List<Object> params = new LinkedList<>();
        params.add(statisticsType.getCode());
        params.add(begin);
        params.add(end);
        if (StringUtils.isNotBlank(adId)) {
            sql += " and ad_id = ? ";
            params.add(adId);
        }
        sql += "group by statistics_time , event_type ";
        return this.findObjectListByClass(StatisticsDspAdEvent.class, sql, params.toArray());
    }

    @Override
    public Integer findCountByEvent(String adId, Integer trackType, String beginTime, String endTime) {
        String sql = "select sum(total) from statistics_dsp_ad_event where ad_id = ? and statistics_type = ? and event_type = ? ";
        List<Object> params = new ArrayList<>();
        params.add(adId);
        params.add(StatisticsType.MINUTE.getCode());
        params.add(trackType);
        if (StringUtils.isNotEmpty(beginTime)) {
            sql += " and statistics_time >= ? ";
            params.add(beginTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sql += " and statistics_time <= ? ";
            params.add(endTime);
        }
        return jdbcTemplate.queryForObject(sql,params.toArray(), Integer.class);
    }
}
