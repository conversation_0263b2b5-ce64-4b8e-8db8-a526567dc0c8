package cn.taken.ad.core.dao.system.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.core.dao.system.OperAuthResourceDao;
import cn.taken.ad.core.pojo.system.OperAuthResource;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class OperAuthResourceDaoImpl extends BasePojoSuperDaoImpl<OperAuthResource> implements OperAuthResourceDao {

    @Override
    public List<OperAuthResource> getUserResources(Long userId) {
        String hql = "select re " +
                "from OperAuthUserRoleAssign ura , OperAuthRole r , OperAuthRoleResourceAssign rra , OperAuthResource re  " +
                "where ura.userId = :userId and r.id = ura.roleId and rra.roleId = r.id and re.id = rra.resourceId ";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        return this.getListResult(OperAuthResource.class, hql, params);
    }

    @Override
    public List<OperAuthResource> findByRoleId(Long roleId) {
        String hql = "select re " +
                "from OperAuthRoleResourceAssign rra , OperAuthResource re  " +
                "where rra.roleId = :roleId and re.id = rra.resourceId ";
        Map<String, Object> params = new HashMap<>();
        params.put("roleId", roleId);
        return this.getListResult(OperAuthResource.class, hql, params);
    }

}
