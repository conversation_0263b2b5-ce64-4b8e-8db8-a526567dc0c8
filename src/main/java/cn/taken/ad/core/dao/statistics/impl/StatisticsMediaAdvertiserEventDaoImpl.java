package cn.taken.ad.core.dao.statistics.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dao.statistics.StatisticsMediaAdvertiserEventDao;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaAdvertiserEvent;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository
public class StatisticsMediaAdvertiserEventDaoImpl extends BasePojoSuperDaoImpl<StatisticsMediaAdvertiserEvent> implements StatisticsMediaAdvertiserEventDao {

    @Override
    public List<StatisticsMediaAdvertiserEvent> findStatistics(String beginTime, String endTime, StatisticsType type) {
        String sql = "SELECT " +
                "sum(total) AS total," +
                "sum(repeat_total) AS repeat_total," +
                "strategy_id," +
                "strategy_tag_adv_id," +
                "advertiser_id," +
                "advertiser_app_id," +
                "advertiser_tag_id," +
                "media_id," +
                "media_app_id," +
                "media_tag_id," +
                "event_type " +
                "FROM statistics_media_advertiser_event " +
                "WHERE statistics_time>=? AND statistics_time<=? AND statistics_type=? " +
                "GROUP BY strategy_id,strategy_tag_adv_id,advertiser_id,advertiser_app_id,advertiser_tag_id,media_id,media_app_id,media_tag_id,event_type";
        List<Object> params = new LinkedList<>();
        params.add(beginTime);
        params.add(endTime);
        params.add(type.getCode());
        return this.findObjectListByClass(StatisticsMediaAdvertiserEvent.class, sql, params.toArray());
    }

    @Override
    public void deleteByTime(StatisticsType type, String time) {
        String hql = "delete from StatisticsMediaAdvertiserEvent where statisticsType=:statisticsType and statisticsTime=:statisticsTime";
        Map<String, Object> params = new HashMap<>();
        params.put("statisticsType", type.getCode());
        params.put("statisticsTime", time);
        this.execByHql(hql, params);
    }

    @Override
    public List<StatisticsMediaAdvertiserEvent> findShowList(StatisticsType statisticsType, String begin, String end, Long id, Long strategyId) {
        String sql = "select " +
                "statistics_time, " +
                "event_type, " +
                "sum(total) total " +
                "from statistics_media_advertiser_event " +
                "where statistics_type = ? and statistics_time >= ? and statistics_time <= ? ";
        List<Object> params = new LinkedList<>();
        params.add(statisticsType.getCode());
        params.add(begin);
        params.add(end);
        if (id != null) {
            sql += " and strategy_tag_adv_id = ? ";
            params.add(id);
        }
        if (strategyId != null) {
            sql += " and strategy_id = ? ";
            params.add(strategyId);
        }
        sql += "group by statistics_time , event_type ";
        return this.findObjectListByClass(StatisticsMediaAdvertiserEvent.class, sql, params.toArray());
    }
}
