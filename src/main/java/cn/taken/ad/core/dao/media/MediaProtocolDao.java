package cn.taken.ad.core.dao.media;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.core.dto.web.oper.media.protocol.MediaProtocolListReq;
import cn.taken.ad.core.dto.web.oper.media.protocol.MediaProtocolPageReq;
import cn.taken.ad.core.pojo.media.MediaProtocol;

import java.util.List;

public interface MediaProtocolDao extends BaseSuperDao<MediaProtocol> {

    Page<MediaProtocol> findPage(MediaProtocolPageReq req);

    List<MediaProtocol> findList(MediaProtocolListReq req);

    MediaProtocol findByCode(String code);

    MediaProtocol findByCodeAndNotId(String code, Long id);
}
