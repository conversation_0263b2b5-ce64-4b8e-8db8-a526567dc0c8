package cn.taken.ad.core.dao.system;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.core.pojo.system.OperAuthResource;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OperAuthResourceDao extends BaseSuperDao<OperAuthResource> {

    /**
     * 获取用户所有的资源
     *
     * @param userId 用户ID
     * @return 资源
     */
    List<OperAuthResource> getUserResources(Long userId);

    List<OperAuthResource> findByRoleId(Long roleId);
}
