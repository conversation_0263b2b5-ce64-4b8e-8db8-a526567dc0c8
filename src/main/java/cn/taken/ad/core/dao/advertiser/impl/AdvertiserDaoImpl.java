package cn.taken.ad.core.dao.advertiser.impl;


import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.core.dao.advertiser.AdvertiserDao;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.advertiser.main.AdvertiserInfo;
import cn.taken.ad.core.dto.web.oper.advertiser.main.AdvertiserListReq;
import cn.taken.ad.core.dto.web.oper.advertiser.main.AdvertiserPageReq;
import cn.taken.ad.core.pojo.advertiser.Advertiser;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 */
@Repository
public class AdvertiserDaoImpl extends BasePojoSuperDaoImpl<Advertiser> implements AdvertiserDao {

    @Override
    public Page<AdvertiserInfo> findPage(AdvertiserPageReq req) {
        String sql = "select " +
                "a.*," +
                "p.name as protocol_name, p.code as protocol_code " +
                "from advertiser a " +
                "left join  advertiser_protocol p on p.id = a.protocol_id " +
                "where 1=1  ";
        List<Object> objects = new ArrayList<>();
        if (StringUtils.isNotEmpty(req.getName())) {
            sql += " and a.name like ? ";
            objects.add("%" + req.getName() + "%");
        }
        if (req.getProtocolId() != null) {
            sql += " and a.protocol_id = ? ";
            objects.add(req.getProtocolId());
        }
        sql += " order by a.id desc ";
        return this.findObjectPageByClassInMysql(AdvertiserInfo.class, sql, req.getStart(), req.getLimit(), objects.toArray());
    }

    @Override
    public List<Advertiser> findList(AdvertiserListReq req) {
        String sql = "select * from advertiser a where 1=1 ";
        List<Object> objects = new ArrayList<>();
        if (StringUtils.isNotEmpty(req.getName())) {
            sql += " and a.name like ? ";
            objects.add("%" + req.getName() + "%");
        }
        sql += " order by id desc limit 50";
        return this.findObjectListByClass(Advertiser.class, sql, objects.toArray());
    }

    @Override
    public Advertiser findByName(String name) {
        return this.findByProperty("name", name);
    }

    @Override
    public AdvertiserInfo findInfoById(Long id) {
        String sql = "select " +
                "a.*," +
                "p.name as protocol_name, p.code as protocol_code , p.param as protocol_param " +
                "from advertiser a " +
                "left join  advertiser_protocol p on p.id = a.protocol_id " +
                "where a.id = ?  ";
        return this.findObjectUnique(AdvertiserInfo.class, sql, id);
    }

    @Override
    public List<Advertiser> findInfoByIds(Long[] advertiserIds) {
        String hql = FIND_ALL_HQL + " where  id in (:ids) ";
        Map<String, Object> param = new HashMap<>();
        param.put("ids", advertiserIds);
        return this.getListResult(Advertiser.class, hql, param);
    }

    @Override
    public List<Advertiser> findCollabList(AdvertiserListReq req, CollabUserDataAuthDto collabUserDataAuthDto) {
        String sql = "select a.* from " + collabUserDataAuthDto.buildSelectAdvertiserSql() + " a where 1=1 ";
        List<Object> objects = new ArrayList<>();
        if (StringUtils.isNotEmpty(req.getName())) {
            sql += " and a.name like ? ";
            objects.add("%" + req.getName() + "%");
        }
        sql += " order by id desc limit 50";
        return this.findObjectListByClass(Advertiser.class, sql, objects.toArray());
    }

}
