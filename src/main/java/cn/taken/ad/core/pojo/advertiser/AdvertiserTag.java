package cn.taken.ad.core.pojo.advertiser;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "advertiser_tag")
public class AdvertiserTag implements Serializable {

    private static final long serialVersionUID = -8833062619923128393L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "advertiser_id")
    private Long advertiserId;
    @Column(name = "advertiser_app_id")
    private Long advertiserAppId;
    @Column(name = "name")
    private String name;
    @Column(name = "code")
    private String code;
    @Column(name = "type")
    private Integer type;
    @Column(name = "timeout")
    private Integer timeout;
    @Column(name = "pny_param")
    private String pnyParam;
    /**
     * 结算方式，1:竞价，2:分成
     */
    @Column(name = "settlement_type")
    private Integer settlementType;
    /**
     * rtb 价格处理方式
     * 1:涨幅
     * 2:固价
     */
    @Column(name = "bid_price_type")
    private Integer bidPriceType;
    @Column(name = "bid_rises_ratio")
    private Integer bidRisesRatio;
    @Column(name = "sharing_price_type")
    private Integer sharingPriceType;
    @Column(name = "fixed_price")
    private Double fixedPrice;

    @Column(name = "limit_rule_open")
    private Boolean limitRuleOpen;
    @Column(name = "limit_type")
    private Integer limitType;
    @Column(name = "quota")
    private Long quota;
    @Column(name = "start_time")
    private Integer startTime;
    @Column(name = "end_time")
    private Integer endTime;

    @Column(name = "filter_exposure_num")
    private Long filterExposureNum;
    @Column(name = "filter_click_num")
    private Long filterClickNum;
    @Column(name = "filter_device_req_num")
    private Long filterDeviceReqNum;
    @Column(name = "filter_device_exposure_num")
    private Long filterDeviceExposureNum;

    @Column(name = "direct_on_off")
    private Boolean directOnOff;
    @Column(name = "target_area")
    private String targetArea; // directCitySelection json
    @Column(name = "target_app_industry")
    private String targetAppIndustry; // industrySelection json
    @Column(name = "target_app_package")
    private String targetAppPackage;
    @Column(name = "target_device_model")
    private String targetDeviceModel;
    @Column(name = "target_device_brand")
    private String targetDeviceBrand; // targetDeviceBrandCheck json
    @Column(name = "target_network")
    private String targetNetwork; // targetNetworkCheck json
    @Column(name = "target_operator")
    private String targetOperator; // targetOperatorCheck json
    @Column(name = "target_os_version_type")
    private Integer targetOsVersionType;
    @Column(name = "target_os_version")
    private String targetOsVersion;

    @Column(name = "filter_on_off")
    private Boolean filterOnOff;
    @Column(name = "filter_area")
    private String filterArea; // filterCitySelection json
    @Column(name = "filter_app_industry")
    private String filterAppIndustry; // filterIndustrySelection json
    @Column(name = "filter_app_package")
    private String filterAppPackage;
    @Column(name = "filter_device_model")
    private String filterDeviceModel;
    @Column(name = "filter_url_domain")
    private String filterUrlDomain;
    @Column(name = "filter_device_brand")
    private String filterDeviceBrand; // filterDeviceBrandCheck json
    @Column(name = "filter_network")
    private String filterNetwork; // filterNetworkCheck json
    @Column(name = "filter_operator")
    private String filterOperator; // filterOperatorCheck json
    @Column(name = "filter_empty_device")
    private Integer filterEmptyDevice;
    @Column(name = "filter_invalid_device")
    private Integer filterInvalidDevice;
    @Column(name = "filter_foreign_ip")
    private Integer filterForeignIp;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "operator_id")
    private Long operatorId;

    @Column(name = "add_event_types")
    private String addEventTypes; // appendEventType json

    @Column(name = "dsp_type")
    private Integer dspType;
    @Column(name = "account_id")
    private Long accountId;

    @Column(name = "filter_installed_app_package")
    private String filterInstalledAppPackage;

    @Column(name = "target_installed_app_package")
    private String targetInstalledAppPackage;

    /**
     * 定向人群包
     */
    @Column(name = "target_pack_id")
    private Long targetPackId;

    /**
     * 过滤人群包
     */
    @Column(name = "filter_pack_id")
    private Long filterPackId;

    @Column(name = "filter_repeat_event")
    private Integer filterRepeatEvent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }

    public Integer getBidPriceType() {
        return bidPriceType;
    }

    public void setBidPriceType(Integer bidPriceType) {
        this.bidPriceType = bidPriceType;
    }

    public Integer getBidRisesRatio() {
        return bidRisesRatio;
    }

    public void setBidRisesRatio(Integer bidRisesRatio) {
        this.bidRisesRatio = bidRisesRatio;
    }

    public Integer getSharingPriceType() {
        return sharingPriceType;
    }

    public void setSharingPriceType(Integer sharingPriceType) {
        this.sharingPriceType = sharingPriceType;
    }

    public Double getFixedPrice() {
        return fixedPrice;
    }

    public void setFixedPrice(Double fixedPrice) {
        this.fixedPrice = fixedPrice;
    }

    public Boolean getLimitRuleOpen() {
        return limitRuleOpen;
    }

    public void setLimitRuleOpen(Boolean limitRuleOpen) {
        this.limitRuleOpen = limitRuleOpen;
    }

    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }

    public Long getQuota() {
        return quota;
    }

    public void setQuota(Long quota) {
        this.quota = quota;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Long getFilterExposureNum() {
        return filterExposureNum;
    }

    public void setFilterExposureNum(Long filterExposureNum) {
        this.filterExposureNum = filterExposureNum;
    }

    public Long getFilterClickNum() {
        return filterClickNum;
    }

    public void setFilterClickNum(Long filterClickNum) {
        this.filterClickNum = filterClickNum;
    }

    public Long getFilterDeviceReqNum() {
        return filterDeviceReqNum;
    }

    public void setFilterDeviceReqNum(Long filterDeviceReqNum) {
        this.filterDeviceReqNum = filterDeviceReqNum;
    }

    public Long getFilterDeviceExposureNum() {
        return filterDeviceExposureNum;
    }

    public void setFilterDeviceExposureNum(Long filterDeviceExposureNum) {
        this.filterDeviceExposureNum = filterDeviceExposureNum;
    }

    public Boolean getDirectOnOff() {
        return directOnOff;
    }

    public void setDirectOnOff(Boolean directOnOff) {
        this.directOnOff = directOnOff;
    }

    public String getTargetArea() {
        return targetArea;
    }

    public void setTargetArea(String targetArea) {
        this.targetArea = targetArea;
    }

    public String getTargetAppIndustry() {
        return targetAppIndustry;
    }

    public void setTargetAppIndustry(String targetAppIndustry) {
        this.targetAppIndustry = targetAppIndustry;
    }

    public String getTargetAppPackage() {
        return targetAppPackage;
    }

    public void setTargetAppPackage(String targetAppPackage) {
        this.targetAppPackage = targetAppPackage;
    }

    public String getTargetDeviceModel() {
        return targetDeviceModel;
    }

    public void setTargetDeviceModel(String targetDeviceModel) {
        this.targetDeviceModel = targetDeviceModel;
    }

    public String getTargetDeviceBrand() {
        return targetDeviceBrand;
    }

    public void setTargetDeviceBrand(String targetDeviceBrand) {
        this.targetDeviceBrand = targetDeviceBrand;
    }

    public String getTargetNetwork() {
        return targetNetwork;
    }

    public void setTargetNetwork(String targetNetwork) {
        this.targetNetwork = targetNetwork;
    }

    public String getTargetOperator() {
        return targetOperator;
    }

    public void setTargetOperator(String targetOperator) {
        this.targetOperator = targetOperator;
    }

    public Integer getTargetOsVersionType() {
        return targetOsVersionType;
    }

    public void setTargetOsVersionType(Integer targetOsVersionType) {
        this.targetOsVersionType = targetOsVersionType;
    }

    public String getTargetOsVersion() {
        return targetOsVersion;
    }

    public void setTargetOsVersion(String targetOsVersion) {
        this.targetOsVersion = targetOsVersion;
    }

    public Boolean getFilterOnOff() {
        return filterOnOff;
    }

    public void setFilterOnOff(Boolean filterOnOff) {
        this.filterOnOff = filterOnOff;
    }

    public String getFilterArea() {
        return filterArea;
    }

    public void setFilterArea(String filterArea) {
        this.filterArea = filterArea;
    }

    public String getFilterAppIndustry() {
        return filterAppIndustry;
    }

    public void setFilterAppIndustry(String filterAppIndustry) {
        this.filterAppIndustry = filterAppIndustry;
    }

    public String getFilterAppPackage() {
        return filterAppPackage;
    }

    public void setFilterAppPackage(String filterAppPackage) {
        this.filterAppPackage = filterAppPackage;
    }

    public String getFilterDeviceModel() {
        return filterDeviceModel;
    }

    public void setFilterDeviceModel(String filterDeviceModel) {
        this.filterDeviceModel = filterDeviceModel;
    }

    public String getFilterUrlDomain() {
        return filterUrlDomain;
    }

    public void setFilterUrlDomain(String filterUrlDomain) {
        this.filterUrlDomain = filterUrlDomain;
    }

    public String getFilterDeviceBrand() {
        return filterDeviceBrand;
    }

    public void setFilterDeviceBrand(String filterDeviceBrand) {
        this.filterDeviceBrand = filterDeviceBrand;
    }

    public String getFilterNetwork() {
        return filterNetwork;
    }

    public void setFilterNetwork(String filterNetwork) {
        this.filterNetwork = filterNetwork;
    }

    public String getFilterOperator() {
        return filterOperator;
    }

    public void setFilterOperator(String filterOperator) {
        this.filterOperator = filterOperator;
    }

    public Integer getFilterEmptyDevice() {
        return filterEmptyDevice;
    }

    public void setFilterEmptyDevice(Integer filterEmptyDevice) {
        this.filterEmptyDevice = filterEmptyDevice;
    }

    public Integer getFilterInvalidDevice() {
        return filterInvalidDevice;
    }

    public void setFilterInvalidDevice(Integer filterInvalidDevice) {
        this.filterInvalidDevice = filterInvalidDevice;
    }

    public Integer getFilterForeignIp() {
        return filterForeignIp;
    }

    public void setFilterForeignIp(Integer filterForeignIp) {
        this.filterForeignIp = filterForeignIp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getAddEventTypes() {
        return addEventTypes;
    }

    public void setAddEventTypes(String addEventTypes) {
        this.addEventTypes = addEventTypes;
    }

    public String getFilterInstalledAppPackage() {
        return filterInstalledAppPackage;
    }

    public void setFilterInstalledAppPackage(String filterInstalledAppPackage) {
        this.filterInstalledAppPackage = filterInstalledAppPackage;
    }

    public String getTargetInstalledAppPackage() {
        return targetInstalledAppPackage;
    }

    public void setTargetInstalledAppPackage(String targetInstalledAppPackage) {
        this.targetInstalledAppPackage = targetInstalledAppPackage;
    }

    public Integer getDspType() {
        return dspType;
    }

    public void setDspType(Integer dspType) {
        this.dspType = dspType;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getTargetPackId() {
        return targetPackId;
    }

    public void setTargetPackId(Long targetPackId) {
        this.targetPackId = targetPackId;
    }

    public Long getFilterPackId() {
        return filterPackId;
    }

    public void setFilterPackId(Long filterPackId) {
        this.filterPackId = filterPackId;
    }

    public Integer getFilterRepeatEvent() {
        return filterRepeatEvent;
    }

    public void setFilterRepeatEvent(Integer filterRepeatEvent) {
        this.filterRepeatEvent = filterRepeatEvent;
    }
}
