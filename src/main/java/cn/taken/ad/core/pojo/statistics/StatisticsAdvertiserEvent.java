package cn.taken.ad.core.pojo.statistics;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "statistics_advertiser_event")
public class StatisticsAdvertiserEvent implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "statistics_type")
    private String statisticsType;

    @Column(name = "statistics_time")
    private String statisticsTime;

    @Column(name = "advertiser_id")
    private Long advertiserId;

    @Column(name = "advertiser_app_id")
    private Long advertiserAppId;

    @Column(name = "advertiser_tag_id")
    private Long advertiserTagId;

    @Column(name = "event_type")
    private Integer eventType;

    @Column(name = "total")
    private Long total;

    @Column(name = "repeat_total")
    private Long repeatTotal;


    public StatisticsAdvertiserEvent() {

    }

    public StatisticsAdvertiserEvent(String statisticsType, String statisticsTime, Long advertiserId, Long advertiserAppId, Long advertiserTagId, Integer eventType) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.advertiserId = advertiserId;
        this.advertiserAppId = advertiserAppId;
        this.advertiserTagId = advertiserTagId;
        this.eventType = eventType;
        this.total = 0L;
        this.repeatTotal = 0L;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Long getRepeatTotal() {
        return repeatTotal;
    }

    public void setRepeatTotal(Long repeatTotal) {
        this.repeatTotal = repeatTotal;
    }
}
