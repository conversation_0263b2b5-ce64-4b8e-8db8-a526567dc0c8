package cn.taken.ad.core.pojo.statistics;


import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "statistics_dsp_ad_request")
public class StatisticsDspAdRequest implements Serializable {

    private static final long serialVersionUID = 2805058413488156899L;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "statistics_type")
    private String statisticsType;

    @Column(name = "statistics_time")
    private String statisticsTime;

    @Column(name = "ad_id")
    private String adId;

    /***
     * 请求总数
     */
    @Column(name = "req_total")
    private Long reqTotal;

    /**
     * 请求成功量
     */
    @Column(name = "req_success_total")
    private Long reqSuccessTotal;

    /**
     * 请求失败总数
     */
    @Column(name = "req_fail_total")
    private Long reqFailTotal;

    /**
     * 响应失败量
     */
    @Column(name = "resp_fail_total")
    private Long respFailTotal;

    /**
     * 请求超时量
     */
    @Column(name = "req_timeout_total")
    private Long reqTimeoutTotal;

    /***
     * 填充量
     */
    @Column(name = "participating_total")
    private Long participatingTotal;

    /***
     * 竟胜数量
     */
    @Column(name = "win_total")
    private Long winTotal;

    /***
     * 消耗金额: 元
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /***
     * 最高请求耗时
     */
    @Column(name = "max_time")
    private Long maxTime;

    /***
     * 平均请求耗时
     */
    @Column(name = "avg_time")
    private Long avgTime;

    /***
     * 最小请求耗时
     */
    @Column(name = "min_time")
    private Long minTime;

    /***
     * 总耗时
     */
    @Column(name = "use_time_total")
    private Long useTimeTotal;

    public StatisticsDspAdRequest() {
    }

    public StatisticsDspAdRequest(String statisticsType, String statisticsTime, String adId) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.adId = adId;
        this.reqTotal = 0L;
        this.reqSuccessTotal = 0L;
        this.reqFailTotal = 0L;
        this.respFailTotal = 0L;
        this.reqTimeoutTotal = 0L;
        this.participatingTotal = 0L;
        this.winTotal = 0L;
        this.amount = BigDecimal.ZERO;
        this.maxTime = 0L;
        this.avgTime = 0L;
        this.minTime = 0L;
        this.useTimeTotal = 0L;
    }

    public StatisticsDspAdRequest(String statisticsType, String statisticsTime) {
        this.statisticsType = statisticsType;
        this.statisticsTime = statisticsTime;
        this.reqTotal = 0L;
        this.reqSuccessTotal = 0L;
        this.reqFailTotal = 0L;
        this.respFailTotal = 0L;
        this.reqTimeoutTotal = 0L;
        this.participatingTotal = 0L;
        this.winTotal = 0L;
        this.amount = BigDecimal.ZERO;
        this.maxTime = 0L;
        this.avgTime = 0L;
        this.minTime = 0L;
        this.useTimeTotal = 0L;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(String statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public Long getReqTotal() {
        return reqTotal;
    }

    public void setReqTotal(Long reqTotal) {
        this.reqTotal = reqTotal;
    }

    public Long getReqSuccessTotal() {
        return reqSuccessTotal;
    }

    public void setReqSuccessTotal(Long reqSuccessTotal) {
        this.reqSuccessTotal = reqSuccessTotal;
    }

    public Long getReqFailTotal() {
        return reqFailTotal;
    }

    public void setReqFailTotal(Long reqFailTotal) {
        this.reqFailTotal = reqFailTotal;
    }

    public Long getReqTimeoutTotal() {
        return reqTimeoutTotal;
    }

    public void setReqTimeoutTotal(Long reqTimeoutTotal) {
        this.reqTimeoutTotal = reqTimeoutTotal;
    }

    public Long getParticipatingTotal() {
        return participatingTotal;
    }

    public void setParticipatingTotal(Long participatingTotal) {
        this.participatingTotal = participatingTotal;
    }

    public Long getMinTime() {
        return minTime;
    }

    public void setMinTime(Long minTime) {
        this.minTime = minTime;
    }

    public Long getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(Long maxTime) {
        this.maxTime = maxTime;
    }

    public Long getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(Long avgTime) {
        this.avgTime = avgTime;
    }

    public Long getUseTimeTotal() {
        return useTimeTotal;
    }

    public void setUseTimeTotal(Long useTimeTotal) {
        this.useTimeTotal = useTimeTotal;
    }

    public Long getRespFailTotal() {
        return respFailTotal;
    }

    public void setRespFailTotal(Long respFailTotal) {
        this.respFailTotal = respFailTotal;
    }

    public Long getWinTotal() {
        return winTotal;
    }

    public void setWinTotal(Long winTotal) {
        this.winTotal = winTotal;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }
}
