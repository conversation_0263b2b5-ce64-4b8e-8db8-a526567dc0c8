package cn.taken.ad.core.pojo.media;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
@Entity
@Table(name="media_tag")
public class MediaTag implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /***
     * 名称
     */
    @Column(name = "name")
    private String name;
    /***
     * 广告位ID
     */
    @Column(name = "code")
    private String code;
    /***
     * 所属媒体
     */
    @Column(name = "media_id")
    private Long mediaId;
    /***
     * 所属媒体APPID
     */
    @Column(name = "media_app_id")
    private Long mediaAppId;
    /***
     * 类型 1:信息流,2:激励视频,3:全屏视频,4:开屏,5:Draw视频,6:插屏视频,7:BANNER,8:插屏,9:原⽣,10:应用推荐,11:锁屏,12:文字链,13:普通视频,14:咨询,15:通知,16:视频内容,99:其他
     */
    @Column(name = "type")
    private Integer type;
    /***
     *超时时间，单位毫秒
     */
    @Column(name = "timeout")
    private Integer timeout;
    /***
     *广告位宽度
     */
    @Column(name = "width")
    private Integer width;

    /***
     *广广告位高度
     */
    @Column(name = "height")
    private  Integer height;
    /***
     *结算方式，1:竞价，2:分成
     */
    @Column(name = "bid_type")
    private Integer bidType;

    /***
     *扩展参数
     */
    @Column(name = "pny_param")
    private String pnyParam;

    /***
     * 操作人
     */
    @Column(name = "operator_id")
    private Long operatorId;
    @Column(name = "media_operator_id")
    private Long mediaOperatorId;
    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "need_https")
    private Integer needHttps;
    /**
     * 素材是否需要为https
     */
    @Column(name = "material_https")
    private Boolean materialHttps;

    @Column(name = "flow_type")
    private Integer flowType;

    /**
     * 分成结算比例
     */
    @Column(name = "settlement_ratio")
    private Double settlementRatio;
    /**
     * 是否开启过滤
     */
    @Column(name = "filter_on_off")
    private Boolean filterOnOff;
    /**
     * 过滤域名
     */
    @Column(name = "filter_url_domain")
    private String filterUrlDomain;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getPnyParam() {
        return pnyParam;
    }

    public void setPnyParam(String pnyParam) {
        this.pnyParam = pnyParam;
    }

    public Integer getNeedHttps() {
        return needHttps;
    }

    public void setNeedHttps(Integer needHttps) {
        this.needHttps = needHttps;
    }

    public Boolean getMaterialHttps() {
        return materialHttps;
    }

    public void setMaterialHttps(Boolean materialHttps) {
        this.materialHttps = materialHttps;
    }

    public Integer getFlowType() {
        return flowType;
    }

    public void setFlowType(Integer flowType) {
        this.flowType = flowType;
    }


    public Long getMediaOperatorId() {
        return mediaOperatorId;
    }

    public void setMediaOperatorId(Long mediaOperatorId) {
        this.mediaOperatorId = mediaOperatorId;
    }

    public Double getSettlementRatio() {
        return settlementRatio;
    }

    public void setSettlementRatio(Double settlementRatio) {
        this.settlementRatio = settlementRatio;
    }

    public Boolean getFilterOnOff() {
        return filterOnOff;
    }

    public void setFilterOnOff(Boolean filterOnOff) {
        this.filterOnOff = filterOnOff;
    }

    public String getFilterUrlDomain() {
        return filterUrlDomain;
    }

    public void setFilterUrlDomain(String filterUrlDomain) {
        this.filterUrlDomain = filterUrlDomain;
    }
}
