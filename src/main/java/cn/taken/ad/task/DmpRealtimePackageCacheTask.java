package cn.taken.ad.task;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.configuration.dmp.DmpMonitor;
import cn.taken.ad.core.dto.business.dmp.DmpPackChunk;
import cn.taken.ad.utils.DmpDeviceCacheUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DmpRealtimePackageCacheTask {

    @Resource
    private RedisClient redis;
    @Resource
    private DmpMonitor dmpMonitor;

    @SuperScheduled(dynamicDelay = true, fixedConcurrent = 2)
    public long execute() {
        DmpPackChunk chunk = dmpMonitor.pollRedis();
        if (chunk == null) {
            return 1000L;
        }
        DmpDeviceCacheUtils.setPackDevice(redis, chunk.getCacheType(), chunk.getCacheDay(), chunk.getId(), chunk.getDeviceIds().toArray(new String[0]));
        return 0L;
    }

}
