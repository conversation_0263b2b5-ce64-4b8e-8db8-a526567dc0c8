package cn.taken.ad.task.statistics;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsDspAdvAdEvent;
import cn.taken.ad.core.service.statistics.StatisticsDspAdvAdEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class StatisticsDspAdvAdEventTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Resource
    private StatisticsDspAdvAdEventService statisticsDspAdvAdEventService;

    @SuperScheduled(cron = "10 */1 * * * ?", only = true)
    public void minute() {
        Date statisticsDate = new Date(System.currentTimeMillis() - (60L * 1000L));
        this.statisticsMinute(statisticsDate);
    }

    @SuperScheduled(cron = "40 0/5 * * * ?", only = true)
    public void hour() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.MINUTE) < 9) {
            // 统计上一个小时的数据
            c.add(Calendar.HOUR_OF_DAY, -1);
            this.statisticsHour(c.getTime());
        }
        //当前小时
        this.statisticsHour(statisticsDate);
    }

    @SuperScheduled(cron = "50 0/10 * * * ?", only = true)
    public void day() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) < 20) {
            // 统计昨天的数据
            c.add(Calendar.DAY_OF_MONTH, -1);
            this.statisticsDay(c.getTime());
        }
        //当前天的
        this.statisticsDay(statisticsDate);
    }

    private void statisticsMinute(Date statisticsDate) {
        String minute = DateUtils.toString(statisticsDate, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsDspAdvAdEvent> eventMap = new HashMap<>();
        for (; ; ) {
            List<StatisticsDspAdvAdEvent> eventList = redis.rpop(BaseRedisKeys.QUEUE_STATISTICS_DSP_ADV_AD_EVENT, 200, StatisticsDspAdvAdEvent.class);
            if (CollectionUtils.isEmpty(eventList)) {
                break;
            }
            for (StatisticsDspAdvAdEvent event : eventList) {
                String key = event.getAdId() + "&&" + event.getAdvertiserId() + "&&" + event.getAdvertiserAppId() + "&&" + event.getAdvertiserTagId() + "&&" + event.getEventType();
                StatisticsDspAdvAdEvent tmp = eventMap.computeIfAbsent(key, k -> new StatisticsDspAdvAdEvent(StatisticsType.MINUTE.getCode(), minute, event.getAdId(), event.getAdvertiserId(), event.getAdvertiserAppId(), event.getAdvertiserTagId(), event.getEventType()));
                tmp.setTotal(tmp.getTotal() + event.getTotal());
                if (event.getRepeatTotal() == null) {
                    event.setRepeatTotal(0L);
                }
                tmp.setRepeatTotal(tmp.getRepeatTotal() + event.getRepeatTotal());
            }
        }
        if (!eventMap.isEmpty()) {
            statisticsDspAdvAdEventService.saveList(new ArrayList<>(eventMap.values()));
        }
        log.info("statistics event minute success {}:{}", minute, eventMap.size());
    }

    private void statisticsDay(Date statisticsDate) {
        String day = DateUtils.toString(statisticsDate, StatisticsType.DAY.getFormat());
        String beginTime = day + "00";
        String endTime = day + "23";

        List<StatisticsDspAdvAdEvent> eventList = statisticsDspAdvAdEventService.findStatisticsAdvertiserEvent(beginTime, endTime, StatisticsType.HOUR);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.DAY.getCode());
                event.setStatisticsTime(day);
            });
            statisticsDspAdvAdEventService.deleteByTime(StatisticsType.DAY, day);
            statisticsDspAdvAdEventService.saveList(eventList);
        }
        log.info("statistics day success {}", day);
    }

    private void statisticsHour(Date statisticsDate) {
        String hour = DateUtils.toString(statisticsDate, StatisticsType.HOUR.getFormat());
        String beginTime = hour + "00";
        String endTime = hour + "59";

        List<StatisticsDspAdvAdEvent> eventList = statisticsDspAdvAdEventService.findStatisticsAdvertiserEvent(beginTime, endTime, StatisticsType.MINUTE);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.HOUR.getCode());
                event.setStatisticsTime(hour);
            });
            statisticsDspAdvAdEventService.deleteByTime(StatisticsType.HOUR, hour);
            statisticsDspAdvAdEventService.saveList(eventList);
        }
        log.info("statistics hour success {}", hour);
    }
}
