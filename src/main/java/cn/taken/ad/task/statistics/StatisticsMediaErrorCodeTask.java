package cn.taken.ad.task.statistics;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaErrorCode;
import cn.taken.ad.core.service.statistics.StatisticsMediaErrorCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class StatisticsMediaErrorCodeTask {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Resource
    private StatisticsMediaErrorCodeService statisticsMediaErrorCodeService;

    @SuperScheduled(cron = "20 */1 * * * ?", only = true)
    public void statistics() {
        Date statisticsDate = new Date(System.currentTimeMillis() - (60L * 1000L));
        this.statisticsMinute(statisticsDate);
    }

    @SuperScheduled(cron = "40 0/5 * * * ?", only = true)
    public void hour() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.MINUTE) < 9) {
            // 统计上一个小时的数据
            c.add(Calendar.HOUR_OF_DAY, -1);
            this.statisticsHour(c.getTime());
        }
        //当前小时
        this.statisticsHour(statisticsDate);
    }

    @SuperScheduled(cron = "50 0/10 * * * ?", only = true)
    public void day() {
        Date statisticsDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(statisticsDate);
        if (c.get(Calendar.HOUR_OF_DAY) == 0 && c.get(Calendar.MINUTE) < 20) {
            // 统计昨天的数据
            c.add(Calendar.DAY_OF_MONTH, -1);
            this.statisticsDay(c.getTime());
        }
        //当前天的
        this.statisticsDay(statisticsDate);
    }

    private void statisticsDay(Date statisticsDate) {
        String day = DateUtils.toString(statisticsDate, StatisticsType.DAY.getFormat());
        String beginTime = day + "00";
        String endTime = day + "23";

        List<StatisticsMediaErrorCode> eventList = statisticsMediaErrorCodeService.findStatisticsMediaErrorCode(beginTime, endTime, StatisticsType.HOUR);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.DAY.getCode());
                event.setStatisticsTime(day);
            });
            statisticsMediaErrorCodeService.deleteByTime(StatisticsType.DAY, day);
            statisticsMediaErrorCodeService.saveList(eventList);
        }
        log.info("statistics day success {}", day);
    }

    private void statisticsHour(Date statisticsDate) {
        String hour = DateUtils.toString(statisticsDate, StatisticsType.HOUR.getFormat());
        String beginTime = hour + "00";
        String endTime = hour + "59";

        List<StatisticsMediaErrorCode> eventList = statisticsMediaErrorCodeService.findStatisticsMediaErrorCode(beginTime, endTime, StatisticsType.MINUTE);
        if (!eventList.isEmpty()) {
            eventList.forEach(event -> {
                event.setStatisticsType(StatisticsType.HOUR.getCode());
                event.setStatisticsTime(hour);
            });
            statisticsMediaErrorCodeService.deleteByTime(StatisticsType.HOUR, hour);
            statisticsMediaErrorCodeService.saveList(eventList);
        }
        log.info("statistics hour success {}", hour);
    }

    private void statisticsMinute(Date statisticsDate) {
        String minute = DateUtils.toString(statisticsDate, StatisticsType.MINUTE.getFormat());
        Map<String, StatisticsMediaErrorCode> errorCodeMap = new HashMap<>();
        for (; ; ) {
            List<StatisticsMediaErrorCode> eventList = redis.rpop(BaseRedisKeys.QUEUE_STATISTICS_MEDIA_ERROR_CODE, 100, StatisticsMediaErrorCode.class);
            if (CollectionUtils.isEmpty(eventList)) {
                break;
            }
            for (StatisticsMediaErrorCode errorCode : eventList) {
                String key = errorCode.getMediaId() + "&&" + errorCode.getMediaAppId() + "&&" + errorCode.getMediaTagId() + "&&" + errorCode.getCode() +"&&"+errorCode.getStrategyId();
                StatisticsMediaErrorCode tmp = errorCodeMap.computeIfAbsent(key, k -> new StatisticsMediaErrorCode(StatisticsType.MINUTE.getCode(), minute,errorCode.getMediaId(),errorCode.getMediaAppId(),errorCode.getMediaTagId(),errorCode.getCode(),errorCode.getStrategyId()));
                tmp.setTotal(tmp.getTotal() + errorCode.getTotal());
            }
        }
        if (!errorCodeMap.isEmpty()){
            statisticsMediaErrorCodeService.saveList(new ArrayList<>(errorCodeMap.values()));
        }
        log.info("statistics event minute success {}:{}", minute, errorCodeMap.size());
    }
}
