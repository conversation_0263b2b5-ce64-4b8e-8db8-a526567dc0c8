package cn.taken.ad;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * 启动入口类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "cn.taken.ad")
@EnableConfigurationProperties
@EnableWebMvc
@EnableScheduling
public class RtbApplication {

    public static void main(String[] args) {
        SpringApplication.run(RtbApplication.class, args);
    }

}
