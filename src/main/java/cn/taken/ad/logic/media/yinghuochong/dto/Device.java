package cn.taken.ad.logic.media.yinghuochong.dto;

import java.util.List;

public class Device {
    private String ua;
    private YingHuoChongCarrier carrier;
    private YingHuoChongNetworkType networkType;
    private YingHuoChongDeviceType deviceType;
    private YingHuoChongOS os;
    private String osv;
    private String make;
    private String model;
    private String brand;
    private Integer screenWidth;
    private Integer screenHeight;
    private Uid uid;
    private List<String> pkgList;

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public YingHuoChongCarrier getCarrier() {
        return carrier;
    }

    public void setCarrier(YingHuoChongCarrier carrier) {
        this.carrier = carrier;
    }

    public YingHuoChongNetworkType getNetworkType() {
        return networkType;
    }

    public void setNetworkType(YingHuoChongNetworkType networkType) {
        this.networkType = networkType;
    }

    public YingHuoChongDeviceType getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(YingHuoChongDeviceType deviceType) {
        this.deviceType = deviceType;
    }

    public YingHuoChongOS getOs() {
        return os;
    }

    public void setOs(YingHuoChongOS os) {
        this.os = os;
    }

    public String getOsv() {
        return osv;
    }

    public void setOsv(String osv) {
        this.osv = osv;
    }

    public String getMake() {
        return make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(Integer screenWidth) {
        this.screenWidth = screenWidth;
    }

    public Integer getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(Integer screenHeight) {
        this.screenHeight = screenHeight;
    }

    public Uid getUid() {
        return uid;
    }

    public void setUid(Uid uid) {
        this.uid = uid;
    }

    public List<String> getPkgList() {
        return pkgList;
    }

    public void setPkgList(List<String> pkgList) {
        this.pkgList = pkgList;
    }
}
