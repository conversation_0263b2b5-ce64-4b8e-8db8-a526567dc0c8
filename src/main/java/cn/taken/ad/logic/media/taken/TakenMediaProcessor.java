package cn.taken.ad.logic.media.taken;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.compress.GzipUtils;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.HexByte;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.logic.AbstractMediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.logic.media.taken.dto.*;
import cn.taken.ad.utils.web.HttpRequestUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component("TAKEN" + LogicSuffix.MEDIA_LOGIC_SUFFIX)
public class TakenMediaProcessor extends AbstractMediaProcessor {

    private static final Logger log = LoggerFactory.getLogger(TakenMediaProcessor.class);


    @Override
    public RtbRequestDto parseRtb(RtbMediaDto mediaDto, String rtbId) throws Throwable {
        byte[] data = HttpRequestUtils.readBytes();
        if (data == null || data.length == 0) {
            log.info("RtbId:{},Code:{},Data Is Empty",mediaDto.getMediaCode(), rtbId);
            return null;
        }
        data = GzipUtils.decompress(data);
        String json = new String(data, StandardCharsets.UTF_8);
        mediaDto.setReqObj(json);
        return convertRequest(json);
    }

    @Override
    public SuperResult<String> returnRtb(RtbMediaDto mediaDto) throws Throwable {
        // 转换错误码
        RtbResponseDto resp = mediaDto.getResponse();
        LogicState state =  LogicState.findByCode(resp.getCode());
        TakenMediaResponse mediaResponse = new TakenMediaResponse();
        mediaResponse.setCode(state.getRespCode());
        mediaResponse.setRespId(resp.getRespId());
        if (null != resp.getTags() && !resp.getTags().isEmpty()) {
            mediaResponse.setTags(new ArrayList<>());
            for (TagResponseDto tag : resp.getTags()) {
                TakenMediaResponseTag respTag = convertResponseTag(tag);
                if (null != respTag){
                    mediaResponse.getTags().add(convertResponseTag(tag));
                }
            }
        }
        HttpServletResponse response = HttpResponseUtils.getCurrentHttpResponse();
        response.addHeader("Content-Encoding", "gzip");
        String text = JsonHelper.toJsonStringWithoutNull(mediaResponse);
        mediaDto.setRespObj(text);
        byte[] bytes = GzipUtils.compress(text.getBytes(StandardCharsets.UTF_8));
        HttpResponseUtils.outputBytes(bytes);
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseBill(RtbBillDto bill, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseEvent(RtbEventDto event, Media media) {
        return SuperResult.rightResult();
    }
    /**
     * 价格解密
     */
    public SuperResult<Double> decryptPrice(String price, Media media) {
        if (StringUtils.isBlank(price)) {
            // 未替换 价格宏
            return SuperResult.badResult("price empty");
        }
        if (price.equals(MacroType.WINNER_PRICE.getCode()) || price.equals(MacroType.WIN_PRICE.getCode()) || price.equals(MacroType.LOSS_RP.getCode())){
            return SuperResult.badResult("price not replaced");
        }
        try {
            String priceSecretKey = media.getPriceKey();
            byte[] data = Aes.decrypt(HexByte.hex2Byte(price), priceSecretKey);
            return SuperResult.rightResult(Double.valueOf(new String(data)));
        } catch (Exception e) {
            return SuperResult.badResult("decrypt["+price+"] error:"+e.getMessage());
        }
    }

    private TakenMediaResponseTag convertResponseTag(TagResponseDto tag) {
        if (null == tag){
            return  null;
        }
        TakenMediaResponseTag responseTag = new TakenMediaResponseTag();
        responseTag.setTitle(tag.getTitle());
        responseTag.setSubTitle(tag.getSubTitle());
        responseTag.setDesc(tag.getDesc());
        responseTag.setActionType(tag.getActionType().getType());
        responseTag.setLogoUrl(tag.getLogoUrl());
        responseTag.setIconUrl(tag.getIconUrl());
        responseTag.setMaterialType(null != tag.getMaterialType() ? tag.getMaterialType().getType(): null);
        responseTag.setMaterialWidth(tag.getMaterialWidth());
        responseTag.setMaterialHeight(tag.getMaterialHeight());
        responseTag.setHtmlContent(tag.getHtmlContent());
        responseTag.setClickUrl(tag.getClickUrl());
        responseTag.setDeepLinkUrl(tag.getDeepLinkUrl());
        responseTag.setMarketUrl(tag.getMarketUrl());
        responseTag.setUniversalLink(tag.getUniversalLink());
        responseTag.setImgUrls(tag.getImgUrls());
        responseTag.setAppInfo(convertRespApp(tag.getAppInfo()));
        responseTag.setVideoInfo(convertRespVideo(tag.getVideoInfo()));
        responseTag.setPrice(tag.getRespMediaPrice());
        responseTag.setWinNoticeUrls(tag.getWinNoticeUrls());
        responseTag.setFailNoticeUrls(tag.getFailNoticeUrls());
        responseTag.setTracks(convertTrack(tag.getTracks()));
        responseTag.setClickAreaReportUrls(tag.getClickAreaReportUrls());
        responseTag.setMiniProgram(convertRespMiniProgram(tag.getMiniProgram()));
        return responseTag;
    }

    private TakenMediaResponseMiniProgram convertRespMiniProgram(ResponseMiniProgramDto miniProgram) {
        if (null == miniProgram) {
            return null;
        }
        TakenMediaResponseMiniProgram responseMiniProgram = new TakenMediaResponseMiniProgram();
        responseMiniProgram.setId(miniProgram.getId());
        responseMiniProgram.setName(miniProgram.getName());
        responseMiniProgram.setLogo(miniProgram.getLogo());
        responseMiniProgram.setUserName(miniProgram.getUserName());
        responseMiniProgram.setPath(miniProgram.getPath());
        responseMiniProgram.setExtData(miniProgram.getExtData());
        return responseMiniProgram;
    }

    private List<TakenMediaResponseTrack> convertTrack(List<ResponseTrackDto> tracks) {
        List<TakenMediaResponseTrack> list = new ArrayList<>();
        for (ResponseTrackDto track : tracks) {
            list.add(new TakenMediaResponseTrack(track.getTrackType(),track.getTrackUrls(),track.getMethod(),track.getContentType(),track.getContent()));
        }
        return list;
    }

    private TakenMediaResponseVideo convertRespVideo(ResponseVideoDto videoInfo) {
        if (null == videoInfo) {
            return null;
        }
        TakenMediaResponseVideo responseVideo = new TakenMediaResponseVideo();
        responseVideo.setVideoUrl(videoInfo.getVideoUrl());
        responseVideo.setDuration(videoInfo.getDuration());
        responseVideo.setVideoSize(videoInfo.getVideoSize());
        responseVideo.setVideoWidth(videoInfo.getVideoWidth());
        responseVideo.setVideoHeight(videoInfo.getVideoHeight());
        responseVideo.setResolution(videoInfo.getResolution());
        responseVideo.setOrientationType(videoInfo.getOrientationType());
        responseVideo.setCoverImgUrls(videoInfo.getCoverImgUrls());
        responseVideo.setCoverWidth(videoInfo.getCoverWidth());
        responseVideo.setCoverHeight(videoInfo.getCoverHeight());
        responseVideo.setButtonText(videoInfo.getButtonText());
        responseVideo.setEndButtonText(videoInfo.getEndButtonText());
        responseVideo.setEndImgUrls(videoInfo.getEndImgUrls());
        responseVideo.setEndHtml(videoInfo.getEndHtml());
        responseVideo.setAutoLanding(videoInfo.getAutoLanding());
        responseVideo.setPrefetch(videoInfo.getPrefetch());
        responseVideo.setSkipSeconds(videoInfo.getSkipSeconds());
        responseVideo.setClickAble(videoInfo.getClickAble());
        responseVideo.setEndIconUrl(videoInfo.getEndIconUrl());
        responseVideo.setEndTitle(videoInfo.getEndTitle());
        responseVideo.setEndDesc(videoInfo.getEndDesc());
        return responseVideo;

    }

    private TakenMediaResponseApp convertRespApp(ResponseAppDto appDto){
        if (null == appDto){
            return null;
        }
        TakenMediaResponseApp app = new TakenMediaResponseApp();
        app.setAppName(appDto.getAppName());
        app.setPackageName(appDto.getPackageName());
        app.setRating(appDto.getRating());
        app.setRatingCount(appDto.getRatingCount());
        app.setAppSize(appDto.getAppSize());
        app.setAppVersion(appDto.getAppVersion());
        app.setAppInfo(appDto.getAppInfo());
        app.setAppInfoUrl(appDto.getAppInfoUrl());
        app.setAppDeveloper(appDto.getAppDeveloper());
        app.setAppPrivacyUrl(appDto.getAppPrivacyUrl());
        app.setAppPermContent(appDto.getAppPermContent());
        app.setAppPermissionInfoUrl(appDto.getAppPermissionInfoUrl());
        app.setRecordNumber(appDto.getRecordNumber());
        app.setAppIconUrl(appDto.getAppIconUrl());
        return app;
    }


    private RtbRequestDto convertRequest(String json){
        if (StringUtils.isEmpty(json)){
            return null;
        }
        TakenMediaRequest mediaRequest = JsonHelper.fromJson(TakenMediaRequest.class,json);
        if (null == mediaRequest){
            return null;
        }
        RtbRequestDto rtbRequestDto = new RtbRequestDto();
        rtbRequestDto.setReqId(mediaRequest.getReqId());
        rtbRequestDto.setApp(convertRequestApp(mediaRequest.getApp()));
        rtbRequestDto.setTag(convertRequestTag(mediaRequest.getTag()));
        rtbRequestDto.setDevice(convertRequestDevice(mediaRequest.getDevice()));
        rtbRequestDto.setNetwork(convertRequestNet(mediaRequest.getNetwork()));
        rtbRequestDto.setGeo(convertRequestGeo(mediaRequest.getGeo()));

        rtbRequestDto.setUser(convertRequestUser(mediaRequest.getUser()));
        rtbRequestDto.setPageInfos(convertRequestPageInfo(mediaRequest.getPageInfos()));
        return rtbRequestDto;
    }

    private RequestDeviceDto convertRequestDevice(TakenMediaRequestDevice request){
        if (null == request){
            return null;
        }
        RequestDeviceDto dto = new RequestDeviceDto();
        dto.setSerialNO (request.getSerialNO());
        dto.setDeviceName (request.getDeviceName());
        dto.setDeviceNameMd5 (request.getDeviceNameMd5());
        dto.setOsType (OsType.findByType(request.getOsType()));
        dto.setDeviceType (DeviceType.findByType(request.getDeviceType()));
        dto.setOsVersion (request.getOsVersion());
        dto.setBrand (request.getBrand());
        dto.setModel (request.getModel());
        dto.setModelCode (request.getModelCode());
        dto.setAndroidId (request.getAndroidId());
        dto.setAndroidIdMd5 (request.getAndroidIdMd5());
        dto.setImei (request.getImei());
        dto.setImeiMd5 (request.getImeiMd5());
        dto.setOaid (request.getOaid());
        dto.setOaidMd5 (request.getOaidMd5());
        dto.setIdfa (request.getIdfa());
        dto.setIdfaMd5 (request.getIdfaMd5());
        dto.setVaid (request.getVaid());
        dto.setVaidMd5 (request.getVaidMd5());
        dto.setIdfaPolicy (request.getIdfaPolicy());
        dto.setBatteryStatus (request.getBatteryStatus());
        dto.setBatteryPower (request.getBatteryPower());
        dto.setOpenUdId (request.getOpenUdId());
        dto.setIdfv (request.getIdfv());
        dto.setIdfvMd5 (request.getIdfvMd5());
        dto.setImsi (request.getImsi());
        dto.setImsiMd5 (request.getImsiMd5());
        dto.setWidth (request.getWidth());
        dto.setHeight (request.getHeight());
        dto.setOrientation (OrientationType.findByType(request.getOrientation()));
        dto.setUserAgent (request.getUserAgent());
        dto.setScreenDensity (request.getScreenDensity());
        dto.setScreenInch (request.getScreenInch());
        dto.setPpi (request.getPpi());
        dto.setDeviceMemory (request.getDeviceMemory());
        dto.setDeviceHardDisk (request.getDeviceHardDisk());
        dto.setTimeZone (request.getTimeZone());
        dto.setLanguage (request.getLanguage());
        dto.setCountry (request.getCountry());
        dto.setCpuNum (request.getCpuNum());
        dto.setHardwareMachine (request.getHardwareMachine());
        dto.setHardwareModel (request.getHardwareModel());
        dto.setHmsVersion (request.getHmsVersion());
        dto.setIsOpenPersonalRecommend (request.getIsOpenPersonal());
        dto.setIsProgrammaticRecommend (request.getIsProgrammatic());
        if (null != request.getInstalledAppInfo()){
            dto.setInstalledAppInfo(request.getInstalledAppInfo().stream().map(item -> {
                RequestInstalledAppDto appDto = new RequestInstalledAppDto();
                appDto.setAppName(item.getAppName());
                appDto.setAppVersion(item.getAppVersion());
                appDto.setIsSystemApp(item.getIsSystemApp());
                appDto.setPackageName(item.getPackageName());
                appDto.setLastUpdateTime(item.getLastUpdateTime());
                appDto.setFirstInstallTime(item.getFirstInstallTime());
                return appDto;
            }).collect(Collectors.toList()));
        }
        dto.setSysStartTime (request.getSysStartTime());
        dto.setSysUpdateTime (request.getSysUpdateTime());
        dto.setSysCompileTime (request.getSysCompileTime());
        dto.setSysInitTime (request.getSysInitTime());
        dto.setUpdateMark (request.getUpdateMark());
        dto.setBootMark (request.getBootMark());
        dto.setAppStoreVersion (request.getAppStoreVersion());
        dto.setAppstoreVersionCode (request.getAppstoreVersionCode());
        if (null != request.getCaIds()){
            List<RequestCaidDto> caids = new ArrayList<>();
            for (TakenMediaCaid item : request.getCaIds()) {
                if (null != item) {
                    caids.add(new RequestCaidDto(item.getCaid(),item.getVersion(),CaidVendor.findByType(item.getVendor()), item.getTimestamp()));
                }
            }
            dto.setCaids(caids);
        }
        dto.setLocalName (request.getLocalName());
        dto.setCpuFreq (request.getCpuFreq());
        dto.setHmsAgVersion (request.getHmsAgVersion());
        dto.setAaid (request.getAaid());
        dto.setAaidMd5 (request.getAaidMd5());
        dto.setPaid (request.getPaid());
        dto.setVendor (request.getVendor());
        dto.setRomVersion (request.getRomVersion());
        dto.setSysElapseTime (request.getSysElapseTime());
        dto.setSysUiVersion (request.getSysUiVersion());
        dto.setCookie (request.getCookie());
        dto.setReferer (request.getReferer());
        dto.setIsRoot (Boolean.TRUE.equals(request.getIsRoot()) ? 1 : 0);
        dto.setSkanVersion (request.getSkanVersion());
        dto.setApiLevel (request.getApiLevel());
        return dto;
    }

    private RequestUserDto convertRequestUser(TakenMediaRequestUser request) {
        if (null == request){
            return null;
        }
        RequestUserDto dto = new RequestUserDto();
        dto.setUserId(request.getUserId());
        dto.setAge(request.getAge());
        dto.setGender(request.getGender());
        dto.setInterest(request.getInterest());
        dto.setIsMarriage(request.getIsMarriage());
        dto.setIsSchool(request.getIsSchool());
        return dto;
    }

    private RequestTagDto convertRequestTag(TakenMediaRequestTag request) {
        if (null == request){
            return null;
        }
        RequestTagDto dto = new RequestTagDto();
        dto.setTagId(request.getTagId());
        dto.setSize(1);
        dto.setWidth(request.getWidth());
        dto.setHeight(request.getHeight());
        dto.setMinDuration(request.getMinDuration());
        dto.setMaxDuration(request.getMaxDuration());
        dto.setPrice(request.getPrice());
        dto.setQuery(request.getQuery());
        return dto;
    }

    private RequestGeoDto convertRequestGeo(TakenMediaRequestGeo request) {
        RequestGeoDto dto = new RequestGeoDto();
        if (null != request){
            dto.setLatitude(request.getLatitude());
            dto.setLongitude(request.getLongitude());
            dto.setCoordinateType(CoordinateType.findByType(request.getCoordinateType()));
            dto.setTimestamp(request.getTimestamp());
        }
        return dto;
    }

    private RequestAppDto convertRequestApp(TakenMediaRequestApp request){
        if (null == request){
            return null;
        }
        RequestAppDto dto = new RequestAppDto();
        dto.setAppId(request.getAppId());
        dto.setAppName(request.getAppName());
        dto.setAppDomainUrl(request.getAppDomainUrl());
        dto.setAppVersion(request.getAppVersion());
        dto.setBundle(request.getBundle());
        dto.setAppVersionCode(request.getAppVersionCode());
        dto.setAppstoreUrl(request.getAppstoreUrl());
        return dto;
    }
    private RequestNetworkDto convertRequestNet(TakenMediaRequestNetwork request){
        if (null == request){
            return null;
        }
        RequestNetworkDto dto = new RequestNetworkDto();
        dto.setIp(request.getIp());
        dto.setIpv6(request.getIpv6());
        dto.setMac(request.getMac());
        dto.setMacMd5(request.getMacMd5());
        dto.setSsid(request.getSsid());
        dto.setWifiMac(request.getWifiMac());
        dto.setCarrierType(CarrierType.findByType(request.getCarrierType()));
        dto.setConnectType(ConnectionType.findByType(request.getConnectType()));
        return dto;
    }

    private List<RequestPageInfoDto> convertRequestPageInfo(List<TakenMediaRequestPageInfo> infos){
        List<RequestPageInfoDto> list = new ArrayList<>();
        if (null != infos && !infos.isEmpty()){
            for (TakenMediaRequestPageInfo item : infos) {
                if (null != item){
                    RequestPageInfoDto dto = new RequestPageInfoDto();
                    dto.setTitle(item.getTitle());
                    dto.setCat(item.getCat());
                    dto.setKeywords(item.getKeywords());
                    dto.setPageUrl(item.getPageUrl());
                    dto.setUseTime(item.getUseTime());
                    dto.setContentTime(item.getContentTime());
                    list.add(dto);
                }
            }
        }
        return list;
    }


}
