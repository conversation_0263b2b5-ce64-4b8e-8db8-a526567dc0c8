package cn.taken.ad.logic.media.duoying;

import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.logic.AbstractMediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.logic.media.duoying.dto.DuoYingMediaBidRequest;
import cn.taken.ad.logic.media.duoying.dto.DuoYingMediaBidResponse;
import cn.taken.ad.utils.web.HttpRequestUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 多赢媒体协议
 * 应用ID、广告位ID需要特殊处理
 * 协议没有appid，只有广告位id，给多赢的广告位id包含appid，格式appId#tagId
 * 示例 广告位ID：baca2141#uy1d7yz1
 */
@Component("DUOYING" + LogicSuffix.MEDIA_LOGIC_SUFFIX)
public class DuoYingMediaProcessor extends AbstractMediaProcessor {

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;

    @Override
    public RtbRequestDto parseRtb(RtbMediaDto mediaDto, String rtbId) throws Throwable {
        byte[] byteData = HttpRequestUtils.readBytes();
        if (byteData == null) {
            return null;
        }
        DuoYingMediaBidRequest mediaRequest = DuoYingMediaBidRequest.parseFrom(byteData);
        mediaDto.setReqObj(mediaRequest);
        RtbRequestDto rtbRequestDto = new RtbRequestDto();
        rtbRequestDto.setReqId(mediaRequest.getId());
        rtbRequestDto.setApp(convertRequestApp(mediaRequest));
        rtbRequestDto.setTag(convertRequestTag(mediaRequest, rtbRequestDto.getApp(), mediaDto));
        RequestNetworkDto networkDto = new RequestNetworkDto();
        RequestDeviceDto deviceDto = new RequestDeviceDto();
        RequestGeoDto geoDto = new RequestGeoDto();

        convertRequestDeviceAndNetwork(mediaRequest, deviceDto, networkDto, geoDto);
        rtbRequestDto.setDevice(deviceDto);
        rtbRequestDto.setNetwork(networkDto);
        rtbRequestDto.setGeo(geoDto);
        rtbRequestDto.setUser(convertRequestUser(mediaRequest, deviceDto));

        return rtbRequestDto;
    }

    @Override
    public SuperResult<String> returnRtb(RtbMediaDto mediaDto) throws Throwable {

        RtbResponseDto resp = mediaDto.getResponse();
        LogicState state = LogicState.findByCode(resp.getCode());
        if (state != LogicState.SUCCESS) {
            HttpResponseUtils.getCurrentHttpResponse().setStatus(204);
            mediaDto.setRespObj("HttpCode:204");
            return SuperResult.rightResult();
        }
        if (null == resp.getTags() || resp.getTags().isEmpty()) {
            mediaDto.setRespObj("HttpCode:204");
            HttpResponseUtils.getCurrentHttpResponse().setStatus(204);
            return SuperResult.rightResult();
        }

        DuoYingMediaBidResponse.Builder bidResponse = DuoYingMediaBidResponse.newBuilder();
        bidResponse.setId(mediaDto.getRequest().getReqId());
        for (TagResponseDto tag : resp.getTags()) {
            String price;
            DuoYingMediaBidResponse.Bid.Builder bid = DuoYingMediaBidResponse.Bid.newBuilder();
            bid.setAdzoneId(mediaDto.getRequest().getTag().getTagId());
            if (StringUtils.isNotEmpty(tag.getCreativeId())) {
                bid.setCrid(tag.getCreativeId());
            } else {
                // 随机生成1个
                bid.setCrid(UUID.randomUUID().toString().replace("-", ""));
            }
            if (null != tag.getRespMediaPrice() && tag.getRespMediaPrice() > 0) {
                bid.setPrice(tag.getRespMediaPrice().intValue());
                // 这里加密价格
                price = Hex.encodeHexString(Aes.encrypt(tag.getRespMediaPrice().intValue() + "", mediaDto.getMediaPriceKey()));
            } else {
                price = "";
            }
            if (null != tag.getWinNoticeUrls() && !tag.getWinNoticeUrls().isEmpty()) {
                if (StringUtils.isNotEmpty(price)) {
                    for (String url : tag.getWinNoticeUrls()) {
                        url = url.replace(MacroType.WIN_PRICE.getCode(), price);
                        bid.addWinNoticeUrl(url);
                    }
                } else {
                    bid.addAllWinNoticeUrl(tag.getWinNoticeUrls());
                }
            }

            DuoYingMediaBidResponse.Bid.Link.Builder link = DuoYingMediaBidResponse.Bid.Link.newBuilder();
            link.setCurl(tag.getClickUrl());
            if (StringUtils.isNotEmpty(tag.getUniversalLink())) {
                link.setCurl(tag.getUniversalLink());
            }
            if (StringUtils.isNotEmpty(tag.getDeepLinkUrl())) {
                link.setDeeplink(tag.getDeepLinkUrl());
            }
            DuoYingMediaBidResponse.Bid.DownloadTracker.Builder downloadTracker = DuoYingMediaBidResponse.Bid.DownloadTracker.newBuilder();
            for (ResponseTrackDto track : tag.getTracks()) {
                List<String> urls = convertMacroCode(track.getTrackUrls());
                EventType eventType = EventType.findByType(track.getTrackType());
                if (null != eventType) {
                    switch (eventType) {
                        case EXPOSURE:
                            // 价格宏替换
                            if (StringUtils.isNotEmpty(price)) {
                                for (String url : urls) {
                                    url = url.replace(MacroType.WIN_PRICE.getCode(), price);
                                    link.addImptrackers(url);
                                }
                            } else {
                                link.addAllImptrackers(urls);
                            }
                            break;
                        case VIDEO_BEGIN:
                            link.addAllStarttrackers(urls);
                            break;
                        case VIDEO_END:
                            link.addAllCompletetrackers(urls);
                            break;
                        case CLICK:
                            link.addAllClicktrackers(urls);
                            break;
                        case DOWNLOAD_BEGIN:
                            downloadTracker.addAllStartdownload(urls);
                            break;
                        case DOWNLOAD_COMPLETED:
                            downloadTracker.addAllFinishdownload(urls);
                            break;
                        case INSTALL_BEGIN:
                            downloadTracker.addAllStartinstall(urls);
                            break;
                        case INSTALL_COMPLETED:
                            downloadTracker.addAllFinishinstall(urls);
                            break;
                        case VIDEO_50:
                            link.addAllMidPointTrackers(urls);
                            break;
                        case DEEPLINK_OPEN_SUCCESS:
                            link.addAllDeeplinktrackers(urls);
                            break;
                        // 其他事件无对应链接，处理不了
                    }
                }
            }
            link.setDownloadtrackers(downloadTracker.build());
            if ((tag.getImgUrls() != null && !tag.getImgUrls().isEmpty()) || (tag.getVideoInfo() != null && StringUtils.isNotEmpty(tag.getVideoInfo().getVideoUrl()))) {
                DuoYingMediaBidResponse.Bid.AdmNative.Builder admNative = DuoYingMediaBidResponse.Bid.AdmNative.newBuilder();
                if (StringUtils.isNotEmpty(tag.getTitle())) {
                    admNative.setTitle(tag.getTitle());
                }
                if (StringUtils.isNotEmpty(tag.getLogoUrl())) {
                    DuoYingMediaBidResponse.Bid.Image.Builder img = DuoYingMediaBidResponse.Bid.Image.newBuilder();
                    img.setUrl(tag.getLogoUrl());
                    img.setType(DuoYingMediaBidResponse.Bid.Image.ImageAssetType.LOGO);
                    admNative.addImgs(img);
                }
                if (StringUtils.isNotEmpty(tag.getIconUrl())) {
                    DuoYingMediaBidResponse.Bid.Image.Builder img = DuoYingMediaBidResponse.Bid.Image.newBuilder();
                    img.setUrl(tag.getIconUrl());
                    img.setType(DuoYingMediaBidResponse.Bid.Image.ImageAssetType.ICON);
                    admNative.addImgs(img);
                }
                if (tag.getImgUrls() != null && !tag.getImgUrls().isEmpty()) {
                    for (String imgUrl : tag.getImgUrls()) {
                        if (StringUtils.isNotEmpty(imgUrl)) {
                            DuoYingMediaBidResponse.Bid.Image.Builder img = DuoYingMediaBidResponse.Bid.Image.newBuilder();
                            if (null != tag.getMaterialHeight() && tag.getMaterialHeight() > 0) {
                                img.setH(tag.getMaterialHeight());
                            }
                            if (null != tag.getMaterialWidth() && tag.getMaterialWidth() > 0) {
                                img.setW(tag.getMaterialWidth());
                            }
                            img.setUrl(imgUrl);
                            img.setType(DuoYingMediaBidResponse.Bid.Image.ImageAssetType.MAIN);
                            admNative.addImgs(img);
                        }
                    }
                }
                if (tag.getVideoInfo() != null && StringUtils.isNotEmpty(tag.getVideoInfo().getVideoUrl())) {
                    DuoYingMediaBidResponse.Bid.Video.Builder video = DuoYingMediaBidResponse.Bid.Video.newBuilder();
                    video.setUrl(tag.getVideoInfo().getVideoUrl());
                    if (null != tag.getVideoInfo().getDuration()) {
                        video.setDuration(tag.getVideoInfo().getDuration());
                    }
                    if (null != tag.getVideoInfo().getVideoWidth()) {
                        video.setW(tag.getVideoInfo().getVideoWidth());
                    }
                    if (null != tag.getVideoInfo().getVideoHeight()) {
                        video.setH(tag.getVideoInfo().getVideoHeight());
                    }
                    admNative.setVideo(video);
                }
                admNative.setLink(link);
                if (null != tag.getAppInfo()) {
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppName())) {
                        admNative.setAppName(tag.getAppInfo().getAppName());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getPackageName())) {
                        admNative.setPackageName(tag.getAppInfo().getPackageName());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppIconUrl())) {
                        admNative.setAppIcon(tag.getAppInfo().getAppIconUrl());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppVersion())) {
                        admNative.setAppVersion(tag.getAppInfo().getAppVersion());
                    }
                }
                bid.setAdmnative(admNative);
            } else {
                if (null != tag.getAppInfo() && StringUtils.isNotEmpty(tag.getAppInfo().getAppInfoUrl())) {
                    bid.setDetailPageUrl(tag.getAppInfo().getAppInfoUrl());
                }
                bid.setAppDescPageUrl(tag.getClickUrl());
                if (StringUtils.isNotEmpty(tag.getUniversalLink())) {
                    bid.setAppDescPageUrl(tag.getUniversalLink());
                }
                if (StringUtils.isNotEmpty(tag.getTitle())) {
                    bid.setTitle(tag.getTitle());
                }
                if (StringUtils.isNotEmpty(tag.getDesc())) {
                    bid.setDescription(tag.getDesc());
                }
                if (null != tag.getAppInfo()) {
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppName())) {
                        bid.setAppName(tag.getAppInfo().getAppName());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getPackageName())) {
                        bid.setApkName(tag.getAppInfo().getPackageName());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppVersion())) {
                        bid.setAppVersion(tag.getAppInfo().getAppVersion());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppDeveloper())) {
                        bid.setAppDeveloper(tag.getAppInfo().getAppDeveloper());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppPermissionInfoUrl())) {
                        bid.setAppPermission(tag.getAppInfo().getAppPermissionInfoUrl());
                    }
                    if (StringUtils.isNotEmpty(tag.getAppInfo().getAppPrivacyUrl())) {
                        bid.setAppPrivacy(tag.getAppInfo().getAppPrivacyUrl());
                    }
                }
                bid.setLink(link);
                if (null != tag.getMiniProgram()) {
                    if (StringUtils.isNotEmpty(tag.getMiniProgram().getUserName())) {
                        bid.setMiniAppName(tag.getMiniProgram().getUserName());
                    }
                    if (StringUtils.isNotEmpty(tag.getMiniProgram().getPath())) {
                        bid.setMiniAppPath(tag.getMiniProgram().getPath());
                    }
                }
            }

            // action
            switch (tag.getActionType()) {
                case WEB_VIEW_H5:
                case SYSTEM_BROWSER_H5:
                    bid.setAction(DuoYingMediaBidResponse.Bid.AdActionType.OPEN_IN_WEBVIEW);
                    break;
                case DEEPLINK:
                    bid.setAction(DuoYingMediaBidResponse.Bid.AdActionType.OPEN_APP_DEEPLINK);
                    break;
                case DOWNLOAD:
                    bid.setAction(DuoYingMediaBidResponse.Bid.AdActionType.DOWNLOAD_APP);
                    break;
                case MINI_PROGRAM:
                    bid.setAction(DuoYingMediaBidResponse.Bid.AdActionType.MINI_APP);
                    break;
            }

            bidResponse.addBid(bid);
        }
        mediaDto.setRespObj(bidResponse);
        HttpResponseUtils.outputBytes(bidResponse.build().toByteArray());
        return SuperResult.rightResult();
    }

    /**
     * 宏参数替换为媒体的
     */
    public List<String> convertMacroCode(List<String> urls) {
        List<String> result = new ArrayList<>();
        urls.forEach(url -> {
            url = url.replace(MacroType.DOWN_X.getCode(), "%%DOWNX%%");
            url = url.replace(MacroType.DOWN_Y.getCode(), "%%DOWNY%%");
            url = url.replace(MacroType.UP_X.getCode(), "%%UPX%%");
            url = url.replace(MacroType.UP_Y.getCode(), "%%UPY%%");
            url = url.replace(MacroType.WIDTH.getCode(), "%%WIDTH%%");
            url = url.replace(MacroType.HEIGHT.getCode(), "%%HEIGHT%%");
            url = url.replace(MacroType.START_TIME.getCode(), "%%EVENT_TIME_START%%");
            url = url.replace(MacroType.END_TIME.getCode(), "%%EVENT_TIME_END%%");
            url = url.replace(MacroType.TIME.getCode(), "%%EVENT_TIME%%");
            url = url.replace(MacroType.DOWN_X.getCode(), "__DOWN_X__");
            url = url.replace(MacroType.DOWN_Y.getCode(), "__DOWN_Y__");
            url = url.replace(MacroType.UP_X.getCode(), "__UP_X__");
            url = url.replace(MacroType.UP_X.getCode(), "__UP_Y__");

            result.add(url);
        });
        return result;
    }

    private RequestUserDto convertRequestUser(DuoYingMediaBidRequest mediaRequest, RequestDeviceDto deviceDto) {
        RequestUserDto dto = new RequestUserDto();
        DuoYingMediaBidRequest.User requestUser = mediaRequest.getUser();
        if (StringUtils.isNotEmpty(requestUser.getId())) {
            dto.setUserId(requestUser.getId());
        }
        if (StringUtils.isNotEmpty(requestUser.getApplist())) {
            String[] packageNames = requestUser.getApplist().split(",");
            List<RequestInstalledAppDto> installedAppInfo = new ArrayList<>();
            for (String packageName : packageNames) {
                if (StringUtils.isNotEmpty(packageName)) {
                    RequestInstalledAppDto appDto = new RequestInstalledAppDto();
                    appDto.setAppName(packageName);
                    installedAppInfo.add(appDto);
                }
            }
            if (!installedAppInfo.isEmpty()) {
                deviceDto.setInstalledAppInfo(installedAppInfo);
            }
        }
        return dto;
    }

    private void convertRequestDeviceAndNetwork(DuoYingMediaBidRequest mediaRequest, RequestDeviceDto deviceDto, RequestNetworkDto networkDto, RequestGeoDto geoDto) {
        DuoYingMediaBidRequest.Device requestDevice = mediaRequest.getDevice();
        if (StringUtils.isNotEmpty(requestDevice.getUa())) {
            deviceDto.setUserAgent(requestDevice.getUa());
        }
        DuoYingMediaBidRequest.Geo requestGeo = requestDevice.getGeo();
        if (requestGeo.getLat() > 0 && requestGeo.getLon() > 0) {
            geoDto.setLatitude(requestGeo.getLat());
            geoDto.setLongitude(requestGeo.getLon());
        }
        if (StringUtils.isNotEmpty(requestDevice.getIp())) {
            networkDto.setIp(requestDevice.getIp());
        }
        if (StringUtils.isNotEmpty(requestDevice.getIdfa())) {
            deviceDto.setIdfa(requestDevice.getIdfa());
        }
        if (StringUtils.isNotEmpty(requestDevice.getIdfaMd5())) {
            deviceDto.setIdfaMd5(requestDevice.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getImeiMd5())) {
            deviceDto.setImeiMd5(requestDevice.getImeiMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getAndroidid())) {
            deviceDto.setAndroidId(requestDevice.getAndroidid());
        }
        if (StringUtils.isNotEmpty(requestDevice.getAndroididMd5())) {
            deviceDto.setAndroidIdMd5(requestDevice.getAndroididMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getOaid())) {
            deviceDto.setOaid(requestDevice.getOaid());
        }
        if (StringUtils.isNotEmpty(requestDevice.getOaidMd5())) {
            deviceDto.setOaidMd5(requestDevice.getOaidMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getMac())) {
            networkDto.setMac(requestDevice.getMac());
        }
        if (StringUtils.isNotEmpty(requestDevice.getProcessedMacMd5())) {
            networkDto.setMacMd5(requestDevice.getProcessedMacMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getMake())) {
            deviceDto.setBrand(requestDevice.getMake());
            deviceDto.setVendor(requestDevice.getMake());
        }
        if (StringUtils.isNotEmpty(requestDevice.getModel())) {
            deviceDto.setModel(requestDevice.getModel());
        }
        if (StringUtils.isNotEmpty(requestDevice.getOsv())) {
            deviceDto.setOsVersion(requestDevice.getOsv());
        }
        if (requestDevice.getOsApiLevel() > 0) {
            deviceDto.setApiLevel(requestDevice.getOsApiLevel());
        }
        deviceDto.setOsType(OsType.UNKNOWN);
        if (StringUtils.isNotEmpty(requestDevice.getOs())) {
            if (requestDevice.getOs().equalsIgnoreCase(OsType.IOS.getCode())) {
                deviceDto.setOsType(OsType.IOS);
            } else if (requestDevice.getOs().equalsIgnoreCase(OsType.ANDROID.getCode())) {
                deviceDto.setOsType(OsType.ANDROID);
            }
        }
        if (requestDevice.getH() > 0) {
            deviceDto.setHeight(requestDevice.getH());
        }
        if (requestDevice.getW() > 0) {
            deviceDto.setWidth(requestDevice.getW());
        }
        deviceDto.setDeviceType(DeviceType.UNKNOWN);
        switch (requestDevice.getDevicetype()) {
            case PHONE:
                deviceDto.setDeviceType(DeviceType.PHONE);
                break;
            case PAD:
                deviceDto.setDeviceType(DeviceType.PAD);
                break;
            case PERSONAL_COMPUTER:
                deviceDto.setDeviceType(DeviceType.PC);
                break;
        }
        networkDto.setCarrierType(CarrierType.UNKNOWN);
        switch (requestDevice.getCarrier()) {
            case 1:
                networkDto.setCarrierType(CarrierType.CM);
                break;
            case 2:
                networkDto.setCarrierType(CarrierType.CU);
                break;
            case 3:
                networkDto.setCarrierType(CarrierType.CT);
                break;
        }
        networkDto.setConnectType(ConnectionType.UNKNOWN);
        switch (requestDevice.getConnectiontype()) {
            case ETHERNET:
                networkDto.setConnectType(ConnectionType.ETHERNET);
                break;
            case WIFI:
                networkDto.setConnectType(ConnectionType.WIFI);
                break;
            case CELL_UNKNOWN:
                networkDto.setConnectType(ConnectionType.NETWORK_CELLULAR);
                break;
            case CELL_2G:
                networkDto.setConnectType(ConnectionType.NETWORK_2G);
                break;
            case CELL_3G:
                networkDto.setConnectType(ConnectionType.NETWORK_3G);
                break;
            case CELL_4G:
                networkDto.setConnectType(ConnectionType.NETWORK_4G);
                break;
            case CELL_5G:
                networkDto.setConnectType(ConnectionType.NETWORK_5G);
                break;
        }
        if (StringUtils.isNotEmpty(requestDevice.getCountryCode())) {
            deviceDto.setCountry(requestDevice.getCountryCode());
        }
        if (StringUtils.isNotEmpty(requestDevice.getTimeZoneSec())) {
            deviceDto.setTimeZone(requestDevice.getTimeZoneSec());
        }
        if (StringUtils.isNotEmpty(requestDevice.getDeviceNameMd5())) {
            deviceDto.setDeviceNameMd5(requestDevice.getDeviceNameMd5());
        }
        if (StringUtils.isNotEmpty(requestDevice.getDeviceLanguage())) {
            deviceDto.setLanguage(requestDevice.getDeviceLanguage());
        }
        if (StringUtils.isNotEmpty(requestDevice.getMachineOfDevice())) {
            deviceDto.setHardwareMachine(requestDevice.getMachineOfDevice());
        }
        if (StringUtils.isNotEmpty(requestDevice.getBootMark())) {
            deviceDto.setBootMark(requestDevice.getBootMark());
        }
        if (StringUtils.isNotEmpty(requestDevice.getUpdateMark())) {
            deviceDto.setUpdateMark(requestDevice.getUpdateMark());
        }
        if (requestDevice.getDiskTotal() > 0) {
            deviceDto.setDeviceHardDisk(requestDevice.getDiskTotal());
        }
        if (requestDevice.getMemTotal() > 0) {
            deviceDto.setDeviceMemory(requestDevice.getMemTotal());
        }
        if (StringUtils.isNotBlank(requestDevice.getSysCompilingTime())) {
            deviceDto.setSysCompileTime(requestDevice.getSysCompilingTime());
        }
        if (StringUtils.isNotBlank(requestDevice.getSystemUpdate())) {
            deviceDto.setSysUpdateTime(requestDevice.getSystemUpdate());
        }
        if (StringUtils.isNotBlank(requestDevice.getDeviceStartSec())) {
            deviceDto.setSysStartTime(requestDevice.getDeviceStartSec());
        }
        if (StringUtils.isNotBlank(requestDevice.getBirthTime())) {
            deviceDto.setSysInitTime(requestDevice.getBirthTime());
        }
        if (!requestDevice.getCaidsList().isEmpty()) {
            deviceDto.setCaids(new ArrayList<>());
            for (DuoYingMediaBidRequest.Caid caid : requestDevice.getCaidsList()) {
                if (StringUtils.isNotBlank(caid.getCaid()) && StringUtils.isNotBlank(caid.getCaidVersion())) {
                    deviceDto.getCaids().add(new RequestCaidDto(caid.getCaid(), caid.getCaidVersion()));
                }
            }
        }
        if (StringUtils.isNotBlank(requestDevice.getVercodeOfHms())) {
            deviceDto.setHmsVersion(requestDevice.getVercodeOfHms());
        }
        if (StringUtils.isNotBlank(requestDevice.getVercodeOfAg())) {
            deviceDto.setHmsVersion(requestDevice.getVercodeOfAg());
        }
    }

    private RequestTagDto convertRequestTag(DuoYingMediaBidRequest mediaRequest, RequestAppDto appDto, RtbMediaDto mediaDto) {
        if (mediaRequest.getImpList().isEmpty()) {
            return null;
        }
        RequestTagDto dto = new RequestTagDto();
        DuoYingMediaBidRequest.Imp requestImp = mediaRequest.getImpList().get(0);
        String tagCode = requestImp.getAdzoneId();
        if (StringUtils.isNotEmpty(tagCode)) {
            if (tagCode.contains("#")) {
                String[] tmp = requestImp.getAdzoneId().split("#");
                if (tmp.length != 2) {
                    return null;
                }
                // appId在这里赋值
                appDto.setAppId(tmp[0]);
                dto.setTagId(tmp[1]);
            } else {
                MediaTag mediaTag = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_NOAPPCODE_TAG_CODE_ + mediaDto.getMediaId() + "_" + tagCode, MediaTag.class);
                if (mediaTag == null) {
                    return null;
                }
                MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_ID_ + mediaTag.getMediaAppId(), MediaApp.class);
                if (mediaApp == null) {
                    return null;
                }
                appDto.setAppId(mediaApp.getCode());
                dto.setTagId(tagCode);
            }

        }
        if (requestImp.getBidfloor() > 0) {
            dto.setPrice((double) requestImp.getBidfloor());
        }
        DuoYingMediaBidRequest.Imp.Native impNative = requestImp.getNative();
        // impNative.getImgsList(); 不使用媒体传过来的图片参数 参数为图片宽、高要求；非广告位宽/高
        DuoYingMediaBidRequest.Imp.Native.Video video = impNative.getVideo();
        if (video.getMinduration() > 0) {
            dto.setMinDuration(video.getMinduration());
        }
        if (video.getMaxduration() > 0) {
            dto.setMaxDuration(video.getMaxduration());
        }
        dto.setNeedHttps(requestImp.getSecure() == 1 ? true : false);
        return dto;
    }

    private RequestAppDto convertRequestApp(DuoYingMediaBidRequest mediaRequest) {
        RequestAppDto dto = new RequestAppDto();
        DuoYingMediaBidRequest.App requestApp = mediaRequest.getApp();
        // appId 在广告位中处理
        if (StringUtils.isNotEmpty(requestApp.getName())) {
            dto.setAppName(requestApp.getName());
        }
        if (StringUtils.isNotEmpty(requestApp.getBundle())) {
            dto.setBundle(requestApp.getBundle());
        }
        if (StringUtils.isNotEmpty(requestApp.getDomain())) {
            dto.setAppDomainUrl(requestApp.getDomain());
        }
        if (StringUtils.isNotEmpty(requestApp.getVer())) {
            dto.setAppVersion(requestApp.getVer());
        }
        if (requestApp.getVercode() > 0) {
            dto.setAppVersionCode(String.valueOf(requestApp.getVercode()));
        }
        // app关键字 暂无
        return dto;
    }

    @Override
    public SuperResult<String> mediaParseBill(RtbBillDto bill, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseEvent(RtbEventDto event, Media media) {
        return SuperResult.rightResult();
    }
    @Override
    public SuperResult<Double> decryptPrice(String price, Media media) {
        if (StringUtils.isBlank(price)) {
            return SuperResult.badResult("price empty");
        }
        if (price.equals(MacroType.WINNER_PRICE.getCode()) || price.equals(MacroType.WIN_PRICE.getCode())) {
            // 未替换 价格宏
            return SuperResult.badResult("price not replaced");
        }
        try {
            String priceSecretKey = media.getPriceKey();
            byte[] data = Aes.decrypt(Hex.decodeHex(price), priceSecretKey);
            return SuperResult.rightResult(Double.valueOf(new String(data)));
        } catch (Exception e) {
            return SuperResult.badResult("decrypt["+price+"] error:"+e.getMessage());
        }
    }
}
