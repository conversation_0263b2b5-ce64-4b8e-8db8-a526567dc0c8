package cn.taken.ad.logic.media.yinghuochong.dto;

import java.util.List;

public class AdSlot {
    private String appId;
    private String adSlotId;
    private Integer bidFloor;
    private Float cpcBidFloor;
    private YingHuoChongAdType adType;
    private List<YingHuoChongActionType> actionType;
    private AdInfo adInfo;
    private Integer maxCount;
    private YingHuoChongMaterialType materialType;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAdSlotId() {
        return adSlotId;
    }

    public void setAdSlotId(String adSlotId) {
        this.adSlotId = adSlotId;
    }

    public Integer getBidFloor() {
        return bidFloor;
    }

    public void setBidFloor(Integer bidFloor) {
        this.bidFloor = bidFloor;
    }

    public Float getCpcBidFloor() {
        return cpcBidFloor;
    }

    public void setCpcBidFloor(Float cpcBidFloor) {
        this.cpcBidFloor = cpcBidFloor;
    }

    public YingHuoChongAdType getAdType() {
        return adType;
    }

    public void setAdType(YingHuoChongAdType adType) {
        this.adType = adType;
    }

    public List<YingHuoChongActionType> getActionType() {
        return actionType;
    }

    public void setActionType(List<YingHuoChongActionType> actionType) {
        this.actionType = actionType;
    }

    public AdInfo getAdInfo() {
        return adInfo;
    }

    public void setAdInfo(AdInfo adInfo) {
        this.adInfo = adInfo;
    }

    public Integer getMaxCount() {
        return maxCount;
    }

    public void setMaxCount(Integer maxCount) {
        this.maxCount = maxCount;
    }

    public YingHuoChongMaterialType getMaterialType() {
        return materialType;
    }

    public void setMaterialType(YingHuoChongMaterialType materialType) {
        this.materialType = materialType;
    }
}
