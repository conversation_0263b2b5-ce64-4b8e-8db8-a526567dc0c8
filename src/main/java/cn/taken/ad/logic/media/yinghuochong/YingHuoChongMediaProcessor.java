package cn.taken.ad.logic.media.yinghuochong;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.logic.AbstractMediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.media.yinghuochong.dto.AdInfo;
import cn.taken.ad.logic.media.yinghuochong.dto.AdRequest;
import cn.taken.ad.logic.media.yinghuochong.dto.AdSlot;
import cn.taken.ad.logic.media.yinghuochong.dto.YingHuoChongAdType;
import cn.taken.ad.logic.media.zixuan.dto.*;
import cn.taken.ad.utils.web.HttpRequestUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * https://alidocs.dingtalk.com/i/nodes/pYLaezmVNeql1jvMUa0vMQe7WrMqPxX6
 */
@Component("YINGHUOCHONG" + LogicSuffix.MEDIA_LOGIC_SUFFIX)
public class YingHuoChongMediaProcessor extends AbstractMediaProcessor {
    private static final Logger log = LoggerFactory.getLogger(YingHuoChongMediaProcessor.class);

    @Autowired
    private BaseRedisL2Cache baseRedisL2Cache;


    @Override
    public RtbRequestDto parseRtb(RtbMediaDto mediaDto, String rtbId) throws Throwable {
        byte[] data = HttpRequestUtils.readBytes();
        if (data == null || data.length == 0) {
            log.info("RtbId:{},Code:{},Data Is Empty", mediaDto.getMediaCode(), rtbId);
            return null;
        }
        AdRequest request = JsonHelper.fromJson(AdRequest.class,
                new String(data, StandardCharsets.UTF_8));
        mediaDto.setReqObj(request);
        if (StringUtils.isEmpty(request.getReqId())) {
            return null;
        }

        if (request.getAdSlots() == null || request.getAdSlots().isEmpty()) {
            return null;
        }
        AdSlot adSlot = request.getAdSlots().get(0);
        if (StringUtils.isEmpty(adSlot.getAdSlotId()) || StringUtils.isEmpty(adSlot.getAppId())) {
            return null;
        }
        MediaTag mediaTag = baseRedisL2Cache.get(
                BaseRedisKeys.KV_MEDIA_NOAPPCODE_TAG_CODE_ + mediaDto.getMediaId() + "_" + adSlot.getAdSlotId(),
                MediaTag.class);
        if (mediaTag == null) {
            return null;
        }
        MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_ID_ + adSlot.getAppId(), MediaApp.class);
        if (mediaApp == null) {
            return null;
        }
        RtbRequestDto requestDto = new RtbRequestDto();
        requestDto.setReqId(request.getReqId());
        // tag
        requestDto.setTag(createReqTag(adSlot, mediaTag));
        // app
        requestDto.setApp(createReqApp(request, mediaApp.getCode()));
        // deivce
        requestDto.setDevice(createReqDevice(request));
        // user
//        if (request.getUser() != null) {
//            requestDto.setUser(createReqUser(request));
//        }
        // network
        requestDto.setNetwork(createReqNetwork(request));
        // geo
//        if (request.getLatitude() != null || request.getLongitude() != null) {
//            RequestGeoDto geoDto = new RequestGeoDto();
//            geoDto.setLatitude(request.getLatitude());
//            geoDto.setLongitude(request.getLongitude());
//            geoDto.setCoordinateType(CoordinateType.GLOBAL);
//            requestDto.setGeo(geoDto);
//        }
        return requestDto;
    }

    private RequestUserDto createReqUser(AdRequest request) {
        RequestUserDto requestUserDto = new RequestUserDto();
        ZiXuanUser user = request.getUser();
        if (user != null) {
            if (StringUtils.isNotBlank(user.getKeywords())) {
                requestUserDto.setInterest(user.getKeywords().split(","));
            }
            if (user.getGender() != null) {
                requestUserDto.setGender(1 == user.getGender() ? "M" : "F");
            }
            if (user.getYob() != null) {
                try {
                    requestUserDto.setAge(calculateAge(Integer.parseInt(user.getYob())));
                } catch (Exception e) {

                }
            }
        }
        return requestUserDto;
    }

    public static int calculateAge(int birthYear) {
        int currentYear = LocalDate.now().getYear(); // 获取当前年份

        if (birthYear > currentYear) {
            throw new IllegalArgumentException("出生年份不能是未来的年份。");
        }

        return currentYear - birthYear;
    }

    private RequestNetworkDto createReqNetwork(AdRequest device) {
        RequestNetworkDto builder = new RequestNetworkDto();
        builder.setMac(device.getMac());
        builder.setIp(device.getIp());
        if (StringUtils.isNotBlank(device.getMac_md5())) {
            builder.setMacMd5(device.getMac_md5());
        }
        // 0:未知,1:wifi,2:2g,3:3g,4:4g,5:5g
        if (1 == device.getConnection_type()) {
            builder.setConnectType(ConnectionType.WIFI);
        } else if (2 == device.getConnection_type()) {
            builder.setConnectType(ConnectionType.NETWORK_2G);
        } else if (3 == device.getConnection_type()) {
            builder.setConnectType(ConnectionType.NETWORK_3G);
        } else if (4 == device.getConnection_type()) {
            builder.setConnectType(ConnectionType.NETWORK_4G);
        } else if (5 == device.getConnection_type()) {
            builder.setConnectType(ConnectionType.NETWORK_5G);
        } else {
            builder.setConnectType(ConnectionType.UNKNOWN);
        }
        // 没有运营商类型
        if (device.getCarrier() != null) {
            if (1 == device.getCarrier()) {
                builder.setCarrierType(CarrierType.CM);
            } else if (2 == device.getCarrier()) {
                builder.setCarrierType(CarrierType.CU);
            } else if (1 == device.getCarrier()) {
                builder.setCarrierType(CarrierType.CT);
            } else {
                builder.setCarrierType(CarrierType.UNKNOWN);
            }
        }
        if (StringUtils.isNotBlank(device.getSsid())) {
            builder.setSsid(device.getSsid());
        }
        if (StringUtils.isNotBlank(device.getWifi_mac())) {
            builder.setWifiMac(device.getWifi_mac());
        }
        return builder;
    }

    private RequestDeviceDto createReqDevice(AdRequest device) {
        RequestDeviceDto builder = new RequestDeviceDto();
        if (StringUtils.isNotBlank(device.getOaid())) {
            builder.setOaid(device.getOaid());
        }
        // builder.setOaidMd5(device.getoa());
        if (StringUtils.isNotBlank(device.getAndroid_id())) {
            builder.setAndroidId(device.getAndroid_id());
        }
        if (StringUtils.isNotBlank(device.getAndroid_id_md5())) {
            builder.setAndroidIdMd5(device.getAndroid_id_md5());
        }
        if (StringUtils.isNotBlank(device.getImei())) {
            builder.setImei(device.getImei());
        }
        // builder.setImeiMd5(device.getImeiMd5());
        if (StringUtils.isNotBlank(device.getUser_agent())) {
            builder.setUserAgent(device.getUser_agent());
        }
        if (StringUtils.isNotBlank(device.getIdfa())) {
            builder.setIdfa(device.getIdfa());
        }
        if (StringUtils.isNotBlank(device.getIdfa_md5())) {
            builder.setIdfaMd5(device.getIdfa_md5());
        }
        List<ZiXuanCaid> caids = device.getCaids();
        if (!CollectionUtils.isEmpty(caids)) {
            List<RequestCaidDto> caidDtoList = new LinkedList<>();
            caids.forEach(ziXuanCaid -> {
                RequestCaidDto caidDto = new RequestCaidDto();
                caidDto.setCaid(ziXuanCaid.getCaid());
                caidDto.setVersion(ziXuanCaid.getVersion());
                caidDtoList.add(caidDto);
            });
            builder.setCaids(caidDtoList);
        }
        // 设备类型
        // 1:手机,2:平板,3:智能 TV,4:PC
        if (device.getDevice_type() != null) {
            if (1 == device.getDevice_type()) {
                builder.setDeviceType(DeviceType.PHONE);
            } else if (2 == device.getDevice_type()) {
                builder.setDeviceType(DeviceType.PAD);
            } else if (3 == device.getDevice_type()) {
                builder.setDeviceType(DeviceType.SCREEN);
            } else if (4 == device.getDevice_type()) {
                builder.setDeviceType(DeviceType.PC);
            } else {
                builder.setDeviceType(DeviceType.UNKNOWN);
            }
        } else {
            builder.setDeviceType(DeviceType.UNKNOWN);
        }
        if (StringUtils.isNotBlank(device.getVendor())) {
            builder.setVendor(device.getVendor());
        }
        if (StringUtils.isNotBlank(device.getBrand())) {
            builder.setBrand(device.getBrand());
        }
        if (StringUtils.isNotBlank(device.getModel())) {
            builder.setModel(device.getModel());
        }
        if (StringUtils.isNotBlank(device.getInit_time())) {
            builder.setSysInitTime(device.getInit_time());
        }
        if (StringUtils.isNotBlank(device.getStartup_time())) {
            builder.setSysStartTime(device.getStartup_time());
        }
        if (StringUtils.isNotBlank(device.getUpgrade_time())) {
            builder.setSysUpdateTime(device.getUpgrade_time());
        }
        if (1 == device.getOs_type()) {
            builder.setOsType(OsType.ANDROID);
        } else if (2 == device.getOs_type()) {
            builder.setOsType(OsType.IOS);
        } else if (3 == device.getOs_type()) {
            builder.setOsType(OsType.HARMONY);
        } else {
            builder.setOsType(OsType.UNKNOWN);
        }
        if (StringUtils.isNotBlank(device.getOs_version())) {
            builder.setOsVersion(device.getOs_version());
        }
        if (!CollectionUtils.isEmpty(device.getPackages())) {
            List<RequestInstalledAppDto> appDtos = new LinkedList<>();
            device.getPackages().forEach(v -> {
//                String appName = installAppMap.get(v);
//                if (StringUtils.isNotBlank(appName)) {
//                    RequestInstalledAppDto dto = new RequestInstalledAppDto();
//                    dto.setPackageName(v);
//                    dto.setAppName(appName);
//                    appDtos.add(dto);
//                }
            });
            if (!appDtos.isEmpty()) {
                builder.setInstalledAppInfo(appDtos);
            }
        }
        if (StringUtils.isNotBlank(device.getPaid())) {
            builder.setPaid(device.getPaid());
        }
        if (device.getScreen_width() != null) {
            builder.setWidth(device.getScreen_width());
        }
        if (device.getScreen_height() != null) {
            builder.setHeight(device.getScreen_height());
        }
        if (device.getScreen_ppi() != null) {
            builder.setPpi(device.getScreen_ppi());
        }
        // if (device.getScreen_dpi() != null) {
        // builder.setScreenDensity(device.getScreen_dpi());
        // }
        if (device.getScreen_density() != null) {
            builder.setScreenDensity(device.getScreen_density());
        }
        if (device.getScreen_size() != null) {
            try {
                builder.setScreenInch(Double.parseDouble(device.getScreen_size()));
            } catch (Exception e) {

            }
        }
        // private OrientationType orientation;
        if (device.getScreen_orientation() != null) {
            if (2 == device.getScreen_orientation()) {
                builder.setOrientation(OrientationType.HORIZONTAL);
            } else if (1 == device.getScreen_orientation()) {
                builder.setOrientation(OrientationType.VERTICAL);
            } else {
                builder.setOrientation(OrientationType.UNKNOWN);
            }
        }
        if (device.getRom_version() != null) {
            builder.setRomVersion(device.getRom_version());
        }
        if (StringUtils.isNotBlank(device.getApp_store_url())) {
            builder.setAppStoreVersion(device.getApp_store_version());
        }
        if (StringUtils.isNotBlank(device.getHag())) {
            builder.setHmsAgVersion(device.getHag());
        }
        if (StringUtils.isNotBlank(device.getHardware_machine())) {
            builder.setHardwareMachine(device.getHardware_machine());
        }
        if (StringUtils.isNotBlank(device.getHardware_model())) {
            builder.setHardwareModel(device.getHardware_model());
        }
        if (StringUtils.isNotBlank(device.getDevice_name())) {
            builder.setDeviceName(device.getDevice_name());
        }
        if (StringUtils.isNotBlank(device.getDevice_name_md5())) {
            builder.setDeviceNameMd5(device.getDevice_name_md5());
        }
        if (StringUtils.isNotBlank(device.getTimezone())) {
            builder.setTimeZone(device.getTimezone());
        }
        if (StringUtils.isNotBlank(device.getCountry())) {
            builder.setCountry(device.getCountry());
        }
        if (StringUtils.isNotBlank(device.getLanguage())) {
            builder.setLanguage(device.getLanguage());
        }
        if (device.getMemory() != null && device.getMemory() > 0) {
            builder.setDeviceMemory(device.getMemory());
        }

        if (device.getHard_disk() != null && device.getHard_disk() > 0) {
            builder.setDeviceHardDisk(device.getHard_disk());
        }
        if (device.getCpu_num() != null && device.getCpu_num() > 0) {
            builder.setCpuNum(device.getCpu_num());
        }
        if (device.getCpu_freq() != null && device.getCpu_freq() > 0) {
            builder.setCpuFreq(device.getCpu_freq());
        }
        if (device.getIdfa_policy() != null && device.getIdfa_policy() > 0) {
            builder.setIdfaPolicy(device.getIdfa_policy());
        }
        if (device.getBattery_status() != null && device.getBattery_status() > 0) {
            builder.setBatteryStatus(device.getBattery_status());
        }
        if (device.getBattery_power() != null && device.getBattery_power() > 0) {
            builder.setBatteryPower(device.getBattery_power());
        }
        if (StringUtils.isNotEmpty(device.getBoot_mark())) {
            builder.setBootMark(device.getBoot_mark());
        }
        if (StringUtils.isNotEmpty(device.getUpdate_mark())) {
            builder.setUpdateMark(device.getUpdate_mark());
        }
        if (device.getElapse_time() != null && device.getElapse_time() > 0) {
            builder.setSysElapseTime(device.getElapse_time().toString());
        }
        return builder;
    }

    private RequestAppDto createReqApp(AdRequest request, String mediaAppCode) {
        RequestAppDto builder = new RequestAppDto();
        builder.setAppId(mediaAppCode);
        if (StringUtils.isNotBlank(request.getApp_name())) {
            builder.setAppName(request.getApp_name());
        }
        if (StringUtils.isNotBlank(request.getApp_ver())) {
            builder.setAppVersion(request.getApp_ver());
        }
        if (StringUtils.isNotBlank(request.getApp_package())) {
            builder.setBundle(request.getApp_package());
        }
        if (StringUtils.isNotBlank(request.getApp_store_url())) {
            builder.setAppstoreUrl(request.getApp_store_url());
        }
        return builder;
    }

    private RequestTagDto createReqTag(AdSlot adSlot, MediaTag mediaTag) {
        RequestTagDto builder = new RequestTagDto();
        builder.setTagId(adSlot.getAdSlotId());
        // * 类型
        if (YingHuoChongAdType.FEED == adSlot.getAdType()) {
            builder.setTagType(TagType.INFORMATION_FLOW);
        } else if (YingHuoChongAdType.RVIDEO == adSlot.getAdType()) {
            builder.setTagType(TagType.INCENTIVE_VIDEO);
        } else if (YingHuoChongAdType.SPLASH == adSlot.getAdType()) {
            builder.setTagType(TagType.OPEN);
        } else if (YingHuoChongAdType.INSERT == adSlot.getAdType()) {
            builder.setTagType(TagType.INTERSTITIAL);
        } else {
            builder.setTagType(TagType.OTHER);
        }
        builder.setSize(1);
        if (adSlot.getBidFloor() != null) {
            builder.setPrice(Double.parseDouble(adSlot.getBidFloor().toString()));
        }
        if(adSlot.getAdInfo() != null) {
            AdInfo adInfo = adSlot.getAdInfo();

            if(adInfo.getVideoInfo()!=null){
                builder.setMaxDuration(adInfo.getVideoInfo().getDuration());
            }
//            todo
//            if (adSlot.getSlot_width() != null) {
//                builder.setWidth(request.getSlot_width());
//            }
//            if (request.getSlot_height() != null) {
//                builder.setHeight(request.getSlot_height());
//            }
//            if (request.getSecure() != null) {
//                builder.setNeedHttps(request.getSecure() == 1);
//            }
        }

        return builder;
    }

    @Override
    public SuperResult<String> returnRtb(RtbMediaDto mediaDto) throws Throwable {
        RtbResponseDto resp = mediaDto.getResponse();
        ZiXuanMediaResponse response = new ZiXuanMediaResponse();
        LogicState state = LogicState.findByCode(resp.getCode());
        if (state != LogicState.SUCCESS) {
            response.setCode(2);
            response.setMessage("失败");
        } else if (null == resp.getTags() || resp.getTags().isEmpty()) {
            response.setCode(1);
            response.setMessage("无填充");
        }
        if (resp.getTags() != null && !resp.getTags().isEmpty()) {
            response.setCode(0);
            response.setMessage("成功");
            List<ZiXuanResponseAd> adList = new LinkedList<>();
            resp.getTags().forEach(tag -> {
                ZiXuanResponseAd ad = new ZiXuanResponseAd();
                // 广告交互类型
                // 1：落地页跳转
                // 2：下载类广告
                // 3：DEEPLINK
                // 4：GDT
                // 5：小程序广告
                if (ActionType.WEB_VIEW_H5 == tag.getActionType()
                        || ActionType.SYSTEM_BROWSER_H5 == tag.getActionType()) {
                    ad.setInteraction_type(1);
                } else if (ActionType.DOWNLOAD == tag.getActionType()) {
                    ad.setInteraction_type(2);
                } else if (ActionType.DEEPLINK == tag.getActionType()) {
                    ad.setInteraction_type(3);
                } else if (ActionType.MINI_PROGRAM == tag.getActionType()) {
                    ad.setInteraction_type(5);
                } else {
                    ad.setInteraction_type(2);
                }
                if (tag.getRespMediaPrice() != null) {
                    ad.setPrice(tag.getRespMediaPrice());
                }
                // 广告类型 1：图片广告 2：图文广告 3：纯文字广告 4：HTML广告 5：激励视频广告 6：原生视频广告 7：开屏视频广告
                if (tag.getMaterialType() != null) {
                    switch (tag.getMaterialType()) {
                        case VIDEO:
                            if (TagType.INCENTIVE_VIDEO == mediaDto.getMediaTagType()) {
                                ad.setAd_type(5);
                            } else if (TagType.OPEN == mediaDto.getMediaTagType()) {
                                ad.setAd_type(7);
                            } else {
                                ad.setAd_type(6);
                            }
                            break;
                        case IMAGE_TEXT:
                            ad.setAd_type(2);
                            break;
                        case TEXT:
                            ad.setAd_type(3);
                            break;
                        case HTML:
                            ad.setAd_type(4);
                            break;
                        default:
                            ad.setAd_type(2);

                    }

                }
                if (StringUtils.isNotBlank(tag.getTitle())) {
                    ad.setTitle(tag.getTitle());
                }
                if (StringUtils.isNotBlank(tag.getDesc())) {
                    ad.setDescription(tag.getDesc());
                }
                if (StringUtils.isNotBlank(tag.getClickUrl())) {
                    ad.setClick_url(tag.getClickUrl());
                }
                if (StringUtils.isNotBlank(tag.getDeepLinkUrl())) {
                    ad.setDeeplink_url(tag.getDeepLinkUrl());
                }
                if (StringUtils.isNotBlank(tag.getUniversalLink())) {
                    ad.setUlk(tag.getUniversalLink());
                }
                // ad.setCreativeId(tag.getCreativeId());
                if (StringUtils.isNotBlank(tag.getLogoUrl())) {
                    ad.setIcon_url(tag.getLogoUrl());
                }
                if (tag.getMiniProgram() != null) {
                    if (StringUtils.isNotBlank(tag.getMiniProgram().getId())) {
                        ad.setApplet_id(tag.getMiniProgram().getId());
                    }
                    if (StringUtils.isNotBlank(tag.getMiniProgram().getPath())) {
                        ad.setApplet_path(tag.getMiniProgram().getPath());
                    }
                }
                // 竞胜
                if (!CollectionUtils.isEmpty(tag.getWinNoticeUrls())) {
                    ad.setNurls(replaceMacro(MacroType.WIN_PRICE.getCode(), tag.getWinNoticeUrls(), "__PRICE__"));
                }
                if (!CollectionUtils.isEmpty(tag.getFailNoticeUrls())) {
                    ad.setLurls(replaceMacro(MacroType.WIN_PRICE.getCode(), tag.getFailNoticeUrls(), "__PRICE__"));
                }
                if (tag.getVideoInfo() != null) {
                    ResponseVideoDto videoDto = tag.getVideoInfo();
                    ZiXuanResponseVideo video = new ZiXuanResponseVideo();
                    if (StringUtils.isNotBlank(videoDto.getVideoUrl())) {
                        video.setUrl(videoDto.getVideoUrl());
                    }
                    if (videoDto.getDuration() != null) {
                        video.setDuration(videoDto.getDuration());
                    }
                    if (videoDto.getVideoWidth() != null && videoDto.getVideoWidth() > 0) {
                        video.setWidth(videoDto.getVideoWidth());
                    }
                    if (videoDto.getVideoHeight() != null && videoDto.getVideoHeight() > 0) {
                        video.setHeight(videoDto.getVideoHeight());
                    }
                    if (videoDto.getVideoSize() != null && videoDto.getVideoSize() > 0) {
                        video.setLength((int) (videoDto.getVideoSize() / 1024));
                    }
                    if (!CollectionUtils.isEmpty(videoDto.getCoverImgUrls())) {
                        video.setCover_url(videoDto.getCoverImgUrls().get(0));
                    }
                    if (videoDto.getEndImgUrls() != null && videoDto.getEndImgUrls().size() > 0) {
                        video.setEndcard_url(videoDto.getEndImgUrls().get(0));
                    }
                    if (StringUtils.isNotBlank(videoDto.getEndHtml())) {
                        video.setEndcard_html(videoDto.getEndHtml());
                    }

                    if (StringUtils.isNotBlank(videoDto.getEndIconUrl())) {
                        video.setEnd_icon_url(videoDto.getEndIconUrl());
                    }
                    if (StringUtils.isNotBlank(videoDto.getEndTitle())) {
                        video.setEnd_title(videoDto.getEndTitle());
                    }
                    if (StringUtils.isNotBlank(videoDto.getEndDesc())) {
                        video.setEnd_description(videoDto.getEndDesc());
                    }
                    if (StringUtils.isNotBlank(videoDto.getEndButtonText())) {
                        video.setEnd_button_text(videoDto.getEndButtonText());
                    }
                    // video.setEnd_button_url(videoDto.getend);
                    ad.setVideo(video);
                }
                if (!CollectionUtils.isEmpty(tag.getImgUrls())) {
                    List<String> imgs = new LinkedList<>(tag.getImgUrls());
                    ad.setImages(imgs);
                }
                if (tag.getAppInfo() != null) {
                    ResponseAppDto appDto = tag.getAppInfo();
                    ZiXuanResponseApp app = new ZiXuanResponseApp();
                    if (StringUtils.isNotBlank(appDto.getAppIconUrl())) {
                        app.setIcon(appDto.getAppIconUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppName())) {
                        app.setName(appDto.getAppName());
                    }
                    if (StringUtils.isNotBlank(appDto.getPackageName())) {
                        app.setPack(appDto.getPackageName());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppDeveloper())) {
                        app.setDevs(appDto.getAppDeveloper());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppInfo())) {
                        app.setInfo(appDto.getAppInfo());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppPermissionInfoUrl())) {
                        app.setPmsurl(appDto.getAppPermissionInfoUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppPermContent())) {
                        app.setPmsdesc(appDto.getAppPermContent());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppPrivacyUrl())) {
                        app.setPcyurl(appDto.getAppPrivacyUrl());
                    }
                    if (StringUtils.isNotBlank(appDto.getAppVersion())) {
                        app.setVer(appDto.getAppVersion());
                    }
                    ad.setApp(app);
                }
                if (!CollectionUtils.isEmpty(tag.getTracks())) {
                    tag.getTracks().forEach(track -> {
                        List<String> urls = track.getTrackUrls();
                        urls = replaceMacro(MacroType.WIN_PRICE.getCode(), urls, "__PRICE__");
                        urls = replaceMacro(MacroType.DP_DOWN_X.getCode(), urls, "__DOWN_X__");
                        urls = replaceMacro(MacroType.DP_DOWN_Y.getCode(), urls, "__DOWN_Y__");
                        urls = replaceMacro(MacroType.DP_UP_X.getCode(), urls, "__UP_X__");
                        urls = replaceMacro(MacroType.DP_UP_Y.getCode(), urls, "__UP_Y__");
                        urls = replaceMacro(MacroType.ABS_DOWN_X.getCode(), urls, "__AB_DOWN_X__");
                        urls = replaceMacro(MacroType.ABS_DOWN_Y.getCode(), urls, "__AB_DOWN_Y__");
                        urls = replaceMacro(MacroType.ABS_UP_X.getCode(), urls, "__AB_UP_X__");
                        urls = replaceMacro(MacroType.ABS_UP_Y.getCode(), urls, "__AB_UP_Y__");
                        urls = replaceMacro(MacroType.TIME_SECONDS.getCode(), urls, "__EVENT_TIME__");
                        urls = replaceMacro(MacroType.TIME.getCode(), urls, "__EVENT_TIME_MS__");
                        urls = replaceMacro(MacroType.START_TIME.getCode(), urls, "__CLICK_TIME_START__");
                        urls = replaceMacro(MacroType.END_TIME.getCode(), urls, "__CLICK_TIME_END__");
                        urls = replaceMacro(MacroType.VIDEO_TIME.getCode(), urls, "__DURATION__");
                        urls = replaceMacro(MacroType.VIDEO_BEGIN_TIME.getCode(), urls, "__PLAY_BEGIN_TIME__");
                        urls = replaceMacro(MacroType.VIDEO_END_TIME.getCode(), urls, "__PLAY_END_TIME__");
                        urls = replaceMacro(MacroType.VIDEO_PLAY_FIRST_FRAME.getCode(), urls, "__PLAY_FIRST_FRAME__");
                        urls = replaceMacro(MacroType.VIDEO_PLAY_LAST_FRAME.getCode(), urls, "__PLAY_LAST_FRAME__ ");
                        urls = replaceMacro(MacroType.VIDEO_SCENE.getCode(), urls, "__SCENE__");
                        urls = replaceMacro(MacroType.VIDEO_STATUS.getCode(), urls, "__BEHAIVOR__");
                        urls = replaceMacro(MacroType.VIDEO_TYPE.getCode(), urls, "__STATUS__");
                        urls = replaceMacro(MacroType.VIDEO_PROGRESS_SEC.getCode(), urls, "__PLAY_TIME__");
                        urls = replaceMacro(MacroType.VIDEO_PROGRESS.getCode(), urls, "__PLAY_TIME_MS__");
                        urls = replaceMacro(MacroType.VIDEO_P_RATE.getCode(), urls, "__SHOW_RATE__");
                        int type = track.getTrackType();
                        if (type == EventType.EXPOSURE.getType()) {
                            ad.setImpression_urls(urls);
                        } else if (type == EventType.CLICK.getType()) {
                            ad.setClick_urls(urls);
                        } else if (type == EventType.DOWNLOAD_BEGIN.getType()) {
                            ad.setDownload_begin_urls(urls);
                        } else if (type == EventType.DOWNLOAD_COMPLETED.getType()) {
                            ad.setDownload_end_urls(urls);
                        } else if (type == EventType.INSTALL_BEGIN.getType()) {
                            ad.setInstall_begin_urls(urls);
                        } else if (type == EventType.INSTALL_COMPLETED.getType()) {
                            ad.setInstall_end_urls(urls);
                        } else if (type == EventType.INSTALLED_OPEN.getType()) {
                            ad.setActive_urls(urls);
                        } else if (type == EventType.DEEPLINK_START.getType()) {
                            ad.setDeeplink_try_urls(urls);
                        } else if (type == EventType.DEEPLINK_OPEN_SUCCESS.getType()) {
                            ad.setDeeplink_success_urls(urls);
                        } else if (type == EventType.DEEPLINK_OPEN_FAIL.getType()) {
                            ad.setDeeplink_fail_urls(urls);
                        }
                    });
                }
                adList.add(ad);
            });
            response.setAds(adList);
        }
        mediaDto.setRespObj(response);
        HttpResponseUtils.outputJson(response);
        return SuperResult.rightResult();
    }

    private List<String> replaceMacro(String macro, List<String> urls, String msg) {
        return urls.stream().map(url -> url.replace(macro, msg)).collect(Collectors.toList());
    }

    @Override
    public SuperResult<String> mediaParseBill(RtbBillDto bill, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseEvent(RtbEventDto event, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<Double> decryptPrice(String price, Media media) {
        if (StringUtils.isEmpty(price)) {
            return SuperResult.badResult("price empty");
        }
        if (price.equals("%%PRICE%%")) {
            return SuperResult.badResult("price not replaced");
        }
        try {
            price = price.replaceAll(" ", "+");
            String priceSecretKey = media.getPriceKey();
            byte[] data = Aes.decrypt(cn.taken.ad.component.utils.encryption.Base64.decode(price), priceSecretKey);
            return SuperResult.rightResult(Double.valueOf(new String(data)));
        } catch (Exception e) {
            return SuperResult.badResult("decrypt[" + price + "] error:" + e.getMessage());
        }
    }

}
