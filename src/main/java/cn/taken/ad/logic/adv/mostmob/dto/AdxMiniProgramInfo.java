// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: most_mob_api.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.mostmob.dto;

/**
 * <pre>
 * AdxMiniProgramInfo 微信小程序信息类，交互类型为微信小程序时有值
 * </pre>
 *
 * Protobuf type {@code AdxMiniProgramInfo}
 */
public final class AdxMiniProgramInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:AdxMiniProgramInfo)
    AdxMiniProgramInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      AdxMiniProgramInfo.class.getName());
  }
  // Use AdxMiniProgramInfo.newBuilder() to construct.
  private AdxMiniProgramInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AdxMiniProgramInfo() {
    miniProgramId_ = "";
    miniProgramPath_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.internal_static_AdxMiniProgramInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.internal_static_AdxMiniProgramInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.class, cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.Builder.class);
  }

  private int bitField0_;
  public static final int MINI_PROGRAM_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object miniProgramId_ = "";
  /**
   * <pre>
   * 小程序id
   * </pre>
   *
   * <code>optional string mini_program_id = 1;</code>
   * @return Whether the miniProgramId field is set.
   */
  @java.lang.Override
  public boolean hasMiniProgramId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 小程序id
   * </pre>
   *
   * <code>optional string mini_program_id = 1;</code>
   * @return The miniProgramId.
   */
  @java.lang.Override
  public java.lang.String getMiniProgramId() {
    java.lang.Object ref = miniProgramId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      miniProgramId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 小程序id
   * </pre>
   *
   * <code>optional string mini_program_id = 1;</code>
   * @return The bytes for miniProgramId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMiniProgramIdBytes() {
    java.lang.Object ref = miniProgramId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      miniProgramId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MINI_PROGRAM_PATH_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object miniProgramPath_ = "";
  /**
   * <pre>
   * 小程序路径
   * </pre>
   *
   * <code>optional string mini_program_path = 2;</code>
   * @return Whether the miniProgramPath field is set.
   */
  @java.lang.Override
  public boolean hasMiniProgramPath() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <pre>
   * 小程序路径
   * </pre>
   *
   * <code>optional string mini_program_path = 2;</code>
   * @return The miniProgramPath.
   */
  @java.lang.Override
  public java.lang.String getMiniProgramPath() {
    java.lang.Object ref = miniProgramPath_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      miniProgramPath_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 小程序路径
   * </pre>
   *
   * <code>optional string mini_program_path = 2;</code>
   * @return The bytes for miniProgramPath.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMiniProgramPathBytes() {
    java.lang.Object ref = miniProgramPath_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      miniProgramPath_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, miniProgramId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, miniProgramPath_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, miniProgramId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, miniProgramPath_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo other = (cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo) obj;

    if (hasMiniProgramId() != other.hasMiniProgramId()) return false;
    if (hasMiniProgramId()) {
      if (!getMiniProgramId()
          .equals(other.getMiniProgramId())) return false;
    }
    if (hasMiniProgramPath() != other.hasMiniProgramPath()) return false;
    if (hasMiniProgramPath()) {
      if (!getMiniProgramPath()
          .equals(other.getMiniProgramPath())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMiniProgramId()) {
      hash = (37 * hash) + MINI_PROGRAM_ID_FIELD_NUMBER;
      hash = (53 * hash) + getMiniProgramId().hashCode();
    }
    if (hasMiniProgramPath()) {
      hash = (37 * hash) + MINI_PROGRAM_PATH_FIELD_NUMBER;
      hash = (53 * hash) + getMiniProgramPath().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * AdxMiniProgramInfo 微信小程序信息类，交互类型为微信小程序时有值
   * </pre>
   *
   * Protobuf type {@code AdxMiniProgramInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:AdxMiniProgramInfo)
      cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.internal_static_AdxMiniProgramInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.internal_static_AdxMiniProgramInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.class, cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      miniProgramId_ = "";
      miniProgramPath_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.internal_static_AdxMiniProgramInfo_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo build() {
      cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo buildPartial() {
      cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo result = new cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.miniProgramId_ = miniProgramId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.miniProgramPath_ = miniProgramPath_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo) {
        return mergeFrom((cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo other) {
      if (other == cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo.getDefaultInstance()) return this;
      if (other.hasMiniProgramId()) {
        miniProgramId_ = other.miniProgramId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasMiniProgramPath()) {
        miniProgramPath_ = other.miniProgramPath_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              miniProgramId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              miniProgramPath_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object miniProgramId_ = "";
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @return Whether the miniProgramId field is set.
     */
    public boolean hasMiniProgramId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @return The miniProgramId.
     */
    public java.lang.String getMiniProgramId() {
      java.lang.Object ref = miniProgramId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        miniProgramId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @return The bytes for miniProgramId.
     */
    public com.google.protobuf.ByteString
        getMiniProgramIdBytes() {
      java.lang.Object ref = miniProgramId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        miniProgramId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @param value The miniProgramId to set.
     * @return This builder for chaining.
     */
    public Builder setMiniProgramId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      miniProgramId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMiniProgramId() {
      miniProgramId_ = getDefaultInstance().getMiniProgramId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 小程序id
     * </pre>
     *
     * <code>optional string mini_program_id = 1;</code>
     * @param value The bytes for miniProgramId to set.
     * @return This builder for chaining.
     */
    public Builder setMiniProgramIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      miniProgramId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object miniProgramPath_ = "";
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @return Whether the miniProgramPath field is set.
     */
    public boolean hasMiniProgramPath() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @return The miniProgramPath.
     */
    public java.lang.String getMiniProgramPath() {
      java.lang.Object ref = miniProgramPath_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        miniProgramPath_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @return The bytes for miniProgramPath.
     */
    public com.google.protobuf.ByteString
        getMiniProgramPathBytes() {
      java.lang.Object ref = miniProgramPath_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        miniProgramPath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @param value The miniProgramPath to set.
     * @return This builder for chaining.
     */
    public Builder setMiniProgramPath(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      miniProgramPath_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMiniProgramPath() {
      miniProgramPath_ = getDefaultInstance().getMiniProgramPath();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 小程序路径
     * </pre>
     *
     * <code>optional string mini_program_path = 2;</code>
     * @param value The bytes for miniProgramPath to set.
     * @return This builder for chaining.
     */
    public Builder setMiniProgramPathBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      miniProgramPath_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:AdxMiniProgramInfo)
  }

  // @@protoc_insertion_point(class_scope:AdxMiniProgramInfo)
  private static final cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo();
  }

  public static cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AdxMiniProgramInfo>
      PARSER = new com.google.protobuf.AbstractParser<AdxMiniProgramInfo>() {
    @java.lang.Override
    public AdxMiniProgramInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AdxMiniProgramInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AdxMiniProgramInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.mostmob.dto.AdxMiniProgramInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

