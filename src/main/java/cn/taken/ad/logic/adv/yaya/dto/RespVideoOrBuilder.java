// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

public interface RespVideoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:RespVideo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string url = 1;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <code>string url = 1;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <code>string preImage = 2;</code>
   * @return The preImage.
   */
  java.lang.String getPreImage();
  /**
   * <code>string preImage = 2;</code>
   * @return The bytes for preImage.
   */
  com.google.protobuf.ByteString
      getPreImageBytes();

  /**
   * <code>int32 duration = 3;</code>
   * @return The duration.
   */
  int getDuration();

  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks> 
      getTracksList();
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  cn.taken.ad.logic.adv.yaya.dto.RespTracks getTracks(int index);
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  int getTracksCount();
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  java.util.List<? extends cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder> 
      getTracksOrBuilderList();
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder getTracksOrBuilder(
      int index);

  /**
   * <code>repeated string skipTracks = 5;</code>
   * @return A list containing the skipTracks.
   */
  java.util.List<java.lang.String>
      getSkipTracksList();
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @return The count of skipTracks.
   */
  int getSkipTracksCount();
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @param index The index of the element to return.
   * @return The skipTracks at the given index.
   */
  java.lang.String getSkipTracks(int index);
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @param index The index of the value to return.
   * @return The bytes of the skipTracks at the given index.
   */
  com.google.protobuf.ByteString
      getSkipTracksBytes(int index);

  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @return A list containing the pauseTracks.
   */
  java.util.List<java.lang.String>
      getPauseTracksList();
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @return The count of pauseTracks.
   */
  int getPauseTracksCount();
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @param index The index of the element to return.
   * @return The pauseTracks at the given index.
   */
  java.lang.String getPauseTracks(int index);
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the pauseTracks at the given index.
   */
  com.google.protobuf.ByteString
      getPauseTracksBytes(int index);

  /**
   * <code>repeated string stopTracks = 7;</code>
   * @return A list containing the stopTracks.
   */
  java.util.List<java.lang.String>
      getStopTracksList();
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @return The count of stopTracks.
   */
  int getStopTracksCount();
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @param index The index of the element to return.
   * @return The stopTracks at the given index.
   */
  java.lang.String getStopTracks(int index);
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @param index The index of the value to return.
   * @return The bytes of the stopTracks at the given index.
   */
  com.google.protobuf.ByteString
      getStopTracksBytes(int index);

  /**
   * <code>string afterImage = 8;</code>
   * @return The afterImage.
   */
  java.lang.String getAfterImage();
  /**
   * <code>string afterImage = 8;</code>
   * @return The bytes for afterImage.
   */
  com.google.protobuf.ByteString
      getAfterImageBytes();

  /**
   * <code>string title = 9;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 9;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <code>string desc = 10;</code>
   * @return The desc.
   */
  java.lang.String getDesc();
  /**
   * <code>string desc = 10;</code>
   * @return The bytes for desc.
   */
  com.google.protobuf.ByteString
      getDescBytes();

  /**
   * <code>string afterHtml = 11;</code>
   * @return The afterHtml.
   */
  java.lang.String getAfterHtml();
  /**
   * <code>string afterHtml = 11;</code>
   * @return The bytes for afterHtml.
   */
  com.google.protobuf.ByteString
      getAfterHtmlBytes();

  /**
   * <code>repeated string active = 12;</code>
   * @return A list containing the active.
   */
  java.util.List<java.lang.String>
      getActiveList();
  /**
   * <code>repeated string active = 12;</code>
   * @return The count of active.
   */
  int getActiveCount();
  /**
   * <code>repeated string active = 12;</code>
   * @param index The index of the element to return.
   * @return The active at the given index.
   */
  java.lang.String getActive(int index);
  /**
   * <code>repeated string active = 12;</code>
   * @param index The index of the value to return.
   * @return The bytes of the active at the given index.
   */
  com.google.protobuf.ByteString
      getActiveBytes(int index);
}
