package cn.taken.ad.logic.adv.dishuyun.dto;

import java.io.Serializable;
import java.util.List;

public class DiShYunRequestDevice implements Serializable {

    private static final long serialVersionUID = 4508314840326364184L;
    /**
     * 安卓设备唯一标识码：二选一，优先明文
     * 对于安卓系统，无 imei/imeiMd5 时，oaid、vaid 至少二选一，
     * 可同时存在
     */
    private String imei;
    private String imeiMd5;

    /**
     * iOS 设备唯一标识码：二选一，优先明文
     * 对于 iOS 系统，idfa、idfaMd5、caid 至少有一个，可同时存在
     */
    private String idfa;
    private String idfaMd5;
    /**
     * 匿名设备标识符
     * 对于安卓系统，无 imei/imeiMd5 时，oaid、vaid 至少二选一，
     * 可同时存在
     */
    private String oaid;
    private String oaidMd5;
    /**
     * 开发者匿名设备标识符
     * 对于安卓系统，无 imei/imeiMd5 时，oaid、vaid 至少二选一，
     * 可同时存在
     */
    private String vaid;
    private String vaidMd5;

    /**
     * 安卓 ID：二选一，优先明文
     */
    private String androidId;
    private String androidIdMd5;
    /**
     * IOS 建议填写
     */
    private String idfv;
    private String mac;
    private String macMd5;
    /**
     * caid 信息
     * 对于 iOS 系统，idfa、idfaMd5、caid 至少有一个，可同时存在
     */
    private List<DiShYunRequestCaid> caids;
    private String imsi;
    /**
     * 运营商识别码，如 46000、46001
     */
    private Integer mccmnc;

    private String ua;
    private String ip;
    /**
     * 设备类型
     * 0 未知
     * 1 Phone/手机
     * 2 Tablet/平板
     * 3 TV/智能电视
     * 4 PC/个人电脑
     */
    private Integer devType;
    /**
     * 操作设备类型系统类型：
     * Unknows=0
     * Android=1
     * IOS=2
     * Windows=3
     * HarmonyOS=4
     */
    private Integer os;
    /**
     * 操作系统版本。例如：7.1.3
     */
    private String osVersion;
    /**
     * 设备厂商，如 Apple、Samsung、XiaoMi
     */
    private String vendor;
    /**
     * 设备品牌，如 RedMi
     */
    private String brand;
    /**
     * 设备型号，示例：Meta P60
     */
    private String model;
    /**
     * 设备设置的语言：中文，英文，其他
     */
    private String language;
    /**
     * 横竖屏：未知=0、竖屏=1、横屏=2
     */
    private Integer orientation;
    private Integer ppi;
    /**
     * 设备屏宽
     */
    private Integer screenWidth;
    /**
     * 设备屏高
     */
    private Integer screenHeight;
    private String bootMark;
    private String updateMark;
    private String countryCode;
    private String timeZone;
    private String deviceNameMd5;
    private String hardwareMachine;
    private Long diskTotal;
    private Long memTotal;
    /**
     * 应用商店版本
     * 华为、oppo、vivo 必填
     */
    private String appStoreVersion;
    /**
     * 华为 HMS Core 华为预算必填
     */
    private String hmsCore;
    /**
     * 小米手机 MIUI 版本号
     * 小米预算必填
     */
    private String miuiVersion;
    /**
     * 拼多多预算必填
     */
    private String paid;
    /**
     * 阿里预算必填
     */
    private String aaid;
    /**
     * 系统启动时间，精度毫秒。示例:1724897542671
     */
    private String systemStartTime;
    /**
     * 系统更新时间，精度纳秒(格式: 秒.纳秒)。
     * 示例:1724897542.912750366
     */
    private String systemUpdateTime;
    /**
     * 设备初始化时间，精度纳秒(格式: 秒.纳秒)。
     * 示例:1724897542.912750366
     */
    private String deviceInitTime;

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImeiMd5() {
        return imeiMd5;
    }

    public void setImeiMd5(String imeiMd5) {
        this.imeiMd5 = imeiMd5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getIdfaMd5() {
        return idfaMd5;
    }

    public void setIdfaMd5(String idfaMd5) {
        this.idfaMd5 = idfaMd5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getOaidMd5() {
        return oaidMd5;
    }

    public void setOaidMd5(String oaidMd5) {
        this.oaidMd5 = oaidMd5;
    }

    public String getVaid() {
        return vaid;
    }

    public void setVaid(String vaid) {
        this.vaid = vaid;
    }

    public String getVaidMd5() {
        return vaidMd5;
    }

    public void setVaidMd5(String vaidMd5) {
        this.vaidMd5 = vaidMd5;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAndroidIdMd5() {
        return androidIdMd5;
    }

    public void setAndroidIdMd5(String androidIdMd5) {
        this.androidIdMd5 = androidIdMd5;
    }

    public String getIdfv() {
        return idfv;
    }

    public void setIdfv(String idfv) {
        this.idfv = idfv;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getMacMd5() {
        return macMd5;
    }

    public void setMacMd5(String macMd5) {
        this.macMd5 = macMd5;
    }

    public List<DiShYunRequestCaid> getCaids() {
        return caids;
    }

    public void setCaids(List<DiShYunRequestCaid> caids) {
        this.caids = caids;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public Integer getMccmnc() {
        return mccmnc;
    }

    public void setMccmnc(Integer mccmnc) {
        this.mccmnc = mccmnc;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getDevType() {
        return devType;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public Integer getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(Integer screenWidth) {
        this.screenWidth = screenWidth;
    }

    public Integer getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(Integer screenHeight) {
        this.screenHeight = screenHeight;
    }

    public String getBootMark() {
        return bootMark;
    }

    public void setBootMark(String bootMark) {
        this.bootMark = bootMark;
    }

    public String getUpdateMark() {
        return updateMark;
    }

    public void setUpdateMark(String updateMark) {
        this.updateMark = updateMark;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getDeviceNameMd5() {
        return deviceNameMd5;
    }

    public void setDeviceNameMd5(String deviceNameMd5) {
        this.deviceNameMd5 = deviceNameMd5;
    }

    public String getHardwareMachine() {
        return hardwareMachine;
    }

    public void setHardwareMachine(String hardwareMachine) {
        this.hardwareMachine = hardwareMachine;
    }

    public Long getDiskTotal() {
        return diskTotal;
    }

    public void setDiskTotal(Long diskTotal) {
        this.diskTotal = diskTotal;
    }

    public Long getMemTotal() {
        return memTotal;
    }

    public void setMemTotal(Long memTotal) {
        this.memTotal = memTotal;
    }

    public String getAppStoreVersion() {
        return appStoreVersion;
    }

    public void setAppStoreVersion(String appStoreVersion) {
        this.appStoreVersion = appStoreVersion;
    }

    public String getHmsCore() {
        return hmsCore;
    }

    public void setHmsCore(String hmsCore) {
        this.hmsCore = hmsCore;
    }

    public String getMiuiVersion() {
        return miuiVersion;
    }

    public void setMiuiVersion(String miuiVersion) {
        this.miuiVersion = miuiVersion;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getAaid() {
        return aaid;
    }

    public void setAaid(String aaid) {
        this.aaid = aaid;
    }

    public String getSystemStartTime() {
        return systemStartTime;
    }

    public void setSystemStartTime(String systemStartTime) {
        this.systemStartTime = systemStartTime;
    }

    public String getSystemUpdateTime() {
        return systemUpdateTime;
    }

    public void setSystemUpdateTime(String systemUpdateTime) {
        this.systemUpdateTime = systemUpdateTime;
    }

    public String getDeviceInitTime() {
        return deviceInitTime;
    }

    public void setDeviceInitTime(String deviceInitTime) {
        this.deviceInitTime = deviceInitTime;
    }
}
