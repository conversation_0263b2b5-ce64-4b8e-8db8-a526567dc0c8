// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: Request.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.request;

import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.app.TakenReqApp;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.device.TakenReqDevice;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.geo.TakenReqGeo;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.network.TakenReqNetwork;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.tag.TakenReqTag;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.user.TakenReqUser;

public final class TakenRequest {
  private TakenRequest() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TakenRequest.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_req_TakenRequestInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_req_TakenRequestInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\rRequest.proto\022\003req\032\020RequestApp.proto\032\020" +
      "RequestTag.proto\032\023RequestDevice.proto\032\024R" +
      "equestNetwork.proto\032\021RequestUser.proto\032\020" +
      "RequestGeo.proto\"\203\002\n\020TakenRequestInfo\022\r\n" +
      "\005reqId\030\001 \001(\t\022!\n\003app\030\002 \001(\0132\024.req.TakenReq" +
      "uestApp\022!\n\003tag\030\003 \001(\0132\024.req.TakenRequestT" +
      "ag\022\'\n\006device\030\004 \001(\0132\027.req.TakenRequestDev" +
      "ice\022)\n\007network\030\005 \001(\0132\030.req.TakenRequestN" +
      "etwork\022#\n\004user\030\006 \001(\0132\025.req.TakenRequestU" +
      "ser\022!\n\003geo\030\007 \001(\0132\024.req.TakenRequestGeoB@" +
      "\n.cn.taken.ad.logic.prossor.taken.v2.dto" +
      ".requestB\014TakenRequestP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          TakenReqApp.getDescriptor(),
          TakenReqTag.getDescriptor(),
          TakenReqDevice.getDescriptor(),
          TakenReqNetwork.getDescriptor(),
          TakenReqUser.getDescriptor(),
          TakenReqGeo.getDescriptor(),
        });
    internal_static_req_TakenRequestInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_req_TakenRequestInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_req_TakenRequestInfo_descriptor,
        new String[] { "ReqId", "App", "Tag", "Device", "Network", "User", "Geo", });
    descriptor.resolveAllFeaturesImmutable();
    TakenReqApp.getDescriptor();
    TakenReqTag.getDescriptor();
    TakenReqDevice.getDescriptor();
    TakenReqNetwork.getDescriptor();
    TakenReqUser.getDescriptor();
    TakenReqGeo.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
