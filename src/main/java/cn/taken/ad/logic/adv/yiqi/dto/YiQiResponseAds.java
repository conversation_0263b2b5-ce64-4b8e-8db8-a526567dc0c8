package cn.taken.ad.logic.adv.yiqi.dto;

import java.util.List;

public class YiQiResponseAds {

    /**
     * 广告标题
     */
    private String title;
    /**
     * 广告描述
     */
    private String desc;
    /**
     * “广告”字样的 logo 图标
     */
    private String logo;
    /**
     * 广告图标地址
     */
    private String icon;
    /**
     * 广告素材图片地址列表（支持一图或多图）
     */
    private List<String> imgs;
    /**
     * 落地页地址
     */
    private String click;
    /**
     * 安卓设备应用的商店下载页链接
     */
    private String market;
    /**
     * iOS 设备应用的调起链接
     */
    private String ullink;
    /**
     * deeplink 地址，如果不为空则通过此链接唤醒应用。唤醒失败则调用 clickUrl 的地址
     */
    private String dplink;
    /**
     * 本次曝光的出价，单位：元人民币/千次曝光
     */
    private Double price;
    /**
     * 下载类广告应用名称
     */
    private String app_name;
    /**
     * 下载类广告应用包名如果本地已安装，则直接打开 APP，并上报打开 APP(激活)监测
     */
    private String pkg;
    /**
     * 评分(满分 5)
     */
    private Double rate;
    /**
     * 评分次数
     */
    private Integer comment;
    /**
     * 应用版本
     */
    private String app_version;
    /**
     * 应用大小。单位：byte
     */
    private Long app_size;
    /**
     * 应用开发者名称
     */
    private String developer;
    /**
     * 应用隐私政策
     */
    private String privacy;
    /**
     * 应用权限
     */
    private String permission;
    /**
     * 落地页特殊处理
     * 0：不用特殊处理
     * 1：需要特殊处理，请参考广点通。
     */
    private Integer gdt;
    /**
     * 广告点击交互类型
     * 1：应用下载
     * 2：WebView 跳转
     * 3：站外系统浏览器打开
     */
    private Integer action;
    /**
     * 广告素材类型
     * 1：视频广告
     * 2：图文广告
     * 3：纯文字广告
     * 4：HTML 广告
     */
    private Integer material;
    /**
     * 广告 HTML 内容
     */
    private String html;
    /**
     * Track 对象列表，用于上报广告执行 情况，同一类型的 Track可能有多 个，必须全部依次上报
     */
    private List<YiQiResponseTrack> tracks;
    /**
     * 视频广告类型 ID，默认为 0
     */
    private Integer video_type;
    /**
     * 视频类型 0 对应的广告实体
     */
    private YiQiResponseVideo video;
    /**
     * 详情见 汇川预算上报
     */
    private List<String> click_area_report_url;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<String> getImgs() {
        return imgs;
    }

    public void setImgs(List<String> imgs) {
        this.imgs = imgs;
    }

    public String getClick() {
        return click;
    }

    public void setClick(String click) {
        this.click = click;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getUllink() {
        return ullink;
    }

    public void setUllink(String ullink) {
        this.ullink = ullink;
    }

    public String getDplink() {
        return dplink;
    }

    public void setDplink(String dplink) {
        this.dplink = dplink;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getPkg() {
        return pkg;
    }

    public void setPkg(String pkg) {
        this.pkg = pkg;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public Integer getComment() {
        return comment;
    }

    public void setComment(Integer comment) {
        this.comment = comment;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public Long getApp_size() {
        return app_size;
    }

    public void setApp_size(Long app_size) {
        this.app_size = app_size;
    }

    public String getDeveloper() {
        return developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public Integer getGdt() {
        return gdt;
    }

    public void setGdt(Integer gdt) {
        this.gdt = gdt;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public List<YiQiResponseTrack> getTracks() {
        return tracks;
    }

    public void setTracks(List<YiQiResponseTrack> tracks) {
        this.tracks = tracks;
    }

    public Integer getVideo_type() {
        return video_type;
    }

    public void setVideo_type(Integer video_type) {
        this.video_type = video_type;
    }

    public YiQiResponseVideo getVideo() {
        return video;
    }

    public void setVideo(YiQiResponseVideo video) {
        this.video = video;
    }

    public List<String> getClick_area_report_url() {
        return click_area_report_url;
    }

    public void setClick_area_report_url(List<String> click_area_report_url) {
        this.click_area_report_url = click_area_report_url;
    }
}
