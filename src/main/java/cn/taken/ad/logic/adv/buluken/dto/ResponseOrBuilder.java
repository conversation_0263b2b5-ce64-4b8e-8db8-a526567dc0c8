// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: adprof_req.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.buluken.dto;

public interface ResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:ad.Response)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string bidid = 2;</code>
   * @return The bidid.
   */
  java.lang.String getBidid();
  /**
   * <code>string bidid = 2;</code>
   * @return The bytes for bidid.
   */
  com.google.protobuf.ByteString
      getBididBytes();

  /**
   * <code>.ad.Response.SeatBid seatbid = 3;</code>
   * @return Whether the seatbid field is set.
   */
  boolean hasSeatbid();
  /**
   * <code>.ad.Response.SeatBid seatbid = 3;</code>
   * @return The seatbid.
   */
  cn.taken.ad.logic.adv.buluken.dto.Response.SeatBid getSeatbid();
  /**
   * <code>.ad.Response.SeatBid seatbid = 3;</code>
   */
  cn.taken.ad.logic.adv.buluken.dto.Response.SeatBidOrBuilder getSeatbidOrBuilder();

  /**
   * <code>string nbr = 4;</code>
   * @return The nbr.
   */
  java.lang.String getNbr();
  /**
   * <code>string nbr = 4;</code>
   * @return The bytes for nbr.
   */
  com.google.protobuf.ByteString
      getNbrBytes();
}
