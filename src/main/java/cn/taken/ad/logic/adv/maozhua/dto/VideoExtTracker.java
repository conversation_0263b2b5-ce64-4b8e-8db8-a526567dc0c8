package cn.taken.ad.logic.adv.maozhua.dto;

import java.io.Serializable;
import java.util.List;

public class VideoExtTracker implements Serializable {
    private Integer  check_point;
    private Float progress;
    private List<String> urls;

    public Integer getCheck_point() {
        return check_point;
    }

    public void setCheck_point(Integer check_point) {
        this.check_point = check_point;
    }

    public Float getProgress() {
        return progress;
    }

    public void setProgress(Float progress) {
        this.progress = progress;
    }

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }
}
