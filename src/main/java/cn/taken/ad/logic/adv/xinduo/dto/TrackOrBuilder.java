// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xinduo_9.0.1.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.xinduo.dto;

public interface TrackOrBuilder extends
    // @@protoc_insertion_point(interface_extends:Track)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 type = 1;</code>
   * @return The type.
   */
  int getType();

  /**
   * <code>repeated string urls = 2;</code>
   * @return A list containing the urls.
   */
  java.util.List<String>
      getUrlsList();
  /**
   * <code>repeated string urls = 2;</code>
   * @return The count of urls.
   */
  int getUrlsCount();
  /**
   * <code>repeated string urls = 2;</code>
   * @param index The index of the element to return.
   * @return The urls at the given index.
   */
  String getUrls(int index);
  /**
   * <code>repeated string urls = 2;</code>
   * @param index The index of the value to return.
   * @return The bytes of the urls at the given index.
   */
  com.google.protobuf.ByteString
      getUrlsBytes(int index);

  /**
   * <code>string method = 3;</code>
   * @return The method.
   */
  String getMethod();
  /**
   * <code>string method = 3;</code>
   * @return The bytes for method.
   */
  com.google.protobuf.ByteString
      getMethodBytes();

  /**
   * <code>string content_type = 4;</code>
   * @return The contentType.
   */
  String getContentType();
  /**
   * <code>string content_type = 4;</code>
   * @return The bytes for contentType.
   */
  com.google.protobuf.ByteString
      getContentTypeBytes();

  /**
   * <code>string content = 5;</code>
   * @return The content.
   */
  String getContent();
  /**
   * <code>string content = 5;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();
}
