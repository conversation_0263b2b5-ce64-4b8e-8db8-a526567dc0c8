// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf type {@code RespVideo}
 */
public final class RespVideo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:RespVideo)
    RespVideoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      RespVideo.class.getName());
  }
  // Use RespVideo.newBuilder() to construct.
  private RespVideo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RespVideo() {
    url_ = "";
    preImage_ = "";
    tracks_ = java.util.Collections.emptyList();
    skipTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    pauseTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    stopTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    afterImage_ = "";
    title_ = "";
    desc_ = "";
    afterHtml_ = "";
    active_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_RespVideo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_RespVideo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.yaya.dto.RespVideo.class, cn.taken.ad.logic.adv.yaya.dto.RespVideo.Builder.class);
  }

  public static final int URL_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object url_ = "";
  /**
   * <code>string url = 1;</code>
   * @return The url.
   */
  @java.lang.Override
  public java.lang.String getUrl() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      url_ = s;
      return s;
    }
  }
  /**
   * <code>string url = 1;</code>
   * @return The bytes for url.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUrlBytes() {
    java.lang.Object ref = url_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      url_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PREIMAGE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object preImage_ = "";
  /**
   * <code>string preImage = 2;</code>
   * @return The preImage.
   */
  @java.lang.Override
  public java.lang.String getPreImage() {
    java.lang.Object ref = preImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      preImage_ = s;
      return s;
    }
  }
  /**
   * <code>string preImage = 2;</code>
   * @return The bytes for preImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPreImageBytes() {
    java.lang.Object ref = preImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      preImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DURATION_FIELD_NUMBER = 3;
  private int duration_ = 0;
  /**
   * <code>int32 duration = 3;</code>
   * @return The duration.
   */
  @java.lang.Override
  public int getDuration() {
    return duration_;
  }

  public static final int TRACKS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks> tracks_;
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  @java.lang.Override
  public java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks> getTracksList() {
    return tracks_;
  }
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder> 
      getTracksOrBuilderList() {
    return tracks_;
  }
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  @java.lang.Override
  public int getTracksCount() {
    return tracks_.size();
  }
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.RespTracks getTracks(int index) {
    return tracks_.get(index);
  }
  /**
   * <code>repeated .RespTracks tracks = 4;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder getTracksOrBuilder(
      int index) {
    return tracks_.get(index);
  }

  public static final int SKIPTRACKS_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList skipTracks_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @return A list containing the skipTracks.
   */
  public com.google.protobuf.ProtocolStringList
      getSkipTracksList() {
    return skipTracks_;
  }
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @return The count of skipTracks.
   */
  public int getSkipTracksCount() {
    return skipTracks_.size();
  }
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @param index The index of the element to return.
   * @return The skipTracks at the given index.
   */
  public java.lang.String getSkipTracks(int index) {
    return skipTracks_.get(index);
  }
  /**
   * <code>repeated string skipTracks = 5;</code>
   * @param index The index of the value to return.
   * @return The bytes of the skipTracks at the given index.
   */
  public com.google.protobuf.ByteString
      getSkipTracksBytes(int index) {
    return skipTracks_.getByteString(index);
  }

  public static final int PAUSETRACKS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList pauseTracks_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @return A list containing the pauseTracks.
   */
  public com.google.protobuf.ProtocolStringList
      getPauseTracksList() {
    return pauseTracks_;
  }
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @return The count of pauseTracks.
   */
  public int getPauseTracksCount() {
    return pauseTracks_.size();
  }
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @param index The index of the element to return.
   * @return The pauseTracks at the given index.
   */
  public java.lang.String getPauseTracks(int index) {
    return pauseTracks_.get(index);
  }
  /**
   * <code>repeated string pauseTracks = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the pauseTracks at the given index.
   */
  public com.google.protobuf.ByteString
      getPauseTracksBytes(int index) {
    return pauseTracks_.getByteString(index);
  }

  public static final int STOPTRACKS_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList stopTracks_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @return A list containing the stopTracks.
   */
  public com.google.protobuf.ProtocolStringList
      getStopTracksList() {
    return stopTracks_;
  }
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @return The count of stopTracks.
   */
  public int getStopTracksCount() {
    return stopTracks_.size();
  }
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @param index The index of the element to return.
   * @return The stopTracks at the given index.
   */
  public java.lang.String getStopTracks(int index) {
    return stopTracks_.get(index);
  }
  /**
   * <code>repeated string stopTracks = 7;</code>
   * @param index The index of the value to return.
   * @return The bytes of the stopTracks at the given index.
   */
  public com.google.protobuf.ByteString
      getStopTracksBytes(int index) {
    return stopTracks_.getByteString(index);
  }

  public static final int AFTERIMAGE_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object afterImage_ = "";
  /**
   * <code>string afterImage = 8;</code>
   * @return The afterImage.
   */
  @java.lang.Override
  public java.lang.String getAfterImage() {
    java.lang.Object ref = afterImage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      afterImage_ = s;
      return s;
    }
  }
  /**
   * <code>string afterImage = 8;</code>
   * @return The bytes for afterImage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAfterImageBytes() {
    java.lang.Object ref = afterImage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      afterImage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TITLE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object title_ = "";
  /**
   * <code>string title = 9;</code>
   * @return The title.
   */
  @java.lang.Override
  public java.lang.String getTitle() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      title_ = s;
      return s;
    }
  }
  /**
   * <code>string title = 9;</code>
   * @return The bytes for title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTitleBytes() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      title_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>string desc = 10;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      desc_ = s;
      return s;
    }
  }
  /**
   * <code>string desc = 10;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AFTERHTML_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object afterHtml_ = "";
  /**
   * <code>string afterHtml = 11;</code>
   * @return The afterHtml.
   */
  @java.lang.Override
  public java.lang.String getAfterHtml() {
    java.lang.Object ref = afterHtml_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      afterHtml_ = s;
      return s;
    }
  }
  /**
   * <code>string afterHtml = 11;</code>
   * @return The bytes for afterHtml.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAfterHtmlBytes() {
    java.lang.Object ref = afterHtml_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      afterHtml_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTIVE_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList active_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string active = 12;</code>
   * @return A list containing the active.
   */
  public com.google.protobuf.ProtocolStringList
      getActiveList() {
    return active_;
  }
  /**
   * <code>repeated string active = 12;</code>
   * @return The count of active.
   */
  public int getActiveCount() {
    return active_.size();
  }
  /**
   * <code>repeated string active = 12;</code>
   * @param index The index of the element to return.
   * @return The active at the given index.
   */
  public java.lang.String getActive(int index) {
    return active_.get(index);
  }
  /**
   * <code>repeated string active = 12;</code>
   * @param index The index of the value to return.
   * @return The bytes of the active at the given index.
   */
  public com.google.protobuf.ByteString
      getActiveBytes(int index) {
    return active_.getByteString(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(preImage_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, preImage_);
    }
    if (duration_ != 0) {
      output.writeInt32(3, duration_);
    }
    for (int i = 0; i < tracks_.size(); i++) {
      output.writeMessage(4, tracks_.get(i));
    }
    for (int i = 0; i < skipTracks_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, skipTracks_.getRaw(i));
    }
    for (int i = 0; i < pauseTracks_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, pauseTracks_.getRaw(i));
    }
    for (int i = 0; i < stopTracks_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, stopTracks_.getRaw(i));
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(afterImage_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, afterImage_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, title_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(desc_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, desc_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(afterHtml_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 11, afterHtml_);
    }
    for (int i = 0; i < active_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, active_.getRaw(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(preImage_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, preImage_);
    }
    if (duration_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, duration_);
    }
    for (int i = 0; i < tracks_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, tracks_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < skipTracks_.size(); i++) {
        dataSize += computeStringSizeNoTag(skipTracks_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getSkipTracksList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < pauseTracks_.size(); i++) {
        dataSize += computeStringSizeNoTag(pauseTracks_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getPauseTracksList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < stopTracks_.size(); i++) {
        dataSize += computeStringSizeNoTag(stopTracks_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getStopTracksList().size();
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(afterImage_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, afterImage_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, title_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(desc_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, desc_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(afterHtml_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(11, afterHtml_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < active_.size(); i++) {
        dataSize += computeStringSizeNoTag(active_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getActiveList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.yaya.dto.RespVideo)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.yaya.dto.RespVideo other = (cn.taken.ad.logic.adv.yaya.dto.RespVideo) obj;

    if (!getUrl()
        .equals(other.getUrl())) return false;
    if (!getPreImage()
        .equals(other.getPreImage())) return false;
    if (getDuration()
        != other.getDuration()) return false;
    if (!getTracksList()
        .equals(other.getTracksList())) return false;
    if (!getSkipTracksList()
        .equals(other.getSkipTracksList())) return false;
    if (!getPauseTracksList()
        .equals(other.getPauseTracksList())) return false;
    if (!getStopTracksList()
        .equals(other.getStopTracksList())) return false;
    if (!getAfterImage()
        .equals(other.getAfterImage())) return false;
    if (!getTitle()
        .equals(other.getTitle())) return false;
    if (!getDesc()
        .equals(other.getDesc())) return false;
    if (!getAfterHtml()
        .equals(other.getAfterHtml())) return false;
    if (!getActiveList()
        .equals(other.getActiveList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + URL_FIELD_NUMBER;
    hash = (53 * hash) + getUrl().hashCode();
    hash = (37 * hash) + PREIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getPreImage().hashCode();
    hash = (37 * hash) + DURATION_FIELD_NUMBER;
    hash = (53 * hash) + getDuration();
    if (getTracksCount() > 0) {
      hash = (37 * hash) + TRACKS_FIELD_NUMBER;
      hash = (53 * hash) + getTracksList().hashCode();
    }
    if (getSkipTracksCount() > 0) {
      hash = (37 * hash) + SKIPTRACKS_FIELD_NUMBER;
      hash = (53 * hash) + getSkipTracksList().hashCode();
    }
    if (getPauseTracksCount() > 0) {
      hash = (37 * hash) + PAUSETRACKS_FIELD_NUMBER;
      hash = (53 * hash) + getPauseTracksList().hashCode();
    }
    if (getStopTracksCount() > 0) {
      hash = (37 * hash) + STOPTRACKS_FIELD_NUMBER;
      hash = (53 * hash) + getStopTracksList().hashCode();
    }
    hash = (37 * hash) + AFTERIMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getAfterImage().hashCode();
    hash = (37 * hash) + TITLE_FIELD_NUMBER;
    hash = (53 * hash) + getTitle().hashCode();
    hash = (37 * hash) + DESC_FIELD_NUMBER;
    hash = (53 * hash) + getDesc().hashCode();
    hash = (37 * hash) + AFTERHTML_FIELD_NUMBER;
    hash = (53 * hash) + getAfterHtml().hashCode();
    if (getActiveCount() > 0) {
      hash = (37 * hash) + ACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getActiveList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.yaya.dto.RespVideo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code RespVideo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:RespVideo)
      cn.taken.ad.logic.adv.yaya.dto.RespVideoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_RespVideo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_RespVideo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.yaya.dto.RespVideo.class, cn.taken.ad.logic.adv.yaya.dto.RespVideo.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.yaya.dto.RespVideo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      url_ = "";
      preImage_ = "";
      duration_ = 0;
      if (tracksBuilder_ == null) {
        tracks_ = java.util.Collections.emptyList();
      } else {
        tracks_ = null;
        tracksBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      skipTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      pauseTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      stopTracks_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      afterImage_ = "";
      title_ = "";
      desc_ = "";
      afterHtml_ = "";
      active_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_RespVideo_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.RespVideo getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.yaya.dto.RespVideo.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.RespVideo build() {
      cn.taken.ad.logic.adv.yaya.dto.RespVideo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.RespVideo buildPartial() {
      cn.taken.ad.logic.adv.yaya.dto.RespVideo result = new cn.taken.ad.logic.adv.yaya.dto.RespVideo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(cn.taken.ad.logic.adv.yaya.dto.RespVideo result) {
      if (tracksBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          tracks_ = java.util.Collections.unmodifiableList(tracks_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.tracks_ = tracks_;
      } else {
        result.tracks_ = tracksBuilder_.build();
      }
    }

    private void buildPartial0(cn.taken.ad.logic.adv.yaya.dto.RespVideo result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.url_ = url_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.preImage_ = preImage_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.duration_ = duration_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        skipTracks_.makeImmutable();
        result.skipTracks_ = skipTracks_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        pauseTracks_.makeImmutable();
        result.pauseTracks_ = pauseTracks_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        stopTracks_.makeImmutable();
        result.stopTracks_ = stopTracks_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.afterImage_ = afterImage_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.title_ = title_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.desc_ = desc_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.afterHtml_ = afterHtml_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        active_.makeImmutable();
        result.active_ = active_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.yaya.dto.RespVideo) {
        return mergeFrom((cn.taken.ad.logic.adv.yaya.dto.RespVideo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.yaya.dto.RespVideo other) {
      if (other == cn.taken.ad.logic.adv.yaya.dto.RespVideo.getDefaultInstance()) return this;
      if (!other.getUrl().isEmpty()) {
        url_ = other.url_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getPreImage().isEmpty()) {
        preImage_ = other.preImage_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.getDuration() != 0) {
        setDuration(other.getDuration());
      }
      if (tracksBuilder_ == null) {
        if (!other.tracks_.isEmpty()) {
          if (tracks_.isEmpty()) {
            tracks_ = other.tracks_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureTracksIsMutable();
            tracks_.addAll(other.tracks_);
          }
          onChanged();
        }
      } else {
        if (!other.tracks_.isEmpty()) {
          if (tracksBuilder_.isEmpty()) {
            tracksBuilder_.dispose();
            tracksBuilder_ = null;
            tracks_ = other.tracks_;
            bitField0_ = (bitField0_ & ~0x00000008);
            tracksBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getTracksFieldBuilder() : null;
          } else {
            tracksBuilder_.addAllMessages(other.tracks_);
          }
        }
      }
      if (!other.skipTracks_.isEmpty()) {
        if (skipTracks_.isEmpty()) {
          skipTracks_ = other.skipTracks_;
          bitField0_ |= 0x00000010;
        } else {
          ensureSkipTracksIsMutable();
          skipTracks_.addAll(other.skipTracks_);
        }
        onChanged();
      }
      if (!other.pauseTracks_.isEmpty()) {
        if (pauseTracks_.isEmpty()) {
          pauseTracks_ = other.pauseTracks_;
          bitField0_ |= 0x00000020;
        } else {
          ensurePauseTracksIsMutable();
          pauseTracks_.addAll(other.pauseTracks_);
        }
        onChanged();
      }
      if (!other.stopTracks_.isEmpty()) {
        if (stopTracks_.isEmpty()) {
          stopTracks_ = other.stopTracks_;
          bitField0_ |= 0x00000040;
        } else {
          ensureStopTracksIsMutable();
          stopTracks_.addAll(other.stopTracks_);
        }
        onChanged();
      }
      if (!other.getAfterImage().isEmpty()) {
        afterImage_ = other.afterImage_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getTitle().isEmpty()) {
        title_ = other.title_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getDesc().isEmpty()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getAfterHtml().isEmpty()) {
        afterHtml_ = other.afterHtml_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (!other.active_.isEmpty()) {
        if (active_.isEmpty()) {
          active_ = other.active_;
          bitField0_ |= 0x00000800;
        } else {
          ensureActiveIsMutable();
          active_.addAll(other.active_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              url_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              preImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              duration_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              cn.taken.ad.logic.adv.yaya.dto.RespTracks m =
                  input.readMessage(
                      cn.taken.ad.logic.adv.yaya.dto.RespTracks.parser(),
                      extensionRegistry);
              if (tracksBuilder_ == null) {
                ensureTracksIsMutable();
                tracks_.add(m);
              } else {
                tracksBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureSkipTracksIsMutable();
              skipTracks_.add(s);
              break;
            } // case 42
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();
              ensurePauseTracksIsMutable();
              pauseTracks_.add(s);
              break;
            } // case 50
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureStopTracksIsMutable();
              stopTracks_.add(s);
              break;
            } // case 58
            case 66: {
              afterImage_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              title_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              desc_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              afterHtml_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureActiveIsMutable();
              active_.add(s);
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object url_ = "";
    /**
     * <code>string url = 1;</code>
     * @return The url.
     */
    public java.lang.String getUrl() {
      java.lang.Object ref = url_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        url_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string url = 1;</code>
     * @return The bytes for url.
     */
    public com.google.protobuf.ByteString
        getUrlBytes() {
      java.lang.Object ref = url_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        url_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string url = 1;</code>
     * @param value The url to set.
     * @return This builder for chaining.
     */
    public Builder setUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      url_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string url = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUrl() {
      url_ = getDefaultInstance().getUrl();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string url = 1;</code>
     * @param value The bytes for url to set.
     * @return This builder for chaining.
     */
    public Builder setUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      url_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object preImage_ = "";
    /**
     * <code>string preImage = 2;</code>
     * @return The preImage.
     */
    public java.lang.String getPreImage() {
      java.lang.Object ref = preImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        preImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string preImage = 2;</code>
     * @return The bytes for preImage.
     */
    public com.google.protobuf.ByteString
        getPreImageBytes() {
      java.lang.Object ref = preImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        preImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string preImage = 2;</code>
     * @param value The preImage to set.
     * @return This builder for chaining.
     */
    public Builder setPreImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      preImage_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string preImage = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPreImage() {
      preImage_ = getDefaultInstance().getPreImage();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string preImage = 2;</code>
     * @param value The bytes for preImage to set.
     * @return This builder for chaining.
     */
    public Builder setPreImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      preImage_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int duration_ ;
    /**
     * <code>int32 duration = 3;</code>
     * @return The duration.
     */
    @java.lang.Override
    public int getDuration() {
      return duration_;
    }
    /**
     * <code>int32 duration = 3;</code>
     * @param value The duration to set.
     * @return This builder for chaining.
     */
    public Builder setDuration(int value) {

      duration_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>int32 duration = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDuration() {
      bitField0_ = (bitField0_ & ~0x00000004);
      duration_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks> tracks_ =
      java.util.Collections.emptyList();
    private void ensureTracksIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        tracks_ = new java.util.ArrayList<cn.taken.ad.logic.adv.yaya.dto.RespTracks>(tracks_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.RespTracks, cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder, cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder> tracksBuilder_;

    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks> getTracksList() {
      if (tracksBuilder_ == null) {
        return java.util.Collections.unmodifiableList(tracks_);
      } else {
        return tracksBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public int getTracksCount() {
      if (tracksBuilder_ == null) {
        return tracks_.size();
      } else {
        return tracksBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.RespTracks getTracks(int index) {
      if (tracksBuilder_ == null) {
        return tracks_.get(index);
      } else {
        return tracksBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder setTracks(
        int index, cn.taken.ad.logic.adv.yaya.dto.RespTracks value) {
      if (tracksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTracksIsMutable();
        tracks_.set(index, value);
        onChanged();
      } else {
        tracksBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder setTracks(
        int index, cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder builderForValue) {
      if (tracksBuilder_ == null) {
        ensureTracksIsMutable();
        tracks_.set(index, builderForValue.build());
        onChanged();
      } else {
        tracksBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder addTracks(cn.taken.ad.logic.adv.yaya.dto.RespTracks value) {
      if (tracksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTracksIsMutable();
        tracks_.add(value);
        onChanged();
      } else {
        tracksBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder addTracks(
        int index, cn.taken.ad.logic.adv.yaya.dto.RespTracks value) {
      if (tracksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTracksIsMutable();
        tracks_.add(index, value);
        onChanged();
      } else {
        tracksBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder addTracks(
        cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder builderForValue) {
      if (tracksBuilder_ == null) {
        ensureTracksIsMutable();
        tracks_.add(builderForValue.build());
        onChanged();
      } else {
        tracksBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder addTracks(
        int index, cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder builderForValue) {
      if (tracksBuilder_ == null) {
        ensureTracksIsMutable();
        tracks_.add(index, builderForValue.build());
        onChanged();
      } else {
        tracksBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder addAllTracks(
        java.lang.Iterable<? extends cn.taken.ad.logic.adv.yaya.dto.RespTracks> values) {
      if (tracksBuilder_ == null) {
        ensureTracksIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, tracks_);
        onChanged();
      } else {
        tracksBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder clearTracks() {
      if (tracksBuilder_ == null) {
        tracks_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        tracksBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public Builder removeTracks(int index) {
      if (tracksBuilder_ == null) {
        ensureTracksIsMutable();
        tracks_.remove(index);
        onChanged();
      } else {
        tracksBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder getTracksBuilder(
        int index) {
      return getTracksFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder getTracksOrBuilder(
        int index) {
      if (tracksBuilder_ == null) {
        return tracks_.get(index);  } else {
        return tracksBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public java.util.List<? extends cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder> 
         getTracksOrBuilderList() {
      if (tracksBuilder_ != null) {
        return tracksBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(tracks_);
      }
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder addTracksBuilder() {
      return getTracksFieldBuilder().addBuilder(
          cn.taken.ad.logic.adv.yaya.dto.RespTracks.getDefaultInstance());
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder addTracksBuilder(
        int index) {
      return getTracksFieldBuilder().addBuilder(
          index, cn.taken.ad.logic.adv.yaya.dto.RespTracks.getDefaultInstance());
    }
    /**
     * <code>repeated .RespTracks tracks = 4;</code>
     */
    public java.util.List<cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder> 
         getTracksBuilderList() {
      return getTracksFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.RespTracks, cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder, cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder> 
        getTracksFieldBuilder() {
      if (tracksBuilder_ == null) {
        tracksBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.RespTracks, cn.taken.ad.logic.adv.yaya.dto.RespTracks.Builder, cn.taken.ad.logic.adv.yaya.dto.RespTracksOrBuilder>(
                tracks_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        tracks_ = null;
      }
      return tracksBuilder_;
    }

    private com.google.protobuf.LazyStringArrayList skipTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureSkipTracksIsMutable() {
      if (!skipTracks_.isModifiable()) {
        skipTracks_ = new com.google.protobuf.LazyStringArrayList(skipTracks_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @return A list containing the skipTracks.
     */
    public com.google.protobuf.ProtocolStringList
        getSkipTracksList() {
      skipTracks_.makeImmutable();
      return skipTracks_;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @return The count of skipTracks.
     */
    public int getSkipTracksCount() {
      return skipTracks_.size();
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param index The index of the element to return.
     * @return The skipTracks at the given index.
     */
    public java.lang.String getSkipTracks(int index) {
      return skipTracks_.get(index);
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param index The index of the value to return.
     * @return The bytes of the skipTracks at the given index.
     */
    public com.google.protobuf.ByteString
        getSkipTracksBytes(int index) {
      return skipTracks_.getByteString(index);
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param index The index to set the value at.
     * @param value The skipTracks to set.
     * @return This builder for chaining.
     */
    public Builder setSkipTracks(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureSkipTracksIsMutable();
      skipTracks_.set(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param value The skipTracks to add.
     * @return This builder for chaining.
     */
    public Builder addSkipTracks(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureSkipTracksIsMutable();
      skipTracks_.add(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param values The skipTracks to add.
     * @return This builder for chaining.
     */
    public Builder addAllSkipTracks(
        java.lang.Iterable<java.lang.String> values) {
      ensureSkipTracksIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, skipTracks_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkipTracks() {
      skipTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000010);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string skipTracks = 5;</code>
     * @param value The bytes of the skipTracks to add.
     * @return This builder for chaining.
     */
    public Builder addSkipTracksBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureSkipTracksIsMutable();
      skipTracks_.add(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList pauseTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensurePauseTracksIsMutable() {
      if (!pauseTracks_.isModifiable()) {
        pauseTracks_ = new com.google.protobuf.LazyStringArrayList(pauseTracks_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @return A list containing the pauseTracks.
     */
    public com.google.protobuf.ProtocolStringList
        getPauseTracksList() {
      pauseTracks_.makeImmutable();
      return pauseTracks_;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @return The count of pauseTracks.
     */
    public int getPauseTracksCount() {
      return pauseTracks_.size();
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param index The index of the element to return.
     * @return The pauseTracks at the given index.
     */
    public java.lang.String getPauseTracks(int index) {
      return pauseTracks_.get(index);
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the pauseTracks at the given index.
     */
    public com.google.protobuf.ByteString
        getPauseTracksBytes(int index) {
      return pauseTracks_.getByteString(index);
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param index The index to set the value at.
     * @param value The pauseTracks to set.
     * @return This builder for chaining.
     */
    public Builder setPauseTracks(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensurePauseTracksIsMutable();
      pauseTracks_.set(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param value The pauseTracks to add.
     * @return This builder for chaining.
     */
    public Builder addPauseTracks(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensurePauseTracksIsMutable();
      pauseTracks_.add(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param values The pauseTracks to add.
     * @return This builder for chaining.
     */
    public Builder addAllPauseTracks(
        java.lang.Iterable<java.lang.String> values) {
      ensurePauseTracksIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, pauseTracks_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPauseTracks() {
      pauseTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000020);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string pauseTracks = 6;</code>
     * @param value The bytes of the pauseTracks to add.
     * @return This builder for chaining.
     */
    public Builder addPauseTracksBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensurePauseTracksIsMutable();
      pauseTracks_.add(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList stopTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureStopTracksIsMutable() {
      if (!stopTracks_.isModifiable()) {
        stopTracks_ = new com.google.protobuf.LazyStringArrayList(stopTracks_);
      }
      bitField0_ |= 0x00000040;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @return A list containing the stopTracks.
     */
    public com.google.protobuf.ProtocolStringList
        getStopTracksList() {
      stopTracks_.makeImmutable();
      return stopTracks_;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @return The count of stopTracks.
     */
    public int getStopTracksCount() {
      return stopTracks_.size();
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param index The index of the element to return.
     * @return The stopTracks at the given index.
     */
    public java.lang.String getStopTracks(int index) {
      return stopTracks_.get(index);
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param index The index of the value to return.
     * @return The bytes of the stopTracks at the given index.
     */
    public com.google.protobuf.ByteString
        getStopTracksBytes(int index) {
      return stopTracks_.getByteString(index);
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param index The index to set the value at.
     * @param value The stopTracks to set.
     * @return This builder for chaining.
     */
    public Builder setStopTracks(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureStopTracksIsMutable();
      stopTracks_.set(index, value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param value The stopTracks to add.
     * @return This builder for chaining.
     */
    public Builder addStopTracks(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureStopTracksIsMutable();
      stopTracks_.add(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param values The stopTracks to add.
     * @return This builder for chaining.
     */
    public Builder addAllStopTracks(
        java.lang.Iterable<java.lang.String> values) {
      ensureStopTracksIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, stopTracks_);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearStopTracks() {
      stopTracks_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000040);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string stopTracks = 7;</code>
     * @param value The bytes of the stopTracks to add.
     * @return This builder for chaining.
     */
    public Builder addStopTracksBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureStopTracksIsMutable();
      stopTracks_.add(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object afterImage_ = "";
    /**
     * <code>string afterImage = 8;</code>
     * @return The afterImage.
     */
    public java.lang.String getAfterImage() {
      java.lang.Object ref = afterImage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        afterImage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string afterImage = 8;</code>
     * @return The bytes for afterImage.
     */
    public com.google.protobuf.ByteString
        getAfterImageBytes() {
      java.lang.Object ref = afterImage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        afterImage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string afterImage = 8;</code>
     * @param value The afterImage to set.
     * @return This builder for chaining.
     */
    public Builder setAfterImage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      afterImage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string afterImage = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearAfterImage() {
      afterImage_ = getDefaultInstance().getAfterImage();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string afterImage = 8;</code>
     * @param value The bytes for afterImage to set.
     * @return This builder for chaining.
     */
    public Builder setAfterImageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      afterImage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object title_ = "";
    /**
     * <code>string title = 9;</code>
     * @return The title.
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string title = 9;</code>
     * @return The bytes for title.
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string title = 9;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string title = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      title_ = getDefaultInstance().getTitle();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string title = 9;</code>
     * @param value The bytes for title to set.
     * @return This builder for chaining.
     */
    public Builder setTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      title_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>string desc = 10;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string desc = 10;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string desc = 10;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>string desc = 10;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      desc_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object afterHtml_ = "";
    /**
     * <code>string afterHtml = 11;</code>
     * @return The afterHtml.
     */
    public java.lang.String getAfterHtml() {
      java.lang.Object ref = afterHtml_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        afterHtml_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string afterHtml = 11;</code>
     * @return The bytes for afterHtml.
     */
    public com.google.protobuf.ByteString
        getAfterHtmlBytes() {
      java.lang.Object ref = afterHtml_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        afterHtml_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string afterHtml = 11;</code>
     * @param value The afterHtml to set.
     * @return This builder for chaining.
     */
    public Builder setAfterHtml(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      afterHtml_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>string afterHtml = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearAfterHtml() {
      afterHtml_ = getDefaultInstance().getAfterHtml();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>string afterHtml = 11;</code>
     * @param value The bytes for afterHtml to set.
     * @return This builder for chaining.
     */
    public Builder setAfterHtmlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      afterHtml_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList active_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureActiveIsMutable() {
      if (!active_.isModifiable()) {
        active_ = new com.google.protobuf.LazyStringArrayList(active_);
      }
      bitField0_ |= 0x00000800;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @return A list containing the active.
     */
    public com.google.protobuf.ProtocolStringList
        getActiveList() {
      active_.makeImmutable();
      return active_;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @return The count of active.
     */
    public int getActiveCount() {
      return active_.size();
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param index The index of the element to return.
     * @return The active at the given index.
     */
    public java.lang.String getActive(int index) {
      return active_.get(index);
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param index The index of the value to return.
     * @return The bytes of the active at the given index.
     */
    public com.google.protobuf.ByteString
        getActiveBytes(int index) {
      return active_.getByteString(index);
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param index The index to set the value at.
     * @param value The active to set.
     * @return This builder for chaining.
     */
    public Builder setActive(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureActiveIsMutable();
      active_.set(index, value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param value The active to add.
     * @return This builder for chaining.
     */
    public Builder addActive(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureActiveIsMutable();
      active_.add(value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param values The active to add.
     * @return This builder for chaining.
     */
    public Builder addAllActive(
        java.lang.Iterable<java.lang.String> values) {
      ensureActiveIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, active_);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearActive() {
      active_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000800);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string active = 12;</code>
     * @param value The bytes of the active to add.
     * @return This builder for chaining.
     */
    public Builder addActiveBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureActiveIsMutable();
      active_.add(value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:RespVideo)
  }

  // @@protoc_insertion_point(class_scope:RespVideo)
  private static final cn.taken.ad.logic.adv.yaya.dto.RespVideo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.yaya.dto.RespVideo();
  }

  public static cn.taken.ad.logic.adv.yaya.dto.RespVideo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RespVideo>
      PARSER = new com.google.protobuf.AbstractParser<RespVideo>() {
    @java.lang.Override
    public RespVideo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RespVideo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RespVideo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.RespVideo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

