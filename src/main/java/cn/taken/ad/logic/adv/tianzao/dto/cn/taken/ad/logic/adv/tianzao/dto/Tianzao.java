// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: tianzao.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tianzao.dto;

public final class Tianzao {
  private Tianzao() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      Tianzao.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_Imp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_Imp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_App_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_App_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_User_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_User_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_Device_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_Device_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_Geo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_Geo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_Network_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_Network_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRequest_CaidList_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidRequest_CaidList_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidResponse_Bid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidResponse_Bid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidResponse_Bid_App_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidResponse_Bid_App_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidResponse_Bid_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidResponse_Bid_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidResponse_Bid_Tracker_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_BidResponse_Bid_Tracker_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rtianzao.proto\"\202\027\n\nBidRequest\022\n\n\002id\030\001 \001" +
      "(\t\022\013\n\003ver\030\002 \001(\t\022\034\n\003imp\030\003 \001(\0132\017.BidReques" +
      "t.Imp\022\034\n\003app\030\004 \001(\0132\017.BidRequest.App\022#\n\004u" +
      "ser\030\005 \001(\0132\020.BidRequest.UserH\000\210\001\001\022\"\n\006devi" +
      "ce\030\006 \001(\0132\022.BidRequest.Device\032\240\001\n\003Imp\022\r\n\005" +
      "tagId\030\001 \001(\t\022\t\n\001w\030\002 \001(\021\022\t\n\001h\030\003 \001(\021\022\014\n\004typ" +
      "e\030\004 \001(\021\022\013\n\003pos\030\005 \001(\021\022\r\n\005cType\030\006 \003(\021\022\016\n\006c" +
      "iType\030\007 \003(\021\022\017\n\002dp\030\010 \001(\021H\000\210\001\001\022\025\n\010bidFloor" +
      "\030\t \001(\005H\001\210\001\001B\005\n\003_dpB\013\n\t_bidFloor\032T\n\003App\022\014" +
      "\n\004name\030\001 \001(\t\022\016\n\006bundle\030\002 \001(\t\022\013\n\003ver\030\003 \001(" +
      "\t\022\025\n\010storeUrl\030\004 \001(\tH\000\210\001\001B\013\n\t_storeUrl\032|\n" +
      "\004User\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\020\n\003yob\030\002 \001(\tH\001\210\001\001" +
      "\022\023\n\006gender\030\003 \001(\021H\002\210\001\001\022\025\n\010keywords\030\004 \001(\tH" +
      "\003\210\001\001B\005\n\003_idB\006\n\004_yobB\t\n\007_genderB\013\n\t_keywo" +
      "rds\032\217\017\n\006Device\022\n\n\002ip\030\001 \001(\t\022\021\n\004ipv6\030\002 \001(\t" +
      "H\000\210\001\001\022\n\n\002ua\030\003 \001(\t\022\n\n\002os\030\004 \001(\t\022\013\n\003osv\030\005 \001" +
      "(\t\022\022\n\ndeviceType\030\006 \001(\021\022\034\n\003geo\030\007 \001(\0132\017.Bi" +
      "dRequest.Geo\022$\n\007network\030\010 \001(\0132\023.BidReque" +
      "st.Network\022\r\n\005brand\030\t \001(\t\022\r\n\005model\030\n \001(\t" +
      "\022\021\n\tmodelCode\030\013 \001(\t\022\023\n\013orientation\030\014 \001(\021" +
      "\022\n\n\002dw\030\r \001(\021\022\n\n\002dh\030\016 \001(\021\022\017\n\007density\030\017 \001(" +
      "\001\022\013\n\003ppi\030\020 \001(\021\022\022\n\nscreenSize\030\021 \001(\001\022\025\n\010se" +
      "rialno\030\022 \001(\tH\001\210\001\001\022\021\n\004anId\030\023 \001(\tH\002\210\001\001\022\024\n\007" +
      "anIdMd5\030\024 \001(\tH\003\210\001\001\022\021\n\004imei\030\025 \001(\tH\004\210\001\001\022\024\n" +
      "\007imeiMd5\030\026 \001(\tH\005\210\001\001\022\021\n\004oaid\030\027 \001(\tH\006\210\001\001\022\024" +
      "\n\007oaidMd5\030\030 \001(\tH\007\210\001\001\022\025\n\010apiLevel\030\031 \001(\tH\010" +
      "\210\001\001\022\021\n\004paid\030\032 \001(\tH\t\210\001\001\022\021\n\004idfa\030\033 \001(\tH\n\210\001" +
      "\001\022\024\n\007idfaMd5\030\034 \001(\tH\013\210\001\001\022\021\n\004caid\030\035 \001(\tH\014\210" +
      "\001\001\022\024\n\007caidMd5\030\036 \001(\tH\r\210\001\001\022\024\n\007caidVer\030\037 \001(" +
      "\tH\016\210\001\001\022\021\n\004idfv\030  \001(\tH\017\210\001\001\022\024\n\007idfvMd5\030! \001" +
      "(\tH\020\210\001\001\022\025\n\010openUdid\030\" \001(\tH\021\210\001\001\022\027\n\ndevice" +
      "Name\030# \001(\tH\022\210\001\001\022\032\n\rdeviceNameMd5\030$ \001(\tH\023" +
      "\210\001\001\022\025\n\010language\030% \001(\tH\024\210\001\001\022\024\n\007country\030& " +
      "\001(\tH\025\210\001\001\022\023\n\006romVer\030\' \001(\tH\026\210\001\001\022\034\n\017sysComp" +
      "lingTime\030( \001(\tH\027\210\001\001\022\025\n\010bootTime\030) \001(\021H\030\210" +
      "\001\001\022\027\n\nupdateTime\030* \001(\021H\031\210\001\001\022\025\n\010initTime\030" +
      "+ \001(\tH\032\210\001\001\022\025\n\010diskSize\030, \001(\021H\033\210\001\001\022\027\n\nmem" +
      "orySize\030- \001(\021H\034\210\001\001\022\032\n\rbatteryStatus\030. \001(" +
      "\021H\035\210\001\001\022\031\n\014batteryPower\030/ \001(\021H\036\210\001\001\022\023\n\006cpu" +
      "Num\0300 \001(\021H\037\210\001\001\022\023\n\006cpuFre\0301 \001(\001H \210\001\001\022\025\n\010t" +
      "imeZone\0302 \001(\tH!\210\001\001\022\020\n\003lmt\0303 \001(\021H\"\210\001\001\022\022\n\005" +
      "laccu\0304 \001(\021H#\210\001\001\022\020\n\010bootMark\0305 \001(\t\022\022\n\nup" +
      "dateMark\0306 \001(\t\022\030\n\013appStoreVer\0307 \001(\tH$\210\001\001" +
      "\022\023\n\006hmsVer\0308 \001(\tH%\210\001\001\022\026\n\016skadnetworkVer\030" +
      "9 \003(\t\022\024\n\014installedApp\030: \003(\t\022\017\n\002t2\030; \001(\tH" +
      "&\210\001\001\022\017\n\002t8\030< \001(\tH\'\210\001\001\022\020\n\003kid\030= \001(\tH(\210\001\001\022" +
      "\027\n\ncaidVendor\030> \001(\021H)\210\001\001\022\031\n\014bootTimeNano" +
      "\030? \001(\tH*\210\001\001\022\033\n\016updateTimeNano\030@ \001(\tH+\210\001\001" +
      "\022&\n\010caidList\030A \003(\0132\024.BidRequest.CaidList" +
      "B\007\n\005_ipv6B\013\n\t_serialnoB\007\n\005_anIdB\n\n\010_anId" +
      "Md5B\007\n\005_imeiB\n\n\010_imeiMd5B\007\n\005_oaidB\n\n\010_oa" +
      "idMd5B\013\n\t_apiLevelB\007\n\005_paidB\007\n\005_idfaB\n\n\010" +
      "_idfaMd5B\007\n\005_caidB\n\n\010_caidMd5B\n\n\010_caidVe" +
      "rB\007\n\005_idfvB\n\n\010_idfvMd5B\013\n\t_openUdidB\r\n\013_" +
      "deviceNameB\020\n\016_deviceNameMd5B\013\n\t_languag" +
      "eB\n\n\010_countryB\t\n\007_romVerB\022\n\020_sysCompling" +
      "TimeB\013\n\t_bootTimeB\r\n\013_updateTimeB\013\n\t_ini" +
      "tTimeB\013\n\t_diskSizeB\r\n\013_memorySizeB\020\n\016_ba" +
      "tteryStatusB\017\n\r_batteryPowerB\t\n\007_cpuNumB" +
      "\t\n\007_cpuFreB\013\n\t_timeZoneB\006\n\004_lmtB\010\n\006_lacc" +
      "uB\016\n\014_appStoreVerB\t\n\007_hmsVerB\005\n\003_t2B\005\n\003_" +
      "t8B\006\n\004_kidB\r\n\013_caidVendorB\017\n\r_bootTimeNa" +
      "noB\021\n\017_updateTimeNano\032\217\001\n\003Geo\022\013\n\003lat\030\001 \001" +
      "(\001\022\013\n\003lon\030\002 \001(\001\022\014\n\004type\030\003 \001(\021\022\024\n\007country" +
      "\030\004 \001(\tH\000\210\001\001\022\025\n\010province\030\005 \001(\tH\001\210\001\001\022\021\n\004ci" +
      "ty\030\006 \001(\tH\002\210\001\001B\n\n\010_countryB\013\n\t_provinceB\007" +
      "\n\005_city\032\363\001\n\007Network\022\017\n\007conType\030\001 \001(\021\022\017\n\007" +
      "carrier\030\002 \001(\021\022\021\n\004imsi\030\003 \001(\tH\000\210\001\001\022\020\n\003mcc\030" +
      "\004 \001(\tH\001\210\001\001\022\020\n\003mnc\030\005 \001(\tH\002\210\001\001\022\020\n\003mac\030\006 \001(" +
      "\tH\003\210\001\001\022\023\n\006macMd5\030\007 \001(\tH\004\210\001\001\022\021\n\004ssid\030\010 \001(" +
      "\tH\005\210\001\001\022\024\n\007wifiMac\030\t \001(\tH\006\210\001\001B\007\n\005_imsiB\006\n" +
      "\004_mccB\006\n\004_mncB\006\n\004_macB\t\n\007_macMd5B\007\n\005_ssi" +
      "dB\n\n\010_wifiMac\032<\n\010CaidList\022\017\n\002id\030\001 \001(\tH\000\210" +
      "\001\001\022\020\n\003ver\030\002 \001(\tH\001\210\001\001B\005\n\003_idB\006\n\004_verB\007\n\005_" +
      "user\"\334\n\n\013BidResponse\022\n\n\002id\030\001 \001(\t\022\014\n\004code" +
      "\030\002 \001(\021\022\013\n\003msg\030\003 \001(\t\022\"\n\003bid\030\004 \001(\0132\020.BidRe" +
      "sponse.BidH\000\210\001\001\032\371\t\n\003Bid\022\022\n\005bidId\030\001 \001(\tH\000" +
      "\210\001\001\022\r\n\005tagId\030\002 \001(\t\022\022\n\005title\030\003 \001(\tH\001\210\001\001\022\021" +
      "\n\004desc\030\004 \001(\tH\002\210\001\001\022\017\n\007iconUrl\030\005 \001(\t\022\017\n\007im" +
      "gUrls\030\006 \003(\t\022\016\n\001w\030\007 \001(\021H\003\210\001\001\022\016\n\001h\030\010 \001(\021H\004" +
      "\210\001\001\022\022\n\nlandingUrl\030\t \001(\t\022\025\n\010deeplink\030\n \001(" +
      "\tH\005\210\001\001\022\030\n\013downloadUrl\030\013 \001(\tH\006\210\001\001\022\025\n\010bidF" +
      "loor\030\014 \001(\021H\007\210\001\001\022\017\n\007winUrls\030\r \003(\t\022\020\n\010lose" +
      "Urls\030\016 \003(\t\022\022\n\005cType\030\017 \001(\021H\010\210\001\001\022\023\n\006ciType" +
      "\030\020 \001(\021H\t\210\001\001\022&\n\003app\030\021 \001(\0132\024.BidResponse.B" +
      "id.AppH\n\210\001\001\022*\n\005video\030\022 \001(\0132\026.BidResponse" +
      ".Bid.VideoH\013\210\001\001\022*\n\010trackers\030\023 \003(\0132\030.BidR" +
      "esponse.Bid.Tracker\022\032\n\runiversalLink\030\024 \001" +
      "(\tH\014\210\001\001\022\033\n\023clickAreaReportUrls\030\025 \003(\t\032\301\001\n" +
      "\003App\022\014\n\004name\030\001 \001(\t\022\021\n\004size\030\003 \001(\021H\000\210\001\001\022\020\n" +
      "\003ver\030\004 \001(\tH\001\210\001\001\022\027\n\nprivacyUrl\030\005 \001(\tH\002\210\001\001" +
      "\022\030\n\013permContent\030\006 \001(\tH\003\210\001\001\022\026\n\tdeveloper\030" +
      "\007 \001(\tH\004\210\001\001B\007\n\005_sizeB\006\n\004_verB\r\n\013_privacyU" +
      "rlB\016\n\014_permContentB\014\n\n_developer\032\215\003\n\005Vid" +
      "eo\022\013\n\003url\030\001 \001(\t\022\020\n\010duration\030\002 \001(\021\022\021\n\004siz" +
      "e\030\003 \001(\022H\000\210\001\001\022\017\n\002vw\030\004 \001(\021H\001\210\001\001\022\017\n\002vh\030\005 \001(" +
      "\021H\002\210\001\001\022\023\n\013coverImgUrl\030\006 \003(\t\022\027\n\nbuttonTex" +
      "t\030\007 \001(\tH\003\210\001\001\022\026\n\tendImgUrl\030\010 \001(\tH\004\210\001\001\022\024\n\007" +
      "endHtml\030\t \001(\tH\005\210\001\001\022\024\n\007skipSec\030\n \001(\021H\006\210\001\001" +
      "\022\027\n\ncomLanding\030\013 \001(\010H\007\210\001\001\022\027\n\nproLanding\030" +
      "\014 \001(\010H\010\210\001\001\022\025\n\010prefetch\030\r \001(\010H\t\210\001\001B\007\n\005_si" +
      "zeB\005\n\003_vwB\005\n\003_vhB\r\n\013_buttonTextB\014\n\n_endI" +
      "mgUrlB\n\n\010_endHtmlB\n\n\010_skipSecB\r\n\013_comLan" +
      "dingB\r\n\013_proLandingB\013\n\t_prefetch\032%\n\007Trac" +
      "ker\022\014\n\004type\030\001 \001(\021\022\014\n\004urls\030\002 \003(\tB\010\n\006_bidI" +
      "dB\010\n\006_titleB\007\n\005_descB\004\n\002_wB\004\n\002_hB\013\n\t_dee" +
      "plinkB\016\n\014_downloadUrlB\013\n\t_bidFloorB\010\n\006_c" +
      "TypeB\t\n\007_ciTypeB\006\n\004_appB\010\n\006_videoB\020\n\016_un" +
      "iversalLinkB\006\n\004_bidB%\n!cn.taken.ad.logic" +
      ".adv.tianzao.dtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_BidRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_BidRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_descriptor,
        new java.lang.String[] { "Id", "Ver", "Imp", "App", "User", "Device", });
    internal_static_BidRequest_Imp_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(0);
    internal_static_BidRequest_Imp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_Imp_descriptor,
        new java.lang.String[] { "TagId", "W", "H", "Type", "Pos", "CType", "CiType", "Dp", "BidFloor", });
    internal_static_BidRequest_App_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(1);
    internal_static_BidRequest_App_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_App_descriptor,
        new java.lang.String[] { "Name", "Bundle", "Ver", "StoreUrl", });
    internal_static_BidRequest_User_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(2);
    internal_static_BidRequest_User_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_User_descriptor,
        new java.lang.String[] { "Id", "Yob", "Gender", "Keywords", });
    internal_static_BidRequest_Device_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(3);
    internal_static_BidRequest_Device_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_Device_descriptor,
        new java.lang.String[] { "Ip", "Ipv6", "Ua", "Os", "Osv", "DeviceType", "Geo", "Network", "Brand", "Model", "ModelCode", "Orientation", "Dw", "Dh", "Density", "Ppi", "ScreenSize", "Serialno", "AnId", "AnIdMd5", "Imei", "ImeiMd5", "Oaid", "OaidMd5", "ApiLevel", "Paid", "Idfa", "IdfaMd5", "Caid", "CaidMd5", "CaidVer", "Idfv", "IdfvMd5", "OpenUdid", "DeviceName", "DeviceNameMd5", "Language", "Country", "RomVer", "SysComplingTime", "BootTime", "UpdateTime", "InitTime", "DiskSize", "MemorySize", "BatteryStatus", "BatteryPower", "CpuNum", "CpuFre", "TimeZone", "Lmt", "Laccu", "BootMark", "UpdateMark", "AppStoreVer", "HmsVer", "SkadnetworkVer", "InstalledApp", "T2", "T8", "Kid", "CaidVendor", "BootTimeNano", "UpdateTimeNano", "CaidList", });
    internal_static_BidRequest_Geo_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(4);
    internal_static_BidRequest_Geo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_Geo_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Type", "Country", "Province", "City", });
    internal_static_BidRequest_Network_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(5);
    internal_static_BidRequest_Network_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_Network_descriptor,
        new java.lang.String[] { "ConType", "Carrier", "Imsi", "Mcc", "Mnc", "Mac", "MacMd5", "Ssid", "WifiMac", });
    internal_static_BidRequest_CaidList_descriptor =
      internal_static_BidRequest_descriptor.getNestedTypes().get(6);
    internal_static_BidRequest_CaidList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidRequest_CaidList_descriptor,
        new java.lang.String[] { "Id", "Ver", });
    internal_static_BidResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_BidResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidResponse_descriptor,
        new java.lang.String[] { "Id", "Code", "Msg", "Bid", });
    internal_static_BidResponse_Bid_descriptor =
      internal_static_BidResponse_descriptor.getNestedTypes().get(0);
    internal_static_BidResponse_Bid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidResponse_Bid_descriptor,
        new java.lang.String[] { "BidId", "TagId", "Title", "Desc", "IconUrl", "ImgUrls", "W", "H", "LandingUrl", "Deeplink", "DownloadUrl", "BidFloor", "WinUrls", "LoseUrls", "CType", "CiType", "App", "Video", "Trackers", "UniversalLink", "ClickAreaReportUrls", });
    internal_static_BidResponse_Bid_App_descriptor =
      internal_static_BidResponse_Bid_descriptor.getNestedTypes().get(0);
    internal_static_BidResponse_Bid_App_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidResponse_Bid_App_descriptor,
        new java.lang.String[] { "Name", "Size", "Ver", "PrivacyUrl", "PermContent", "Developer", });
    internal_static_BidResponse_Bid_Video_descriptor =
      internal_static_BidResponse_Bid_descriptor.getNestedTypes().get(1);
    internal_static_BidResponse_Bid_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidResponse_Bid_Video_descriptor,
        new java.lang.String[] { "Url", "Duration", "Size", "Vw", "Vh", "CoverImgUrl", "ButtonText", "EndImgUrl", "EndHtml", "SkipSec", "ComLanding", "ProLanding", "Prefetch", });
    internal_static_BidResponse_Bid_Tracker_descriptor =
      internal_static_BidResponse_Bid_descriptor.getNestedTypes().get(2);
    internal_static_BidResponse_Bid_Tracker_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_BidResponse_Bid_Tracker_descriptor,
        new java.lang.String[] { "Type", "Urls", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
