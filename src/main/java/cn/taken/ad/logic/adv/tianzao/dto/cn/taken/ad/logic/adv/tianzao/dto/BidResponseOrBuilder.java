// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: tianzao.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tianzao.dto;

public interface BidResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:BidResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 媒体请求时的唯一ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * 媒体请求时的唯一ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 响应码，200：有广告填充，404：无广告填充
   * </pre>
   *
   * <code>sint32 code = 2;</code>
   * @return The code.
   */
  int getCode();

  /**
   * <pre>
   * 响应说明
   * </pre>
   *
   * <code>string msg = 3;</code>
   * @return The msg.
   */
  java.lang.String getMsg();
  /**
   * <pre>
   * 响应说明
   * </pre>
   *
   * <code>string msg = 3;</code>
   * @return The bytes for msg.
   */
  com.google.protobuf.ByteString
      getMsgBytes();

  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   * @return Whether the bid field is set.
   */
  boolean hasBid();
  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   * @return The bid.
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getBid();
  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder getBidOrBuilder();
}
