package cn.taken.ad.logic.adv.jiuwei;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.TakenRequestInfo;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.app.TakenRequestApp;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.caid.TakenRequestCaid;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.device.TakenRequestDevice;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.geo.TakenRequestGeo;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.network.TakenRequestNetwork;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.tag.TakenRequestTag;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.request.user.TakenRequestUser;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.response.TakenResponseInfo;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.response.app.TakenResponseApp;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.response.tag.TakenResponseTag;
import cn.taken.ad.logic.adv.jiuwei.v2.dto.response.track.TakenResponseTrack;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestUserDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.LinkedList;
import java.util.List;

@Component("GATEWAY" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class GatewayAdvProcessor implements AdvProcessor {
    private static final Logger log = LoggerFactory.getLogger(GatewayAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto request, RtbAdvDto advDto) throws Throwable {
        TakenRequestInfo.Builder rtb = TakenRequestInfo.newBuilder();
        rtb.setReqId(request.getReqId());
        rtb.setApp(warpApp(request.getApp()));
        rtb.setDevice(warpDevice(request.getDevice()));
        rtb.setTag(warpTag(request.getTag()));
        rtb.setGeo(warpGeo(request.getGeo()));
        rtb.setNetwork(warpNetWork(request.getNetwork()));
        rtb.setUser(warpUser(request.getUser()));
        advDto.setReqObj(rtb);
        byte[] bytes = rtb.build().toByteArray();
        HttpResult result = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{
                new BasicHeader("Content-Type", "application/protobuf")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(result);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        byte[] resp = result.getData();
        TakenResponseInfo info = TakenResponseInfo.parseFrom(resp);
        advDto.setRespObj(info);

        if (info.getCode() != 200) {
            if (info.getCode() == 201) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), info.getCode() + "");
            }
        }
        RtbResponseDto responseDto = new RtbResponseDto();
        responseDto.setCode(LogicState.SUCCESS.getCode());
        responseDto.setAdvErrCode(info.getCode() + "");
        List<TakenResponseTag> tags = info.getDataList();
        if (!CollectionUtils.isEmpty(tags)) {
            tags.forEach(v -> {
                TagResponseDto tagDtp = new TagResponseDto();
                tagDtp.setTagInfoId(info.getRespId());
                tagDtp.setTitle(v.getTitle());
                tagDtp.setSubTitle(v.getSubTitle());
                TakenResponseApp appInfo = v.getAppInfo();
                ResponseAppDto app = new ResponseAppDto();
                if (appInfo != null) {
                    app.setAppName(appInfo.getAppName());
                    app.setAppVersion(appInfo.getAppVersion());
                    app.setAppInfo(appInfo.getAppInfo());
                    app.setAppDeveloper(appInfo.getAppDeveloper());
                    app.setAppIconUrl(appInfo.getAppIconUrl());
                    app.setAppInfoUrl(appInfo.getAppInfoUrl());
                    app.setAppPermContent(appInfo.getAppPermContent());
                    app.setAppPermissionInfoUrl(appInfo.getAppPermissionInfoUrl());
                    app.setAppPrivacyUrl(appInfo.getAppPrivacyUrl());
                    app.setAppSize(appInfo.getAppSize());
                    app.setPackageName(appInfo.getPackageName());
                    app.setRating(app.getRating());
                    app.setRatingCount(appInfo.getRatingCount());
                    app.setRecordNumber(appInfo.getRecordNumber());
                }
                tagDtp.setAppInfo(app);
                tagDtp.setActionType(ActionType.parseType(v.getActionType()));
                tagDtp.setDesc(v.getDesc());
                tagDtp.setPrice((double) v.getPrice());
                tagDtp.setClickAreaReportUrls(v.getClickAreaReportUrlsList());
                tagDtp.setClickUrl(v.getClickUrl());
                tagDtp.setDeepLinkUrl(v.getDeepLinkUrl());
                tagDtp.setFailNoticeUrls(v.getFailNoticeUrlList());
                tagDtp.setHtmlContent(v.getHtmlContent());
                tagDtp.setIconUrl(v.getIconUrl());
                tagDtp.setImgUrls(v.getImgUrlsList());
                tagDtp.setLogoUrl(v.getLogoUrl());
                tagDtp.setMarketUrl(v.getMarketUrl());
                tagDtp.setMaterialHeight(v.getMaterialHeight());
                tagDtp.setMaterialType(MaterialType.parseType(v.getMaterialType()));
                tagDtp.setMaterialWidth(v.getMaterialWidth());
                List<TakenResponseTrack> tracks = v.getTracksList();
                List<ResponseTrackDto> copyTracks = new LinkedList<>();
                for (TakenResponseTrack track : tracks) {
                    ResponseTrackDto trackDto = new ResponseTrackDto();
                    trackDto.setTrackType(track.getTrackType());
                    trackDto.setTrackUrls(track.getTrackUrlsList());
                    copyTracks.add(trackDto);
                }
                tagDtp.setTracks(copyTracks);
                tagDtp.setWinNoticeUrls(v.getWinNoticeUrlList());
                tagDtp.setFailNoticeUrls(v.getFailNoticeUrlList());
                responseDto.getTags().add(tagDtp);
            });
        }
        return responseDto;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        if (!param.isBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        boolean success = false;
        for (String url : param.getUrls()) {
            HttpResult result = httpClient.get(url, null, null, 5000);
            if (!result.isSuccess()){
                log.error("notice fail httpCode:{},Url:{}", result.getStatusLine(),url,result.getThrowable());
            }
            if (!success) {
                success = result.isSuccess();
            }
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult();
    }

    private TakenRequestApp warpApp(RequestAppDto dto) {
        TakenRequestApp.Builder app = TakenRequestApp.newBuilder();
        app.setAppId(dto.getAppId() != null ? dto.getAppId() : "");
        app.setAppName(dto.getAppName() != null ? dto.getAppName() : "");
        app.setAppVersion(dto.getAppVersion() != null ? dto.getAppVersion() : "");
        app.setAppVersionCode(dto.getAppVersionCode() != null ? dto.getAppVersionCode() : "");
        app.setBundle(dto.getBundle() != null ? dto.getBundle() : "");
        app.setAppstoreUrl(dto.getAppstoreUrl() != null ? dto.getAppstoreUrl() : "");
        return app.build();
    }

    private TakenRequestDevice warpDevice(RequestDeviceDto deviceDto) {
        TakenRequestDevice.Builder device = TakenRequestDevice.newBuilder();
        TakenRequestDevice.Builder builder = TakenRequestDevice.newBuilder();
        if (StringUtils.isNotEmpty(deviceDto.getSerialNO())) {
            builder.setSerialNO(deviceDto.getSerialNO());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceName())) {
            builder.setDeviceName(deviceDto.getDeviceName());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceNameMd5())) {
            builder.setDeviceNameMd5(deviceDto.getDeviceNameMd5());
        }
        if (null != deviceDto.getOsType()) {
            builder.setOsType(deviceDto.getOsType().getType());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOsVersion())) {
            builder.setOsVersion(deviceDto.getOsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            builder.setBrand(deviceDto.getBrand());
        }
        if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            builder.setModel(deviceDto.getModel());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            builder.setAndroidId(deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            builder.setAndroidIdMd5(deviceDto.getAndroidIdMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            builder.setImei(deviceDto.getImei());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceNameMd5())) {
            builder.setDeviceNameMd5(deviceDto.getDeviceNameMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            builder.setOaid(deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaidMd5())) {
            builder.setOaidMd5(deviceDto.getOaidMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            builder.setIdfa(deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            builder.setIdfaMd5(deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOpenUdId())) {
            builder.setOpenUdId(deviceDto.getOpenUdId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfv())) {
            builder.setIdfv(deviceDto.getIdfv());
        }
        if (null != deviceDto.getCaids()) {
            for (RequestCaidDto caid : deviceDto.getCaids()) {
                if (null != caid.getCaid() && null != caid.getVersion()) {
                    TakenRequestCaid.Builder caidBuilder = TakenRequestCaid.newBuilder();
                    caidBuilder.setCaid(caid.getCaid()).setVersion(caidBuilder.getVersion());
                    if (null != caid.getVendor()) {
                        caidBuilder.setVersion(caid.getVersion());
                    }
                    builder.addCaids(caidBuilder.build());
                }
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getImsi())) {
            builder.setImsi(deviceDto.getImsi());
        }
        if (null != deviceDto.getWidth()) {
            builder.setWidth(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            builder.setHeight(deviceDto.getHeight());
        }
        if (null != deviceDto.getOrientation()) {
            builder.setOrientation(deviceDto.getOrientation().getType());
        }
        if (null != deviceDto.getScreenDensity()) {
            builder.setScreenDensity(deviceDto.getScreenDensity());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
            builder.setUserAgent(deviceDto.getUserAgent());
        }
        if (null != deviceDto.getPpi()) {
            builder.setPpi(deviceDto.getPpi());
        }
        if (StringUtils.isNotEmpty(deviceDto.getTimeZone())) {
            builder.setTimeZone(deviceDto.getTimeZone());
        }
        if (StringUtils.isNotEmpty(deviceDto.getLanguage())) {
            builder.setLanguage(deviceDto.getLanguage());
        }
        if (StringUtils.isNotEmpty(deviceDto.getCountry())) {
            builder.setCountry(deviceDto.getCountry());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareMachine())) {
            builder.setHardwareMachine(deviceDto.getHardwareMachine());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsVersion())) {
            builder.setHmsVersion(deviceDto.getHmsVersion());
        }
        if (null != deviceDto.getScreenInch()) {
            builder.setScreenInch(deviceDto.getScreenInch());
        }
        if (null != deviceDto.getDeviceMemory()) {
            builder.setDeviceMemory(deviceDto.getDeviceMemory());
        }
        if (null != deviceDto.getDeviceHardDisk()) {
            builder.setDeviceHardDisk(deviceDto.getDeviceHardDisk());
        }
        if (null != deviceDto.getCpuNum()) {
            builder.setCpuNum(deviceDto.getCpuNum());
        }
        if (null != deviceDto.getSysUpdateTime()) {
            Long time = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
            if (null != time) {
                builder.setOsUpdateTime(time);
            }
        }
        if (null != deviceDto.getSysStartTime()) {
            Long time = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
            if (null != time) {
                builder.setDeviceStartTime(time);
            }
        }
        if (null != deviceDto.getIsOpenPersonalRecommend()) {
            builder.setIsOpenPersonalRecommend(Boolean.TRUE.equals(deviceDto.getIsOpenPersonalRecommend()) ? 1 : 0);
        }
        if (null != deviceDto.getIsProgrammaticRecommend()) {
            builder.setIsProgrammaticRecommend(Boolean.TRUE.equals(deviceDto.getIsProgrammaticRecommend()) ? 1 : 0);
        }
        return device.build();
    }

    private TakenRequestTag warpTag(RequestTagDto dto) {
        TakenRequestTag.Builder tag = TakenRequestTag.newBuilder();
        tag.setTagId(dto.getTagId() != null ? dto.getTagId() : "");
        if (null != dto.getHeight()) {
            tag.setHeight(dto.getHeight());
        }
        if (null != dto.getWidth()) {
            tag.setWidth(dto.getWidth());
        }
        if (null != dto.getMinDuration()) {
            tag.setMinDuration(dto.getMinDuration());
        }
        if (null != dto.getMaxDuration()) {
            tag.setMaxDuration(dto.getMaxDuration());
        }
        if (null != dto.getPrice()) {
            tag.setPrice(dto.getPrice().intValue());
        }
        return tag.build();
    }

    private TakenRequestGeo warpGeo(RequestGeoDto dto) {
        TakenRequestGeo.Builder geo = TakenRequestGeo.newBuilder();
        if (dto.getCoordinateType() != null) {
            geo.setCoordinateType(dto.getCoordinateType().getType());
        }
        if (null != dto.getLatitude()) {
            geo.setLatitude(dto.getLatitude());
        }
        if (null != dto.getLongitude()) {
            geo.setLongitude(dto.getLongitude());
        }
        return geo.build();
    }

    private TakenRequestNetwork warpNetWork(RequestNetworkDto dto) {
        TakenRequestNetwork.Builder netWork = TakenRequestNetwork.newBuilder();
        if (null != dto.getCarrierType()) {
            netWork.setCarrierType(dto.getCarrierType().getType());
        }
        if (null != dto.getConnectType()) {
            netWork.setConnectType(dto.getConnectType().getType());
        }
        netWork.setIp(dto.getIp() != null ? dto.getIp() : "");
        netWork.setIpv6(dto.getIpv6() != null ? dto.getIpv6() : "");
        netWork.setMac(dto.getMac() != null ? dto.getMac() : "");
        netWork.setMacMd5(dto.getMacMd5() != null ? dto.getMacMd5() : "");
        netWork.setSsid(dto.getSsid() != null ? dto.getSsid() : "");
        netWork.setWifiMac(dto.getWifiMac() != null ? dto.getWifiMac() : "");
        return netWork.build();
    }

    private TakenRequestUser warpUser(RequestUserDto dto) {
        TakenRequestUser.Builder user = TakenRequestUser.newBuilder();
        user.setUserId(dto.getUserId() != null ? dto.getUserId() : "");
        if (null != dto.getAge()) {
            user.setAge(dto.getAge());
        }
        user.setGender(dto.getGender() != null ? dto.getGender() : "");
        if (dto.getInterest() != null) {
            for (int i = 0; i < dto.getInterest().length; i++) {
                user.addInterest(dto.getInterest()[i]);
            }
        }
        return user.build();
    }
}
