package cn.taken.ad.logic.adv.liwei.dto;

import java.io.Serializable;

public class RespImageAndIcon implements Serializable {

    private String url;// string required 图片地址
    private Integer w;// int optional 图片宽
    private Integer h;// int optional 图片高
    private String type;// string optional 图片类型，例如：image/jpg
    private Integer size;// int optional 图片大小，单位 kb

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getW() {
        return w;
    }

    public void setW(Integer w) {
        this.w = w;
    }

    public Integer getH() {
        return h;
    }

    public void setH(Integer h) {
        this.h = h;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
