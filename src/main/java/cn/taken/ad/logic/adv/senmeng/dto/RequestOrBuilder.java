// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: senmeng.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.senmeng.dto;

public interface RequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:Request)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 版本号
   * </pre>
   *
   * <code>int32 version = 1;</code>
   * @return The version.
   */
  int getVersion();

  /**
   * <pre>
   * 此请求的唯一id
   * </pre>
   *
   * <code>string id = 2;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * 此请求的唯一id
   * </pre>
   *
   * <code>string id = 2;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>repeated .Request.Imp imp = 3;</code>
   */
  java.util.List<cn.taken.ad.logic.adv.senmeng.dto.Request.Imp> 
      getImpList();
  /**
   * <code>repeated .Request.Imp imp = 3;</code>
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.Imp getImp(int index);
  /**
   * <code>repeated .Request.Imp imp = 3;</code>
   */
  int getImpCount();
  /**
   * <code>repeated .Request.Imp imp = 3;</code>
   */
  java.util.List<? extends cn.taken.ad.logic.adv.senmeng.dto.Request.ImpOrBuilder> 
      getImpOrBuilderList();
  /**
   * <code>repeated .Request.Imp imp = 3;</code>
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.ImpOrBuilder getImpOrBuilder(
      int index);

  /**
   * <code>.Request.Device device = 5;</code>
   * @return Whether the device field is set.
   */
  boolean hasDevice();
  /**
   * <code>.Request.Device device = 5;</code>
   * @return The device.
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.Device getDevice();
  /**
   * <code>.Request.Device device = 5;</code>
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.DeviceOrBuilder getDeviceOrBuilder();

  /**
   * <code>.Request.App app = 6;</code>
   * @return Whether the app field is set.
   */
  boolean hasApp();
  /**
   * <code>.Request.App app = 6;</code>
   * @return The app.
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.App getApp();
  /**
   * <code>.Request.App app = 6;</code>
   */
  cn.taken.ad.logic.adv.senmeng.dto.Request.AppOrBuilder getAppOrBuilder();

  /**
   * <pre>
   * 是否必须返回https广告
   * </pre>
   *
   * <code>bool https_required = 10;</code>
   * @return The httpsRequired.
   */
  boolean getHttpsRequired();
}
