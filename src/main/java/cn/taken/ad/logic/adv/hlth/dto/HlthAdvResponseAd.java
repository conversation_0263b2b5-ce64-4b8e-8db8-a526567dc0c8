package cn.taken.ad.logic.adv.hlth.dto;

import java.util.List;

public class HlthAdvResponseAd {
    /**
     * 当前ad在seat中的序号，从0开始
     */
    private Integer id;
    /**
     * 创意类型: 1-文字；2-图片；3-Flash；4-视频
     */
    private Integer creativeType;
    /**
     * 广告创意唯一标识
     */
    private String creativeId;
    /**
     * 落地页地址
     */
    private String clickUrl;
    /**
     * APP唤醒url，若唤醒失败加载clickurl
     */
    private String deeplinkUrl;
    /**
     * iOS, universal link 地址
     */
    private String ulk;
    /**
     * 曝光监测地址
     */
    private List<String> exposeTrackingUrl;
    /**
     * 点击监测地址
     */
    private List<String> clickTrackingUrl;
    /**
     * 尝试唤起监测链接
     */
    private List<String> dpTryUrl;
    /**
     * 成功唤起监测链接
     */
    private List<String> dpSuccUrl;
    /**
     * 失败唤起监测链接
     */
    private List<String> dpFailUrl;
    /**
     * 素材属性
     */
    private List<HlthAdvResponseAttr> attr;
    /**
     * 价格，分/cpm，币种CNY
     */
    private Integer price;
    /**
     * 单号，pmp预算返回
     */
    private String dealid;
    /**
     * 竞胜通知URL，一价策略
     */
    private List<String> nurl;
    /**
     * 微信小程序原始ID
     */
    private String wxoid;
    /**
     * 微信小程序打开路径
     */
    private String wxopath;
    /**
     * 下载应用地址，应用下载时必返回
     */
    private String appDownloadUrl;
    /**
     * 下载应用名称，应用下载时必返回
     */
    private String appName;
    private String appIcon;
    /**
     * 下载应用安装包大小，应用下载时返回
     */
    private Integer appSize;
    /**
     * 下载应用版本号，应用下载时必返回
     */
    private String appVersion;
    /**
     * 下载应用版本号，应用下载时必返回
     */
    private String appPackage;
    /**
     * 下载应用开发者信息，应用下载时返回。
     */
    private String appDeveloper;
    /**
     * 下载应用描述，应用下载时返回
     */
    private String appDescriptioin;
    /**
     * 下载应用描述地址，应用下载时返回
     */
    private String appDescriptioinUrl;
    /**
     * 下载应用隐私协议地址，应用下载时返回
     */
    private String appPrivacyUrl;
    /**
     * 下载应用权限说明地址，应用下载时返回
     */
    private String appPermissionUrl;
    /**
     * 开始下载监测链接，应用下载时返回
     */
    private List<String> downloadStart;
    /**
     * 结束下载监测链接，应用下载时返回
     */
    private List<String> downloadEnd;
    /**
     * 开始安装监测链接，应用下载时返回
     */
    private List<String> installStart;
    /**
     * 结束安装监测链接，应用下载时返回
     */
    private List<String> installEnd;
    /**
     * 激活应用监测链接，应用下载时返回
     */
    private List<String> activateApp;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCreativeType() {
        return creativeType;
    }

    public void setCreativeType(Integer creativeType) {
        this.creativeType = creativeType;
    }

    public String getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(String creativeId) {
        this.creativeId = creativeId;
    }

    public String getClickUrl() {
        return clickUrl;
    }

    public void setClickUrl(String clickUrl) {
        this.clickUrl = clickUrl;
    }

    public String getDeeplinkUrl() {
        return deeplinkUrl;
    }

    public void setDeeplinkUrl(String deeplinkUrl) {
        this.deeplinkUrl = deeplinkUrl;
    }

    public String getUlk() {
        return ulk;
    }

    public void setUlk(String ulk) {
        this.ulk = ulk;
    }

    public List<String> getExposeTrackingUrl() {
        return exposeTrackingUrl;
    }

    public void setExposeTrackingUrl(List<String> exposeTrackingUrl) {
        this.exposeTrackingUrl = exposeTrackingUrl;
    }

    public List<String> getClickTrackingUrl() {
        return clickTrackingUrl;
    }

    public void setClickTrackingUrl(List<String> clickTrackingUrl) {
        this.clickTrackingUrl = clickTrackingUrl;
    }

    public List<String> getDpTryUrl() {
        return dpTryUrl;
    }

    public void setDpTryUrl(List<String> dpTryUrl) {
        this.dpTryUrl = dpTryUrl;
    }

    public List<String> getDpSuccUrl() {
        return dpSuccUrl;
    }

    public void setDpSuccUrl(List<String> dpSuccUrl) {
        this.dpSuccUrl = dpSuccUrl;
    }

    public List<String> getDpFailUrl() {
        return dpFailUrl;
    }

    public void setDpFailUrl(List<String> dpFailUrl) {
        this.dpFailUrl = dpFailUrl;
    }

    public List<HlthAdvResponseAttr> getAttr() {
        return attr;
    }

    public void setAttr(List<HlthAdvResponseAttr> attr) {
        this.attr = attr;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getDealid() {
        return dealid;
    }

    public void setDealid(String dealid) {
        this.dealid = dealid;
    }

    public List<String> getNurl() {
        return nurl;
    }

    public void setNurl(List<String> nurl) {
        this.nurl = nurl;
    }

    public String getWxoid() {
        return wxoid;
    }

    public void setWxoid(String wxoid) {
        this.wxoid = wxoid;
    }

    public String getWxopath() {
        return wxopath;
    }

    public void setWxopath(String wxopath) {
        this.wxopath = wxopath;
    }

    public String getAppDownloadUrl() {
        return appDownloadUrl;
    }

    public void setAppDownloadUrl(String appDownloadUrl) {
        this.appDownloadUrl = appDownloadUrl;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(String appIcon) {
        this.appIcon = appIcon;
    }

    public Integer getAppSize() {
        return appSize;
    }

    public void setAppSize(Integer appSize) {
        this.appSize = appSize;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAppPackage() {
        return appPackage;
    }

    public void setAppPackage(String appPackage) {
        this.appPackage = appPackage;
    }

    public String getAppDeveloper() {
        return appDeveloper;
    }

    public void setAppDeveloper(String appDeveloper) {
        this.appDeveloper = appDeveloper;
    }

    public String getAppDescriptioin() {
        return appDescriptioin;
    }

    public void setAppDescriptioin(String appDescriptioin) {
        this.appDescriptioin = appDescriptioin;
    }

    public String getAppDescriptioinUrl() {
        return appDescriptioinUrl;
    }

    public void setAppDescriptioinUrl(String appDescriptioinUrl) {
        this.appDescriptioinUrl = appDescriptioinUrl;
    }

    public String getAppPrivacyUrl() {
        return appPrivacyUrl;
    }

    public void setAppPrivacyUrl(String appPrivacyUrl) {
        this.appPrivacyUrl = appPrivacyUrl;
    }

    public String getAppPermissionUrl() {
        return appPermissionUrl;
    }

    public void setAppPermissionUrl(String appPermissionUrl) {
        this.appPermissionUrl = appPermissionUrl;
    }

    public List<String> getDownloadStart() {
        return downloadStart;
    }

    public void setDownloadStart(List<String> downloadStart) {
        this.downloadStart = downloadStart;
    }

    public List<String> getDownloadEnd() {
        return downloadEnd;
    }

    public void setDownloadEnd(List<String> downloadEnd) {
        this.downloadEnd = downloadEnd;
    }

    public List<String> getInstallStart() {
        return installStart;
    }

    public void setInstallStart(List<String> installStart) {
        this.installStart = installStart;
    }

    public List<String> getInstallEnd() {
        return installEnd;
    }

    public void setInstallEnd(List<String> installEnd) {
        this.installEnd = installEnd;
    }

    public List<String> getActivateApp() {
        return activateApp;
    }

    public void setActivateApp(List<String> activateApp) {
        this.activateApp = activateApp;
    }
}
