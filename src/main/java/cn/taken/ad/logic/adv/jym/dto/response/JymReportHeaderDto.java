package cn.taken.ad.logic.adv.jym.dto.response;

/**
 * key、value 字段说明
 *  key     value
 *  Host    指定值（监测上报地址的 host）
 *  Hosts   指定值（监测上报地址的 hosts）
 *  User-Agent 指定值（browser_ua）
 *  X-Forwarded-For 指定值（客户端 IP）
 *  Accept *\/*
 *  X-User-Agent 指定值
 *  Gsid 空值
 *  Accept-Encoding gzip
 *  Connection Keep-Alive
 *  Accept-Language zh-CN,zh-Hans;q=0.9
 *  X-Requested-With 指定值
 */
public class JymReportHeaderDto {

    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
