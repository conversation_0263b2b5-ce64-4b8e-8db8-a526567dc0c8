// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RequestUser.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.request.user;

public interface TakenRequestUserOrBuilder extends
    // @@protoc_insertion_point(interface_extends:req.TakenRequestUser)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string gender = 1;</code>
   * @return The gender.
   */
  String getGender();
  /**
   * <code>string gender = 1;</code>
   * @return The bytes for gender.
   */
  com.google.protobuf.ByteString
      getGenderBytes();

  /**
   * <code>int32 age = 2;</code>
   * @return The age.
   */
  int getAge();

  /**
   * <code>repeated string interest = 3;</code>
   * @return A list containing the interest.
   */
  java.util.List<String>
      getInterestList();
  /**
   * <code>repeated string interest = 3;</code>
   * @return The count of interest.
   */
  int getInterestCount();
  /**
   * <code>repeated string interest = 3;</code>
   * @param index The index of the element to return.
   * @return The interest at the given index.
   */
  String getInterest(int index);
  /**
   * <code>repeated string interest = 3;</code>
   * @param index The index of the value to return.
   * @return The bytes of the interest at the given index.
   */
  com.google.protobuf.ByteString
      getInterestBytes(int index);

  /**
   * <code>string userId = 4;</code>
   * @return The userId.
   */
  String getUserId();
  /**
   * <code>string userId = 4;</code>
   * @return The bytes for userId.
   */
  com.google.protobuf.ByteString
      getUserIdBytes();
}
