package cn.taken.ad.logic.adv.chongtong;


import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.chongtong.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * http://show-doc.chongtongtec.cn/web/#/p/f121e3f931c619cec06f592671bd8442
 */
@Component("CHONGTONG" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class ChongTongAdvProcessor implements AdvProcessor {
    private static final Logger log = LoggerFactory.getLogger(ChongTongAdvProcessor.class);
    public static final String AES_KEY = "secretKey";
    public static final String AES_IVSTR = "ivStr";

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto request, RtbAdvDto advDto) throws Throwable {

        String secretKey = ParamParser.parseParamByJson(advDto.getPnyParam()).get(AES_KEY);
        String ivStr = ParamParser.parseParamByJson(advDto.getPnyParam()).get(AES_IVSTR);

        AdRequestDto ct = new AdRequestDto();
        ct.setAdSlot(buildAdslotInfo(request, advDto));
        ct.setApp(buildAppInfo(request, advDto));
        ct.setDevice(buildDeviceInfo(request, advDto));
        ct.setNetwork(buildNetworkInfo(request, advDto));
        ct.setGps(buildGpsInfo(request, advDto));

        RequestDeviceDto deviceDto = request.getDevice();
        if (!CollectionUtils.isEmpty(deviceDto.getInstalledAppInfo())) {
            Set<String> appList = new HashSet<>();
            deviceDto.getInstalledAppInfo().forEach(v -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(v.getAppName())) {
                    appList.add(v.getAppName());
                }
            });
            if (appList.size() > 0) {
                ct.setInstalledSet(appList);
            }
        }
        advDto.setReqObj(ct);
        String json = JsonHelper.toJsonStringWithoutNull(ct);
        byte[] encryptData = Aes.encrypt(json.getBytes("utf-8"), secretKey.getBytes("utf-8"), ivStr.getBytes("utf-8"), "AES/CBC/PKCS5Padding");
        String encryptResultStr = org.apache.commons.codec.binary.Base64.encodeBase64String(encryptData);
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", advDto.getAppCode());
        requestMap.put("md", encryptResultStr);

        HttpResult httpResult = httpClient.post(advDto.getRtburl(), requestMap, "Utf-8", new Header[]{new BasicHeader("Content-Type", "application/x-www-form-urlencoded")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        advDto.setRespObj(resp);
        return parseResponse(json, resp);
    }

    private AdslotInfo buildAdslotInfo(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        RequestTagDto tag = rtbDto.getTag();
        AdslotInfo info = new AdslotInfo();

        info.setId(advDto.getTagCode());
        if (null != tag.getWidth()) {
            info.setWidth(tag.getWidth());
        }
        if (null != tag.getHeight()) {
            info.setHeight(tag.getHeight());
        }
        info.setAdCount(1);
        if (null != advDto.getPrice()) {
            info.setBidfloor(advDto.getPrice().multiply(new BigDecimal(100)).floatValue());
        }
        if (StringUtils.isNotEmpty(tag.getQuery())) {
            info.setSearchWords(Collections.singletonList(tag.getQuery()));
        }
        Integer tagType = tag.getTagType().getType();
        if (tagType != null) {
            if (tagType.intValue() == TagType.OPEN.getType()) {
                info.setAdType(2);
            } else if (tagType.intValue() == TagType.INFORMATION_FLOW.getType()) {
                info.setAdType(1);
            } else if (tagType.intValue() == TagType.BANNER.getType()) {
                info.setAdType(0);
            }
        }
        return info;
    }

    private AppInfo buildAppInfo(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        RequestAppDto appInfo = rtbDto.getApp();
        AppInfo info = new AppInfo();
        if (StringUtils.isNotEmpty(advDto.getAppCode())) {
            info.setAppId(advDto.getAppCode());
        }
        if (StringUtils.isNotEmpty(appInfo.getAppName())) {
            info.setAppName(appInfo.getAppName());
        }
        if (StringUtils.isNotEmpty(appInfo.getBundle())) {
            info.setPackageName(appInfo.getBundle());
        }
        if (StringUtils.isNotEmpty(appInfo.getAppVersion())) {
            info.setVersionName(appInfo.getAppVersion());
        }
        if (StringUtils.isNotEmpty(appInfo.getAppVersionCode())) {
            info.setVersionCode(Long.valueOf(appInfo.getAppVersionCode()));
        }
        return info;
    }

    private DeviceInfo buildDeviceInfo(RtbRequestDto rtbDto, RtbAdvDto advDto) {

        DeviceInfo device = new DeviceInfo();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        if (null != deviceDto.getDeviceType()) {
            DeviceType deviceType = deviceDto.getDeviceType();
            if (null != deviceType) {
                switch (deviceType) {
                    case PC:
                        device.setDeviceType(2);
                        break;
                    case PHONE:
                        device.setDeviceType(4);
                        break;
                    case PAD:
                        device.setDeviceType(5);
                        break;
                    case TV:
                        device.setDeviceType(3);
                    default:
                        device.setDeviceType(1);
                }
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getVendor())) {
            //待确认
            device.setManufacturer(deviceDto.getVendor());
        } else {
            if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
                //待确认
                device.setManufacturer(deviceDto.getBrand());
            }
        }

        if (StringUtils.isNotEmpty(deviceDto.getVendor())) {
            device.setVendor(deviceDto.getVendor());
        }

        if (StringUtils.isNotEmpty(deviceDto.getBrand())) {
            device.setBrand(deviceDto.getBrand());
        }
        if (StringUtils.isNotEmpty(deviceDto.getModel())) {
            device.setModel(deviceDto.getModel());
        }
        if (null != deviceDto.getOsType()) {
            OsType osType = deviceDto.getOsType();
            switch (osType) {
                case ANDROID:
                    device.setOs("android");
                    break;
                case IOS:
                    device.setOs("ios");
                    break;
                case WINDOWS_PHONE:
                    device.setOs("wp");
                    break;
                default:
                    device.setOs("unknown");
            }
        }
        if (null != deviceDto.getApiLevel()) {
            device.setOsLevel(deviceDto.getApiLevel());
        } else {
            if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
                device.setOsLevel(extractAndroidMajorVersion(deviceDto.getUserAgent()));
            }
        }
        if (StringUtils.isNotEmpty(deviceDto.getOsVersion())) {
            device.setOsVersion(deviceDto.getOsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            device.setAndroidId(deviceDto.getAndroidId());
        } else {
            device.setAndroidId("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            device.setImei(deviceDto.getImei());
        } else {
            device.setImei("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            device.setIdfa(deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            device.setIdfaMd5(deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfv())) {
            device.setIdfv(deviceDto.getIdfv());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfvMd5())) {
            device.setIdfvMd5(deviceDto.getIdfvMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOpenUdId())) {
            device.setOpenudid(deviceDto.getOpenUdId());
        }

        if (!CollectionUtils.isEmpty(deviceDto.getCaids())) {
            device.setCaid(deviceDto.getCaids().get(0).getCaid());
        }


        if (StringUtils.isNotEmpty(deviceDto.getSerialNO())) {
            device.setSerialno(deviceDto.getSerialNO());
        } else {
            device.setSerialno("default");
        }
        if (StringUtils.isNotEmpty(deviceDto.getImsi())) {
            device.setImsi(deviceDto.getImsi());
        } else {
            device.setImsi("default");
        }
        if (StringUtils.isNotEmpty(networkDto.getMac())) {
            device.setMac(networkDto.getMac());
        } else {
            device.setMac("00:00:00:00");
        }

        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == 1) {
                device.setOrientation(2);
            } else if (deviceDto.getOrientation().getType() == 2) {
                device.setOrientation(1);
            } else {
                device.setOrientation(0);
            }
        } else {
            device.setOrientation(0);
        }
        if (null != deviceDto.getWidth()) {
            device.setScreenWidth(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            device.setScreenHeight(deviceDto.getHeight());
        }

        if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
            device.setUa(deviceDto.getUserAgent());
        }

        if (null != deviceDto.getScreenInch()) {
            device.setDpi(deviceDto.getScreenInch());
        }

        if (null != deviceDto.getScreenDensity()) {
            device.setDensity(deviceDto.getScreenDensity());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            device.setOaid(deviceDto.getOaid());
        } else {
            device.setOaid("");
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaidMd5())) {
            device.setOaidMd5(deviceDto.getOaidMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            device.setImeiMd5(deviceDto.getImeiMd5());
        }
        if (StringUtils.isNotEmpty(networkDto.getMacMd5())) {
            device.setMacMd5(networkDto.getMacMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            device.setAndroidIdMd5(deviceDto.getAndroidIdMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsVersion())) {
            device.setBuildVersion(deviceDto.getHmsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysInitTime())) {
            device.setBuildTime(TimeUtils.convertMilliSecond(deviceDto.getSysInitTime()));
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareMachine())) {
            device.setHardware(deviceDto.getHardwareMachine());
        }
//        device.setMcc();
//        device.setMnc();
//        device.setCsc();

        return device;
    }

    private NetworkInfo buildNetworkInfo(RtbRequestDto rtbDto, RtbAdvDto advDto) {

        NetworkInfo net = new NetworkInfo();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        if (StringUtils.isNotEmpty(networkDto.getIp())) {
            net.setIpv4(networkDto.getIp());
        }

        if (null != networkDto.getConnectType()) {
            if (networkDto.getConnectType().getType() == 2) {
                net.setConnectType(1);
            } else if (networkDto.getConnectType().getType() == 5) {
                net.setConnectType(3);
            } else if (networkDto.getConnectType().getType() == 6) {
                net.setConnectType(4);
            } else if (networkDto.getConnectType().getType() == 7) {
                net.setConnectType(5);
            } else if (networkDto.getConnectType().getType() == 4) {
                net.setConnectType(2);
            } else {
                net.setConnectType(0);
            }
        }

        if (null != networkDto.getCarrierType()) {
            if (networkDto.getCarrierType().getType() == 1) {
                net.setCarrier(1);
            } else if (networkDto.getCarrierType().getType() == 2) {
                net.setCarrier(3);
            } else if (networkDto.getCarrierType().getType() == 3) {
                net.setCarrier(2);
            } else {
                net.setCarrier(4);
            }
        }
        if (StringUtils.isNotEmpty(networkDto.getSsid())) {
            net.setBssid(networkDto.getSsid());
        }
        if (StringUtils.isNotEmpty(networkDto.getWifiMac())) {
            net.setWifiMac(networkDto.getWifiMac());
        }
        return net;
    }

    private GpsInfo buildGpsInfo(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        GpsInfo gps = new GpsInfo();
        RequestGeoDto geoDto = rtbDto.getGeo();
        if (geoDto.getCoordinateType() != null) {
            CoordinateType coordinateType = geoDto.getCoordinateType();
            gps.setCoordinateType(coordinateType.getType());
        }
        if (null != geoDto.getLongitude()) {
            gps.setLon(geoDto.getLongitude());
        }
        if (null != geoDto.getLatitude()) {
            gps.setLon(geoDto.getLatitude());
        }
        if (null != geoDto.getLatitude()) {
            gps.setLon(geoDto.getLatitude());
        }
        if (null != geoDto.getTimestamp()) {
            gps.setTimestamp(geoDto.getTimestamp());
        }
        return gps;
    }

    private RtbResponseDto parseResponse(String req, String resp) throws Exception {
        ResponseDto rspDto = null;
        try {
            rspDto = JsonHelper.fromJson(ResponseDto.class, resp);
        } catch (Exception e) {
            //log.error("Serializable resp fail {}->{} ",req, resp);
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }

        if (rspDto.getCode() != 0) {
            if (6 == rspDto.getCode()) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            }
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), rspDto.getCode() + "");
        }

        if (null == rspDto.getData() || rspDto.getData().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        // 广告信息
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", rspDto.getCode() + "");
        responseDto.setRespId(rspDto.getRequestId());

        List<AdInfo> ad = rspDto.getData();
        ad.forEach(item -> {
                    TagResponseDto dto = new TagResponseDto();
                    if (StringUtils.isNotEmpty(item.getId())) {
                        dto.setTagInfoId(item.getId());
                    }
                    if (null != item.getMaterial()) {
                        Material material = item.getMaterial();
                        if (material.getType().equals("GROUP_IMG") ||
                                material.getType().equals("SMALL_IMG") ||
                                material.getType().equals("BIG_IMG") ||
                                material.getType().equals("SPLASH_IMG") || material.getType().equals("BANNER_IMG")) {
                            dto.setMaterialType(MaterialType.IMAGE_TEXT);
                        } else if (material.getType().equals("SPLASH_VIDEO") || material.getType().equals("VIDEO")) {
                            dto.setMaterialType(MaterialType.VIDEO);
                        } else if (material.getType().equals("HTML")) {
                            dto.setMaterialType(MaterialType.HTML);
                        }
                        if (null != material.getMaterialUrls() && material.getMaterialUrls().size() > 0) {
                            dto.setImgUrls(material.getMaterialUrls());
                        }
                        if (StringUtils.isNotEmpty(material.getTitle())) {
                            dto.setTitle(material.getTitle());
                        }
                        if (StringUtils.isNotEmpty(material.getSubtitle())) {
                            dto.setSubTitle(material.getSubtitle());
                        }
                    }

                    if (StringUtils.isNotEmpty(item.getInteractionType())) {
                        if (item.getInteractionType().equals("WEB_VIEW_H5")) {
                            dto.setActionType(ActionType.WEB_VIEW_H5);
                        } else if (item.getInteractionType().equals("BROWSER_OPEN")) {
                            dto.setActionType(ActionType.SYSTEM_BROWSER_H5);
                        } else if (item.getInteractionType().equals("DIRECT_DOWNLOAD")) {
                            dto.setActionType(ActionType.DOWNLOAD);
                        } else if (item.getInteractionType().equals("DEEPLINK")) {
                            dto.setActionType(ActionType.DEEPLINK);
                        }
                    }
                    if (StringUtils.isNotEmpty(item.getLogo())) {
                        dto.setLogoUrl(item.getLogo());
                    }
                    if (StringUtils.isNotEmpty(item.getClickUrl())) {
                        dto.setClickUrl(item.getClickUrl());
                    }
                    if (StringUtils.isNotEmpty(item.getDownloadRefetch())) {
                        dto.setMarketUrl(item.getDownloadRefetch());
                    }
                    if (StringUtils.isNotEmpty(item.getDeeplink())) {
                        dto.setDeepLinkUrl(item.getDeeplink());
                    }

                    ResponseAppDto appDto = new ResponseAppDto();
                    if (StringUtils.isNotEmpty(item.getBundle())) {
                        appDto.setAppName(item.getBundle());
                    }
                    DownloadInfo downLoadInfo = item.getDownloadInfo();
                    if (null != downLoadInfo) {
                        if (StringUtils.isNotEmpty(downLoadInfo.getAppPackage())) {
                            appDto.setPackageName(downLoadInfo.getAppPackage());
                        }
                        if (StringUtils.isNotEmpty(downLoadInfo.getSize())) {
                            appDto.setAppSize(Long.valueOf(downLoadInfo.getSize()));
                        }
                    }
                    dto.setAppInfo(appDto);

                    List<ResponseTrackDto> tracks = new ArrayList<>();
                    dto.setTracks(tracks);
                    if (null != item.getImpTrackers() && item.getImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(item.getImpTrackers())));
                    }
                    if (null != item.getAdCloseTrackers() && item.getAdCloseTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), new ArrayList<>(item.getAdCloseTrackers())));
                    }
                    if (null != item.getAdSkipTrackers() && item.getAdSkipTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), new ArrayList<>(item.getAdSkipTrackers())));
                    }
                    if (null != item.getClickTrackers() && item.getClickTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(item.getClickTrackers())));
                    }
                    if (null != item.getDeeplinkAttemptTrackers() && item.getDeeplinkAttemptTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>(item.getDeeplinkAttemptTrackers())));
                    }
                    if (null != item.getDeeplinkTrackers() && item.getDeeplinkTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(item.getDeeplinkTrackers())));
                    }
                    if (null != item.getDeeplinkFailedTrackers() && item.getDeeplinkFailedTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(item.getDeeplinkFailedTrackers())));
                    }
                    if (null != item.getDownloadStartTrackers() && item.getDownloadStartTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(item.getDownloadStartTrackers())));
                    }
                    if (null != item.getDownloadEndTrackers() && item.getDownloadEndTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(item.getDownloadEndTrackers())));
                    }
                    if (null != item.getInstallStartTrackers() && item.getInstallStartTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(item.getInstallStartTrackers())));
                    }
                    if (null != item.getInstallTrackers() && item.getInstallTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(item.getInstallTrackers())));
                    }
                    if (null != item.getActiveTrackers() && item.getActiveTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), new ArrayList<>(item.getActiveTrackers())));
                    }
                    if (null != item.getStartVideoImpTrackers() && item.getStartVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(item.getStartVideoImpTrackers())));
                    }
                    if (null != item.getQuarterVideoImpTrackers() && item.getQuarterVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), new ArrayList<>(item.getQuarterVideoImpTrackers())));
                    }
                    if (null != item.getHalfVideoImpTrackers() && item.getHalfVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), new ArrayList<>(item.getHalfVideoImpTrackers())));
                    }
                    if (null != item.getThreeQuartersVideoImpTrackers() && item.getThreeQuartersVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), new ArrayList<>(item.getThreeQuartersVideoImpTrackers())));
                    }
                    if (null != item.getCompletelyVideoImpTrackers() && item.getCompletelyVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(item.getCompletelyVideoImpTrackers())));
                    }
                    if (null != item.getPausedVideoImpTrackers() && item.getPausedVideoImpTrackers().size() > 0) {
                        tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(item.getPausedVideoImpTrackers())));
                    }
                    // 宏替换 统一替换成平台的宏(事件)
                    dto.getTracks().forEach(track -> {
                        List<String> urls = track.getTrackUrls();
                        urls = replaceMacro("__TS__", urls, MacroType.START_TIME.getCode());
                        urls = replaceMacro("__TSS__", urls, MacroType.START_TIME_SECONDS.getCode());

                        urls = replaceMacro("__DOWN_X__", urls, MacroType.ABS_DOWN_X.getCode());
                        urls = replaceMacro("__DOWN_Y__", urls, MacroType.ABS_DOWN_Y.getCode());

                        urls = replaceMacro("__UP_X__", urls, MacroType.ABS_UP_X.getCode());
                        urls = replaceMacro("__UP_Y__", urls, MacroType.ABS_UP_Y.getCode());


                        urls = replaceMacro("__AD_DOWN_X__", urls, MacroType.DOWN_X.getCode());
                        urls = replaceMacro("__AD_DOWN_Y__", urls, MacroType.DOWN_Y.getCode());


                        urls = replaceMacro("__AD_UP_X__", urls, MacroType.UP_X.getCode());
                        urls = replaceMacro("__AD_UP_Y__", urls, MacroType.UP_Y.getCode());

                        urls = replaceMacro("__DST_LIN__", urls, MacroType.DST_LINK.getCode());
                        track.setTrackUrls(urls);
                    });
                    responseDto.getTags().add(dto);
                }
        );
        return responseDto;
    }


    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        return SuperResult.rightResult();
    }


    public static Integer extractAndroidMajorVersion(String userAgent) {
        // Regex to find "Android " followed by one or more digits
        Pattern pattern = Pattern.compile("Android (\\d+)");
        Matcher matcher = pattern.matcher(userAgent);

        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
            }
        }
        return null;
    }
}
