package cn.taken.ad.logic.adv.maozhua.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class VideoSpecial implements Serializable {

    private Integer t ;//optional int 延迟执行时间，单位秒，无该属性、值为0不用处理，有值则表 示该Ad需要在前一个Ad执行完后间隔t秒才执行
    private String ua;// optional string 后续上报事件，访问素材/落地页URL需要使用的UA
    private String img;// optional string 图片URL
    private String curl;// optional string 落地页地址
    private Integer clk_rpt;// optional int 是否需要上报点击事件并且访问落地页URL
    private Integer clk_t;// optional int 点击事件触发的时间，单位：秒，距离曝光的时间
    private Integer render;// optional int 是否需要渲染落地页
    private Integer render_t;// optional int 渲染停留时间，单位：秒
    private Integer render_ac;// optional int 渲染产生点击行为次数
    private Integer render_as;// optional int 渲染产生滑动行为次数
    private Integer ot ;//optional int 超时协议，时间秒，当因灭屏导致程序中断时间超过该设置时间 时，后续广告行为不处理。不传默认不处理超时，正常处理后续 广告行为
    private Map<String,String> heanders;// optional map<string,string> 后续上报监测链接和访问落地页需要带上返回的头域信息
    private Integer handle_type;// optional int 处理模式： 0 代表使用原来的方式， 1代表使用新的方式
    private Integer track_type;// optional int 新模式处理策略； 0 代表和任务执行同一个线程， 1代表多条一个线程， 2代表一条监测一个线程
    private Long clk_t_l;// optional long clk_t字段的毫秒单位的值
    private Long ot_long;// optional long ot字段的毫秒单位的值
    private List<VideoSpecialEvent> events;// optional []VideoSpecialEvent VideoSpecialEvent对象列表，用于各种事件的追踪

    public Integer getT() {
        return t;
    }

    public void setT(Integer t) {
        this.t = t;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getCurl() {
        return curl;
    }

    public void setCurl(String curl) {
        this.curl = curl;
    }

    public Integer getClk_rpt() {
        return clk_rpt;
    }

    public void setClk_rpt(Integer clk_rpt) {
        this.clk_rpt = clk_rpt;
    }

    public Integer getClk_t() {
        return clk_t;
    }

    public void setClk_t(Integer clk_t) {
        this.clk_t = clk_t;
    }

    public Integer getRender() {
        return render;
    }

    public void setRender(Integer render) {
        this.render = render;
    }

    public Integer getRender_t() {
        return render_t;
    }

    public void setRender_t(Integer render_t) {
        this.render_t = render_t;
    }

    public Integer getRender_ac() {
        return render_ac;
    }

    public void setRender_ac(Integer render_ac) {
        this.render_ac = render_ac;
    }

    public Integer getRender_as() {
        return render_as;
    }

    public void setRender_as(Integer render_as) {
        this.render_as = render_as;
    }

    public Integer getOt() {
        return ot;
    }

    public void setOt(Integer ot) {
        this.ot = ot;
    }

    public Map<String, String> getHeanders() {
        return heanders;
    }

    public void setHeanders(Map<String, String> heanders) {
        this.heanders = heanders;
    }

    public Integer getHandle_type() {
        return handle_type;
    }

    public void setHandle_type(Integer handle_type) {
        this.handle_type = handle_type;
    }

    public Integer getTrack_type() {
        return track_type;
    }

    public void setTrack_type(Integer track_type) {
        this.track_type = track_type;
    }

    public Long getClk_t_l() {
        return clk_t_l;
    }

    public void setClk_t_l(Long clk_t_l) {
        this.clk_t_l = clk_t_l;
    }

    public Long getOt_long() {
        return ot_long;
    }

    public void setOt_long(Long ot_long) {
        this.ot_long = ot_long;
    }

    public List<VideoSpecialEvent> getEvents() {
        return events;
    }

    public void setEvents(List<VideoSpecialEvent> events) {
        this.events = events;
    }
}
