package cn.taken.ad.logic.adv.ydzx;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.CarrierType;
import cn.taken.ad.constant.business.ConnectionType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.OrientationType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.ydzx.dto.YdzxDownloadAppInfoDto;
import cn.taken.ad.logic.adv.ydzx.dto.YdzxImageInfoDto;
import cn.taken.ad.logic.adv.ydzx.dto.YdzxResponseDto;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 有道智选
 */
@Component("YDZX" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class YdzxAdvProcessor implements AdvProcessor {

    private static final int INITIALIZATION_VECTOR_SIZE = 16;
    private static final int SIGNATURE_SIZE = 4;
    private static final int BLOCK_SIZE = 20;
    public static final String PRICE_KEY = "priceKey";
    public static final String PRICE_INTEGRITY_KEY = "integrityKeyStr";
    private static final Logger log = LoggerFactory.getLogger(YdzxAdvProcessor.class);

    @Resource
    private BaseRedisL2Cache baseRedisL2Cache;

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        Map<String, String> requestMap = convertRequest(rtbDto, advDto);
        advDto.setReqObj(requestMap);
        HttpResult httpResult = httpClient.post(advDto.getRtburl(), requestMap, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/x-www-form-urlencoded")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        // 有道 返回的错误码
        String errCode = findErrCodeByHeader(httpResult.getHeaders());
        String resp = httpResult.getDataStringUTF8();
        //log.info("RtbId:{} Req Adv:{},HttpCode:{},RespCode:{},Request:{}, Resp:{} ",rtbDto.getRtbId(),rtbDto.getAdvAccountId(),httpResult.getStatusLine(),errCode,JsonHelper.toJsonString(requestMap),resp);
        if (StringUtils.isEmpty(errCode)) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "no code");
        }
        if (!errCode.equals("21000") && !errCode.equals("22001") && !errCode.equals("22002")) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), errCode);
        }
        return parseResponse(errCode, resp, advDto);
    }

    public String findErrCodeByHeader(Header[] headers) {
        if (null == headers || headers.length == 0) {
            return "";
        }
        Header stateHeader = Arrays.stream(headers).filter(item -> StringUtils.isNotEmpty(item.getName()) && item.getName().equalsIgnoreCase("X-Adstate")).findFirst().orElse(null);
        if (null == stateHeader) {
            return "";
        }
        if (StringUtils.isEmpty(stateHeader.getValue())) {
            return "";
        }
        Map<String, String> code = JsonHelper.fromJson(new TypeToken<Map<String, String>>() {
        }, stateHeader.getValue());
        return code.get("code");
    }

    public RtbResponseDto parseResponse(String errCode, String resp, RtbAdvDto advDto) throws Exception {
        List<YdzxResponseDto> dtos = new ArrayList<>();
        try {
            advDto.setRespObj(resp);
            // 是否Json数组
            if (resp.startsWith("[{") && resp.endsWith("}]")) {
                dtos = JsonHelper.fromJson(new TypeToken<List<YdzxResponseDto>>() {
                }, resp);
            } else if (resp.startsWith("{") && resp.endsWith("}")) {
                dtos.add(JsonHelper.fromJson(YdzxResponseDto.class, resp));
            }
        } catch (Exception e) {
            log.error("Error", e);
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", errCode);
        dtos.forEach(item -> {
            TagResponseDto tag = new TagResponseDto();
            //广告 id
            tag.setCreativeId(null != item.getCreativeid() ? item.getCreativeid() + "" : "");
            // 点击链接
            tag.setClickUrl(item.getClk());
            List<ResponseTrackDto> tracks = new ArrayList<>();
            tag.setTracks(tracks);
            //点击跟踪链接数组，PC 端对接广告位客户端只需要获取clktrackers 里的第一条链接进行上报即可。
            if (null != item.getClktrackers() && item.getClktrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(Arrays.asList(item.getClktrackers()))));
            }
            //展示跟踪链接数组，该字段支持的宏定义见下文展示上报说明，由 exchange 做替换
            if (null != item.getImptracker() && item.getImptracker().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(Arrays.asList(item.getImptracker()))));
            }
            tag.setDeepLinkUrl(item.getDeeplink());
            // 尝试吊起 deeplink，用户点击应用直达广告后，尝试调用universal_link 或 customized_invoke_url 时上报至此链接
            if (null != item.getDptrackers() && item.getDptrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>(Arrays.asList(item.getDptrackers()))));
            }
            // deepLink 吊起场景下，该参数下发链接为应用已安装的监测上报链接；应用是否安装场景下，该参数下发链接为应用已安装的监测上报链接
            // dpInstallTrackersdeepLink 吊起场景下，该参数下发链接为应用已安装的监测上报链接；应用是否安装场景下，该参数下发链接为应用已安装的监测上报链接
            // dpNotInstallTrackers 因未安装应用导致调起失败的上报地址
            // dpSuccessTrackers deeplink 调起成功的上报地址
            if (null != item.getDpSuccessTrackers() && item.getDpSuccessTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(Arrays.asList(item.getDpSuccessTrackers()))));
            }
            if (null != item.getDpFailedTrackers() && item.getDpFailedTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(Arrays.asList(item.getDpFailedTrackers()))));
            }
            //广告类型，0：落地页广告；1：下载类型广告
            if (null != item.getYdAdType()) {
                if (item.getYdAdType() == 1) {
                    tag.setActionType(ActionType.DOWNLOAD);
                } else {
                    tag.setActionType(ActionType.SYSTEM_BROWSER_H5);
                }
            }
            //styleName 广告样式名称
            //iconimage
            tag.setIconUrl(item.getIconimage());
            // mainimage
            tag.setLogoUrl(item.getMainimage());
            // text
            tag.setDesc(item.getText());
            tag.setTitle(item.getTitle());
            ResponseAppDto appDto = new ResponseAppDto();
            tag.setAppInfo(appDto);
            //appName 当广告类型为下载类型时，并且 appName 不为空时，会返回此字段。该字段会将样式中的同名字段覆盖
            appDto.setAppName(item.getAppName());
            appDto.setPackageName(item.getPackageName());
            if (null != item.getApkStartDownloadTrackers() && item.getApkStartDownloadTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(Arrays.asList(item.getApkStartDownloadTrackers()))));
            }
            if (null != item.getApkDownloadTrackers() && item.getApkDownloadTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(Arrays.asList(item.getApkDownloadTrackers()))));
            }
            if (null != item.getApkStartInstallTrackers() && item.getApkStartInstallTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(Arrays.asList(item.getApkStartInstallTrackers()))));
            }
            if (null != item.getApkInstallTrackers() && item.getApkInstallTrackers().length > 0) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(Arrays.asList(item.getApkInstallTrackers()))));
            }
            YdzxDownloadAppInfoDto ydzxApp = item.getDownloadAppInfo();
            if (null != ydzxApp) {
                appDto.setAppPermissionInfoUrl(ydzxApp.getAppPermission());
                appDto.setAppPrivacyUrl(ydzxApp.getPrivacyPolicy());
                appDto.setAppDeveloper(ydzxApp.getDeveloperName());
                // appIconImage 应用图标
                appDto.setAppVersion(ydzxApp.getAppVersion());
            }
            // ecpm 效果广告出价（单位：分）暂时强转为int
            tag.setPrice(null == item.getEcpm() ? null : item.getEcpm());
            // wxMiniProgram微信小程序跳转相关信息
            // wxTrackers尝试吊起微信小程序，用户点击微信小程序广告后，尝试调用 WxMiniProgram.path 时上报至此链接；
            // wxSuccessTrackers微信小程序吊起成功时上报至此链接
            // wxFailedTrackers 微信小程序吊起失败时上报至此链接；
            // winNotice 竞胜上报链接，该字段支持的宏定义见下文展示上报说明，由 exchange 做替换
            if (StringUtils.isNotEmpty(item.getWinNotice())) {
                tag.setWinNoticeUrls(Collections.singletonList(item.getWinNotice()));
            }
            // icon广告包含的图标类图片的宽高信息，一个广告最多只有一个icon 图片及其宽高，需在有道侧配置后，对应广告位方可下发该信息
            YdzxImageInfoDto ydzxIcon = item.getIcon();
            if (null != ydzxIcon) {
                tag.setIconUrl(ydzxIcon.getUrl());
            }
            if (null != item.getImages() && item.getImages().length > 0) {
                tag.setImgUrls(new ArrayList<>(Arrays.asList(item.getImages())));
            }
            // 统一替换成平台的宏(事件)
            tag.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__width_logic__", urls, MacroType.DP_WIDTH.getCode());
                urls = replaceMacro("__height_logic__", urls, MacroType.DP_HEIGHT.getCode());

                urls = replaceMacro("__width__", urls, MacroType.WIDTH.getCode());
                urls = replaceMacro("__height__", urls, MacroType.HEIGHT.getCode());

                urls = replaceMacro("__up_y__", urls, MacroType.UP_Y.getCode());
                urls = replaceMacro("__up_logic_y__", urls, MacroType.DP_UP_Y.getCode());
                urls = replaceMacro("__up_x__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__up_logic_x__", urls, MacroType.DP_UP_X.getCode());

                urls = replaceMacro("__sld__", urls, MacroType.SLD.getCode());

                urls = replaceMacro("__down_y__", urls, MacroType.DOWN_Y.getCode());
                urls = replaceMacro("__down_logic_y__", urls, MacroType.DP_DOWN_Y.getCode());
                urls = replaceMacro("__down_x__", urls, MacroType.DOWN_X.getCode());
                urls = replaceMacro("__down_logic_x__", urls, MacroType.DP_DOWN_X.getCode());

                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tag);
        });
        return responseDto;
    }


    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        if (!reqDto.getBiddingSuccess()) {
            // 竟败不需要
            return SuperResult.rightResult();
        }
        Map<String, String> accountParamMap = ParamParser.parseParamByJson(reqDto.getAdvCustomParam());
        // 加密密钥
        String priceSecretKey = accountParamMap.get(PRICE_KEY);
        // 加密向量
        String integrityKey = accountParamMap.get(PRICE_INTEGRITY_KEY);
        if (StringUtils.isEmpty(priceSecretKey) || StringUtils.isEmpty(integrityKey)) {
            return SuperResult.badResult("no priceSecretKey");
        }
        // 是否有请求成功的
        boolean hasRight = false;
        String price = encrypt(reqDto.getPrice(), priceSecretKey, integrityKey);
        List<String> urls = replaceMacro("_YD_WIN_PRICE_", reqDto.getUrls(), price);
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }


    /**
     * 参数转换为广告主需要的格式
     */
    private Map<String, String> convertRequest(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Map<String, String> map = new HashMap<>();
        // 广告主广告位ID
        map.put("id", advDto.getTagCode());
        //App 版本号
        map.put("av", rtbDto.getApp().getAppVersion());
        //网络链接类型
        int[] conType = convertConnectType(rtbDto.getNetwork().getConnectType().getType());
        map.put("ct", conType[0] + "");
        // 子网类型
        map.put("dct", conType[1] + "");
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getAndroidId())) {
            map.put("udid", rtbDto.getDevice().getAndroidId().toUpperCase());
        } else if (StringUtils.isNotEmpty(rtbDto.getDevice().getIdfa())) {
            map.put("udid", rtbDto.getDevice().getIdfa().toUpperCase());
        }
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getIdfaMd5())) {
            map.put("idfa_md5", rtbDto.getDevice().getIdfaMd5().toUpperCase());
        }
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getAndroidIdMd5())) {
            map.put("auidmd5", rtbDto.getDevice().getAndroidIdMd5().toUpperCase());
        }
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getImei())) {
            map.put("imei", rtbDto.getDevice().getImei());
            map.put("imeimd5", Md5.md5(rtbDto.getDevice().getImei().toUpperCase()).toUpperCase());
        } else {
            if (StringUtils.isNotEmpty(rtbDto.getDevice().getImeiMd5())) {
                map.put("imeimd5", rtbDto.getDevice().getImeiMd5().toUpperCase());
            }
        }
        // aaid 暂无 设备广告 id，当 os 类型为 Android 时，表示 Android Advertising ID ，当 os 类型为 IOS 时，表示阿里巴巴广告标
        // map.put("aaid","");
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getOaid())) {
            map.put("oaid", rtbDto.getDevice().getOaid());
            map.put("oaid_md5", Md5.md5(rtbDto.getDevice().getOaid().toUpperCase()).toUpperCase());
        } else {
            if (StringUtils.isNotEmpty(rtbDto.getDevice().getOaidMd5())) {
                map.put("oaid_md5", rtbDto.getDevice().getOaidMd5().toUpperCase());
            }
        }

        if (StringUtils.isNotEmpty(rtbDto.getDevice().getIdfv())) {
            map.put("idfv", rtbDto.getDevice().getIdfv());
        }
        if (!CollectionUtils.isEmpty(rtbDto.getDevice().getCaids())) {
            RequestCaidDto caidDto = rtbDto.getDevice().getCaids().get(0);
            String caId = caidDto.getCaid();
            String version = "0";
            if (StringUtils.isNotEmpty(caidDto.getVersion())) {
                version = caidDto.getVersion();
            }
            map.put("caId", caId + "_" + version);
            map.put("caid_md5", Md5.md5(caId.toUpperCase()).toUpperCase() + "_" + version);
        }
        map.put("rip", rtbDto.getNetwork().getIp());
        if (null != rtbDto.getGeo().getLatitude() && null != rtbDto.getGeo().getLongitude()) {
            String latitude = rtbDto.getGeo().getLatitude().toString();
            String longitude = rtbDto.getGeo().getLongitude().toString();
            map.put("ll", latitude + "," + longitude);
            // 计算 精确度
            String[] arr = latitude.split("\\.");
            if (arr.length == 2) {
                int len = arr[1].length();
                if (len == 3) {
                    map.put("lla", "100");
                } else if (len == 4) {
                    map.put("lla", "10");
                } else if (len >= 5) {
                    map.put("lla", "1");
                }
            }
        }
        // llt 暂无 获取位置信息的时间与发起广告请求的时间差，单位为分钟
        // map.put("llt","");
        // llp 定位所用的 provider，n(network) 为网络定位，g(gps) 为 gps 定位,p(passive) 为其他 app 里面的定位信息，f(fused) 为系统返回的最佳定位
        // map.put("llp","");
        map.put("wifi", "");
        if (StringUtils.isNotEmpty(rtbDto.getNetwork().getWifiMac())) {
            map.put("wifi", rtbDto.getNetwork().getWifiMac() + "," + rtbDto.getNetwork().getSsid());
        }
        // dn 暂无 设备信息，格式为“Build.MANUFACTURER,Build.MODEL,Build.PRODUCT” Android 如:samsung,GT-S5830,GT-S5830；iOS 如：iPhone5,3
        // map.put("dn","");
        // mntid 暂无 系统 id，例如：C02ML9CFFD5
        // map.put("mntid","");

        if (StringUtils.isNotEmpty(rtbDto.getDevice().getDeviceName())) {
            map.put("dname", Md5.md5(rtbDto.getDevice().getDeviceName().toUpperCase()).toUpperCase());
        } else {
            if (StringUtils.isNotEmpty(rtbDto.getDevice().getDeviceNameMd5())) {
                map.put("dname", rtbDto.getDevice().getDeviceNameMd5().toUpperCase());
            }
        }
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getTimeZone())) {
            map.put("z", rtbDto.getDevice().getTimeZone());
        }
        // 广告物料是否需要满足 HTTPS URL，如果值为 1，表示需 要返回物料 URL 为 HTTPS 协议 (包括图片素材、展示点击 tracker 等 URL)；如果无此参数，表示无 HTTPS 要求，但广告物料 URL 可能为 HTTPS
        // map.put("isSecure","");
        // 操作系统版本信息，如：3.1.2，注意：请填写版本名，而不是 API 等级
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getOsVersion())) {
            map.put("osv", rtbDto.getDevice().getOsVersion());
        }
        // 竖 屏 横 屏, 可 能 值 分 别 为:p,l,s,u; u: 未知,p:portrait,l:landscape,s:square
        if (null != rtbDto.getDevice().getOrientation()) {
            map.put("o", convertOrientation(rtbDto.getDevice().getOrientation().getType()));
        }
        // 国家类型，如中国 460, 参考Mobilecountry code
        //map.put("mcc","");
        // 运营商，如移动 00 , 联通 01, 参考Mobilecountry code
        if (null != rtbDto.getNetwork().getCarrierType()) {
            String[] carrier = convertCarrier(rtbDto.getNetwork().getCarrierType().getType());
            if (null != carrier) {
                map.put("mnc", carrier[0]);
                //运营商名，值可能为‘中国联通’
                map.put("cn", carrier[1]);
            }
        }
        // iso 国家代号，值如 cn
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getCountry())) {
            map.put("iso", rtbDto.getDevice().getCountry());
        }
        // lac 小区码
        // cid 基站码，更加准确定位位置
        // ran 一次请求的广告数量
        // cids 在同一个信息流广告会话中，会将最新加载的广告推广创意的 ID 都传送给服务端，以便服务端进行广告去重
        // isrd 当希望访问 clktracker 后跳转到 clk，即通过点击跟踪链接 由有道的服务跳转到落地页时，可以通过 isrd=1 来启用此参数

        // sc_h 设备屏幕物理尺寸高度，单位：像素
        if (null != rtbDto.getDevice().getHeight()) {
            map.put("sc_h", rtbDto.getDevice().getHeight() + "");
        }
        if (null != rtbDto.getDevice().getWidth()) {
            map.put("sc_w", rtbDto.getDevice().getWidth() + "");
        }
        // sc_a 屏 幕 分 辨 率， 值 如：1.0. Android 平 台 参 考DisplayMetrics.density, iOS 平台参考UIScreen.scale
        // sc_ppi 设备屏幕像素密度
        if (null != rtbDto.getDevice().getPpi()) {
            map.put("sc_ppi", rtbDto.getDevice().getPpi() + "");
        }
        // user_id 为用户在开发者域名下的 cookie ID，如果为 PC 广告位，此字段必填
        // m_age 用户的年龄
        if (null != rtbDto.getUser().getAge()) {
            map.put("m_age", rtbDto.getUser().getAge() + "");
        }
        if (null != rtbDto.getUser().getGender()) {
            String gender = rtbDto.getUser().getGender();
            if ("F".equalsIgnoreCase(gender)) {
                map.put("m_gender", 1 + "");
            } else if ("M".equalsIgnoreCase(gender)) {
                map.put("m_gender", 2 + "");
            } else {
                map.put("m_gender", 0 + "");
            }
        }
        if (null != rtbDto.getUser().getInterest() && rtbDto.getUser().getInterest().length > 0) {
            map.put("por_words", StringUtils.join(rtbDto.getUser().getInterest(), ","));
        }
        // phone_name 手机用户的名称
        // pwot power on time: 从开机到请求广告的时间, 单位 ms
        // imsi SIM 卡串号
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getImsi())) {
            map.put("imsi", rtbDto.getDevice().getImsi());
        }
        // romv rom version: 手机 ROM 的版本
        // sysct system compiling timestamp: 系统编译时间,rom 的编译时间 (时间戳), 如:”1596270702.486691”, 整数部分精确到 s,小数部分精确到 ns
        // dlg device language: 设备语言, 如:zh-CN
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getLanguage())) {
            map.put("language", rtbDto.getDevice().getLanguage());
        }
        // dst device startup timestamp: 手机开机时间 (时间戳), 如:”1596270702.486691”, 整数部分精确到 s, 小数部分精确到 ns
        if (null != rtbDto.getDevice().getSysStartTime()) {
            Long next = TimeUtils.convertMilliSecond(rtbDto.getDevice().getSysStartTime());
            map.put("dst", (next / 1000) + "");
        }
        if (null != rtbDto.getDevice().getSysUpdateTime()) {
            Long next = TimeUtils.convertMilliSecond(rtbDto.getDevice().getSysUpdateTime());
            map.put("dut", (next / 1000) + "");
        }
        // dsdt device start during time: 设备初始化时间, 如:”0.95873921”,整数部分精确到 s, 小数部分精确到 ns
        // cpum cpu 数目
        if (null != rtbDto.getDevice().getCpuNum()) {
            map.put("cpum", rtbDto.getDevice().getCpuNum() + "");
        }
        if (null != rtbDto.getDevice().getDeviceHardDisk()) {
            map.put("rom_t", rtbDto.getDevice().getDeviceHardDisk() + "");
        }
        if (null != rtbDto.getDevice().getDeviceMemory()) {
            map.put("ram_t", rtbDto.getDevice().getDeviceMemory() + "");
        }
        if (null != rtbDto.getDevice().getHardwareModel()) {
            map.put("device_type", rtbDto.getDevice().getHardwareModel());
        }
        // dsid 苹果账户 id
        // spt_mkt 是否请求厂商应用商店下载类广告
        // isu
        map.put("isu", "0");
        // rstyleID 广告位样式 ID，携带该字段时 ，广告物料会按照指定样式的尺寸进行响应，一次请求只能携带 1 个样式 ID；如无特殊需求，不建议使用单一样式 ID 请求，以免影响流量填充

        // 本次请求的底价(单位：分),需联系有道侧商务开通此广告 位的动态底价功能后方可生效
        if (null != rtbDto.getTag().getPrice()) {
            map.put("bidFloor", rtbDto.getTag().getPrice().toString());
        }
        //HMS Core版本号，实现被推广应用的静默安装依赖HMS Core能力。hwidv>=50200100。
        if (StringUtils.isNotEmpty(rtbDto.getDevice().getHmsVersion())) {
            map.put("hwidv", rtbDto.getDevice().getHmsVersion());
        }
        return map;
    }

    private String[] convertCarrier(Integer type) {
        CarrierType carrier = CarrierType.findByType(type);
        switch (carrier) {
            case CM:
                return new String[]{"00", carrier.getDesc()};
            case CU:
                return new String[]{"01", carrier.getDesc()};
            case CT:
                return new String[]{"03", carrier.getDesc()};
        }
        return null;
    }

    private String convertOrientation(Integer type) {
        OrientationType orientation = OrientationType.findByType(type);
        switch (orientation) {
            case VERTICAL:
                return "p";
            case HORIZONTAL:
                return "s";
            default:
                return "u";
        }
    }

    private int[] convertConnectType(Integer type) {
        int[] result = {0, 0};
        ConnectionType connectionType = ConnectionType.findByType(type);
        switch (connectionType) {
            case ETHERNET:
                result[0] = 1;
                break;
            case WIFI:
                result[0] = 2;
                break;
            case NETWORK_2G:
                result[0] = 3;
                result[1] = 11;
                break;
            case NETWORK_3G:
                result[0] = 3;
                result[1] = 12;
                break;
            case NETWORK_4G:
                result[0] = 3;
                result[1] = 13;
                break;
            case NETWORK_5G:
                result[0] = 3;
                result[1] = 50;
                break;
            case NETWORK_CELLULAR:
                result[0] = 3;
                break;
            default:
                break;
        }
        return result;
    }

    // 价格加密
    public static String encrypt(Double price, String encryptionKeyStr, String integrityKeyStr) {
        try {
            byte[] plainText = ByteBuffer.allocate(8).putLong(price.longValue()).array();
            SecretKeySpec encryptionKey = new SecretKeySpec(DatatypeConverter.parseHexBinary(encryptionKeyStr), "HmacSHA1");
            SecretKeySpec integrityKey = new SecretKeySpec(DatatypeConverter.parseHexBinary(integrityKeyStr), "HmacSHA1");
            long timestamp = System.currentTimeMillis();
            int seconds = (int) (timestamp / 1000L);
            int microSeconds = (int) ((timestamp - seconds * 1000) * 1000L);
            byte[] iv = new byte[INITIALIZATION_VECTOR_SIZE];
            for (int i = 3; i >= 0; i--) {
                iv[i] = (byte) (seconds & 0xffL);
                seconds >>= 8;
            }
            for (int i = 7; i >= 4; i--) {
                iv[i] = (byte) (microSeconds & 0xffL);
                microSeconds >>= 8;
            }
            final byte[] result = new byte[INITIALIZATION_VECTOR_SIZE + plainText.length + SIGNATURE_SIZE];
            System.arraycopy(iv, 0, result, 0, INITIALIZATION_VECTOR_SIZE);
            final Mac hmacer = Mac.getInstance("HmacSHA1");
            boolean add_iv_counter_byte = true;

            for (int resultIndex = INITIALIZATION_VECTOR_SIZE, plainIndex = 0; plainIndex < plainText.length; ) {
                hmacer.reset();
                hmacer.init(encryptionKey);
                final byte[] pad = hmacer.doFinal(iv);
                for (int i = 0; i < BLOCK_SIZE && plainIndex < plainText.length; resultIndex++, plainIndex++, i++) {
                    result[resultIndex] = (byte) (plainText[plainIndex] ^ pad[i]);
                }
                if (!add_iv_counter_byte) {
                    add_iv_counter_byte = ++iv[iv.length - 1] == 0;
                }
                if (add_iv_counter_byte) {
                    add_iv_counter_byte = false;
                    iv = Arrays.copyOf(iv, iv.length + 1);
                }
            }
            final byte[] sig = new byte[INITIALIZATION_VECTOR_SIZE + plainText.length];
            System.arraycopy(plainText, 0, sig, 0, plainText.length);
            System.arraycopy(iv, 0, sig, plainText.length, 16);
            hmacer.reset();
            hmacer.init(integrityKey);
            byte[] signature = hmacer.doFinal(sig);
            System.arraycopy(signature, 0, result, INITIALIZATION_VECTOR_SIZE + plainText.length, 4);
            return Base64.encodeBase64URLSafeString(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
