// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: iqiyi_bid_V4.8.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.aiqiyi.dto;

public final class AiQiYiPeqRespDto {
  private AiQiYiPeqRespDto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      AiQiYiPeqRespDto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_Entry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_Entry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Imp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Imp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Imp_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Imp_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Imp_Native_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Imp_Native_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Imp_Native_Image_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Imp_Native_Image_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Imp_Native_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Imp_Native_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Site_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Site_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_App_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_App_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Publisher_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Publisher_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Content_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Content_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Producer_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Producer_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Device_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Device_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Device_Caid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Device_Caid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Device_CaidInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Device_CaidInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_Geo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_Geo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_User_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_User_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidRequest_DeduplicatedId_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidRequest_DeduplicatedId_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_AdVideo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_AdVideo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_Opening_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_Opening_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_TvAd_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_TvAd_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_TvAd_Html5_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_TvAd_Html5_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_AdmVideo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_AdmVideo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_AdmNative_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_AdmNative_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_Image_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_Image_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_Video_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_Video_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_DownloadTracker_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_DownloadTracker_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ad_BidResponse_Bid_Link_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ad_BidResponse_Bid_Link_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024iqiyi_bid_V4.8.proto\022\002ad\"?\n\005Entry\022\020\n\003k" +
      "ey\030\001 \001(\tH\000\210\001\001\022\022\n\005value\030\002 \001(\tH\001\210\001\001B\006\n\004_ke" +
      "yB\010\n\006_value\"\2163\n\nBidRequest\022\n\n\002id\030\001 \001(\t\022\021" +
      "\n\ttimestamp\030\002 \001(\003\022\037\n\003imp\030\003 \003(\0132\022.ad.BidR" +
      "equest.Imp\022\031\n\014resourcetype\030\004 \001(\005H\000\210\001\001\022&\n" +
      "\004site\030\005 \001(\0132\023.ad.BidRequest.SiteH\001\210\001\001\022$\n" +
      "\003app\030\006 \001(\0132\022.ad.BidRequest.AppH\002\210\001\001\022*\n\006d" +
      "evice\030\007 \001(\0132\025.ad.BidRequest.DeviceH\003\210\001\001\022" +
      "&\n\004user\030\010 \001(\0132\023.ad.BidRequest.UserH\004\210\001\001\022" +
      "\021\n\004test\030\t \001(\010H\005\210\001\001\0227\n\020deduplicated_ids\030\n" +
      " \003(\0132\035.ad.BidRequest.DeduplicatedId\032\207\013\n\003" +
      "Imp\022\021\n\tadzone_id\030\001 \001(\t\022\034\n\017media_adzone_i" +
      "d\030\007 \001(\tH\000\210\001\001\022/\n\007ad_type\030\004 \001(\0162\031.ad.BidRe" +
      "quest.Imp.AdTypeH\001\210\001\001\022,\n\005video\030\002 \001(\0132\030.a" +
      "d.BidRequest.Imp.VideoH\002\210\001\001\022.\n\006native\030\003 " +
      "\001(\0132\031.ad.BidRequest.Imp.NativeH\003\210\001\001\022\025\n\010b" +
      "idfloor\030\006 \001(\005H\004\210\001\001\022\033\n\023allowed_action_typ" +
      "e\030\010 \003(\005\032\214\003\n\005Video\022\t\n\001w\030\001 \001(\005\022\t\n\001h\030\002 \001(\005\022" +
      "\023\n\013minduration\030\003 \001(\005\022\023\n\013maxduration\030\004 \001(" +
      "\005\022\022\n\nstartdelay\030\005 \001(\005\022:\n\tlinearity\030\006 \001(\016" +
      "2\'.ad.BidRequest.Imp.Video.VideoLinearit" +
      "y\022$\n\027accepted_creative_types\030\t \001(\005H\000\210\001\001\022" +
      "\030\n\013maxadscount\030\007 \001(\005H\001\210\001\001\0223\n\006format\030\010 \001(" +
      "\0162\036.ad.BidRequest.Imp.VideoFormatH\002\210\001\001\"G" +
      "\n\016VideoLinearity\022\016\n\nVL_UNKNOWN\020\000\022\n\n\006LINE" +
      "AR\020\001\022\016\n\nNON_LINEAR\020\002\022\t\n\005PAUSE\020\003B\032\n\030_acce" +
      "pted_creative_typesB\016\n\014_maxadscountB\t\n\007_" +
      "format\032\307\004\n\006Native\022\026\n\ttitle_len\030\001 \001(\005H\000\210\001" +
      "\001\022-\n\004imgs\030\002 \003(\0132\037.ad.BidRequest.Imp.Nati" +
      "ve.Image\0223\n\005video\030\003 \001(\0132\037.ad.BidRequest." +
      "Imp.Native.VideoH\001\210\001\001\022\030\n\013maxadscount\030\004 \001" +
      "(\005H\002\210\001\001\032\364\001\n\005Image\022A\n\004type\030\001 \001(\0162..ad.Bid" +
      "Request.Imp.Native.Image.ImageAssetTypeH" +
      "\000\210\001\001\022\016\n\001w\030\002 \001(\005H\001\210\001\001\022\016\n\001h\030\003 \001(\005H\002\210\001\001\022\021\n\004" +
      "wmin\030\004 \001(\005H\003\210\001\001\022\021\n\004hmin\030\005 \001(\005H\004\210\001\001\";\n\016Im" +
      "ageAssetType\022\013\n\007UNKNOWN\020\000\022\010\n\004ICON\020\001\022\010\n\004L" +
      "OGO\020\002\022\010\n\004MAIN\020\003B\007\n\005_typeB\004\n\002_wB\004\n\002_hB\007\n\005" +
      "_wminB\007\n\005_hmin\032\207\001\n\005Video\022\t\n\001w\030\001 \001(\005\022\t\n\001h" +
      "\030\002 \001(\005\022\023\n\013minduration\030\003 \001(\005\022\023\n\013maxdurati" +
      "on\030\004 \001(\005\0223\n\006format\030\005 \001(\0162\036.ad.BidRequest" +
      ".Imp.VideoFormatH\000\210\001\001B\t\n\007_formatB\014\n\n_tit" +
      "le_lenB\010\n\006_videoB\016\n\014_maxadscount\"5\n\006AdTy" +
      "pe\022\010\n\004ROLL\020\000\022\t\n\005FEEDS\020\001\022\013\n\007OPENING\020\002\022\t\n\005" +
      "PAUSE\020\003\":\n\013VideoFormat\022\r\n\tVIDEO_ANY\020\000\022\r\n" +
      "\tVIDEO_FLV\020\001\022\r\n\tVIDEO_MP4\020\002B\022\n\020_media_ad" +
      "zone_idB\n\n\010_ad_typeB\010\n\006_videoB\t\n\007_native" +
      "B\013\n\t_bidfloor\032\242\003\n\004Site\022\021\n\004name\030\001 \001(\tH\000\210\001" +
      "\001\022\023\n\006domain\030\002 \001(\tH\001\210\001\001\022\013\n\003cat\030\003 \003(\t\022\017\n\007p" +
      "agecat\030\004 \003(\t\022\021\n\004page\030\005 \001(\tH\002\210\001\001\022\032\n\rpriva" +
      "cypolicy\030\006 \001(\010H\003\210\001\001\022\020\n\003ref\030\007 \001(\tH\004\210\001\001\022\023\n" +
      "\006search\030\010 \001(\tH\005\210\001\001\0220\n\tpublisher\030\t \001(\0132\030." +
      "ad.BidRequest.PublisherH\006\210\001\001\022,\n\007content\030" +
      "\n \001(\0132\026.ad.BidRequest.ContentH\007\210\001\001\022\025\n\010ke" +
      "ywords\030\013 \001(\tH\010\210\001\001\022\023\n\006mobile\030\014 \001(\010H\t\210\001\001B\007" +
      "\n\005_nameB\t\n\007_domainB\007\n\005_pageB\020\n\016_privacyp" +
      "olicyB\006\n\004_refB\t\n\007_searchB\014\n\n_publisherB\n" +
      "\n\010_contentB\013\n\t_keywordsB\t\n\007_mobile\032\224\003\n\003A" +
      "pp\022\021\n\004name\030\001 \001(\tH\000\210\001\001\022\023\n\006domain\030\002 \001(\tH\001\210" +
      "\001\001\022\013\n\003cat\030\003 \003(\t\022\020\n\003ver\030\004 \001(\tH\002\210\001\001\022\023\n\006bun" +
      "dle\030\005 \001(\tH\003\210\001\001\022\021\n\004paid\030\006 \001(\010H\004\210\001\001\0220\n\tpub" +
      "lisher\030\007 \001(\0132\030.ad.BidRequest.PublisherH\005" +
      "\210\001\001\022,\n\007content\030\010 \001(\0132\026.ad.BidRequest.Con" +
      "tentH\006\210\001\001\022\025\n\010keywords\030\t \001(\tH\007\210\001\001\022\025\n\010stor" +
      "eurl\030\n \001(\tH\010\210\001\001\022\032\n\rdeeplinkstate\030\013 \001(\005H\t" +
      "\210\001\001B\007\n\005_nameB\t\n\007_domainB\006\n\004_verB\t\n\007_bund" +
      "leB\007\n\005_paidB\014\n\n_publisherB\n\n\010_contentB\013\n" +
      "\t_keywordsB\013\n\t_storeurlB\020\n\016_deeplinkstat" +
      "e\032l\n\tPublisher\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\021\n\004name\030" +
      "\002 \001(\tH\001\210\001\001\022\013\n\003cat\030\003 \003(\t\022\023\n\006domain\030\004 \001(\tH" +
      "\002\210\001\001B\005\n\003_idB\007\n\005_nameB\t\n\007_domain\032\276\t\n\007Cont" +
      "ent\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\024\n\007episode\030\002 \001(\005H\001\210" +
      "\001\001\022\022\n\005title\030\003 \001(\tH\002\210\001\001\022\023\n\006series\030\004 \001(\tH\003" +
      "\210\001\001\022\023\n\006season\030\005 \001(\tH\004\210\001\001\022\023\n\006artist\030\006 \001(\t" +
      "H\005\210\001\001\022\022\n\005genre\030\007 \001(\tH\006\210\001\001\022\022\n\005album\030\010 \001(\t" +
      "H\007\210\001\001\022\020\n\003url\030\t \001(\tH\010\210\001\001\022\013\n\003cat\030\n \003(\t\022<\n\005" +
      "prodq\030\013 \001(\0162(.ad.BidRequest.Content.Prod" +
      "uctionQualityH\t\210\001\001\022;\n\007context\030\014 \001(\0162%.ad" +
      ".BidRequest.Content.ContentContextH\n\210\001\001\022" +
      "\032\n\rcontentrating\030\r \001(\tH\013\210\001\001\022\027\n\nuserratin" +
      "g\030\016 \001(\tH\014\210\001\001\022\025\n\010keywords\030\017 \001(\tH\r\210\001\001\022\027\n\nl" +
      "ivestream\030\020 \001(\010H\016\210\001\001\022\037\n\022sourcerelationsh" +
      "ip\030\021 \001(\010H\017\210\001\001\022.\n\010producer\030\022 \001(\0132\027.ad.Bid" +
      "Request.ProducerH\020\210\001\001\022\020\n\003len\030\023 \001(\005H\021\210\001\001\022" +
      "B\n\016qagmediarating\030\024 \001(\0162%.ad.BidRequest." +
      "Content.QAGMediaRatingH\022\210\001\001\022\027\n\nembeddabl" +
      "e\030\025 \001(\010H\023\210\001\001\022\025\n\010language\030\026 \001(\tH\024\210\001\001\"\\\n\021P" +
      "roductionQuality\022\023\n\017QUALITY_UNKNOWN\020\000\022\020\n" +
      "\014PROFESSIONAL\020\001\022\014\n\010PROSUMER\020\002\022\022\n\016USER_GE" +
      "NERATED\020\003\"{\n\016ContentContext\022\016\n\nCC_UNKNOW" +
      "N\020\000\022\t\n\005VIDEO\020\001\022\010\n\004GAME\020\002\022\t\n\005MUSIC\020\003\022\017\n\013A" +
      "PPLICATION\020\004\022\010\n\004TEXT\020\005\022\t\n\005OTHER\020\006\022\023\n\017CON" +
      "TEXT_UNKNOWN\020\007\"X\n\016QAGMediaRating\022\021\n\rMR_C" +
      "C_UNKNOWN\020\000\022\021\n\rALL_AUDIENCES\020\001\022\024\n\020EVERYO" +
      "NE_OVER_12\020\002\022\n\n\006MATURE\020\003B\005\n\003_idB\n\n\010_epis" +
      "odeB\010\n\006_titleB\t\n\007_seriesB\t\n\007_seasonB\t\n\007_" +
      "artistB\010\n\006_genreB\010\n\006_albumB\006\n\004_urlB\010\n\006_p" +
      "rodqB\n\n\010_contextB\020\n\016_contentratingB\r\n\013_u" +
      "serratingB\013\n\t_keywordsB\r\n\013_livestreamB\025\n" +
      "\023_sourcerelationshipB\013\n\t_producerB\006\n\004_le" +
      "nB\021\n\017_qagmediaratingB\r\n\013_embeddableB\013\n\t_" +
      "language\032k\n\010Producer\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\021\n" +
      "\004name\030\002 \001(\tH\001\210\001\001\022\013\n\003cat\030\003 \003(\t\022\023\n\006domain\030" +
      "\004 \001(\tH\002\210\001\001B\005\n\003_idB\007\n\005_nameB\t\n\007_domain\032\266\016" +
      "\n\006Device\022\n\n\002ua\030\001 \001(\t\022\017\n\002ip\030\002 \001(\tH\000\210\001\001\022\037\n" +
      "\003geo\030\003 \001(\0132\022.ad.BidRequest.Geo\022\021\n\004idfa\030\004" +
      " \001(\tH\001\210\001\001\022\025\n\010idfa_md5\030\033 \001(\tH\002\210\001\001\022\025\n\010imei" +
      "_md5\030\005 \001(\tH\003\210\001\001\022\026\n\tandroidid\030\006 \001(\tH\004\210\001\001\022" +
      "\032\n\randroidid_md5\030\034 \001(\tH\005\210\001\001\022\021\n\004oaid\030\007 \001(" +
      "\tH\006\210\001\001\022\025\n\010oaid_md5\030\035 \001(\tH\007\210\001\001\022\020\n\003mac\030\010 \001" +
      "(\tH\010\210\001\001\022\036\n\021processed_mac_md5\030\036 \001(\tH\t\210\001\001\022" +
      "\021\n\004ipv6\030\021 \001(\tH\n\210\001\001\022\024\n\007carrier\030\022 \001(\005H\013\210\001\001" +
      "\022\021\n\004make\030\023 \001(\tH\014\210\001\001\022\022\n\005model\030\024 \001(\tH\r\210\001\001\022" +
      "\017\n\002os\030\025 \001(\tH\016\210\001\001\022\016\n\001w\030\027 \001(\005H\017\210\001\001\022\016\n\001h\030\030 " +
      "\001(\005H\020\210\001\001\022A\n\016connectiontype\030\031 \001(\0162$.ad.Bi" +
      "dRequest.Device.ConnectionTypeH\021\210\001\001\0229\n\nd" +
      "evicetype\030\032 \001(\0162 .ad.BidRequest.Device.D" +
      "eviceTypeH\022\210\001\001\022\024\n\007tv_type\030\037 \001(\005H\023\210\001\001\0226\n\t" +
      "caid_info\030  \001(\0132\036.ad.BidRequest.Device.C" +
      "aidInfoH\024\210\001\001\022\031\n\014country_code\030! \001(\tH\025\210\001\001\022" +
      "\032\n\rtime_zone_sec\030\" \001(\tH\026\210\001\001\022\034\n\017device_na" +
      "me_md5\030# \001(\tH\027\210\001\001\022\034\n\017device_language\030$ \001" +
      "(\tH\030\210\001\001\022\036\n\021machine_of_device\030% \001(\tH\031\210\001\001\022" +
      "\026\n\tboot_mark\030& \001(\tH\032\210\001\001\022\030\n\013update_mark\030\'" +
      " \001(\tH\033\210\001\001\022\020\n\003osv\030\026 \001(\tH\034\210\001\001\022\031\n\014carrier_n" +
      "ame\030( \001(\tH\035\210\001\001\022\027\n\ndisk_total\030) \001(\003H\036\210\001\001\022" +
      "\026\n\tmem_total\030* \001(\003H\037\210\001\001\022\023\n\006mnt_id\030+ \001(\tH" +
      " \210\001\001\022\033\n\016file_init_time\030, \001(\tH!\210\001\001\032D\n\004Cai" +
      "d\022\024\n\007version\030\001 \001(\tH\000\210\001\001\022\021\n\004caid\030\002 \001(\tH\001\210" +
      "\001\001B\n\n\010_versionB\007\n\005_caid\0324\n\010CaidInfo\022(\n\004c" +
      "aid\030\001 \003(\0132\032.ad.BidRequest.Device.Caid\"c\n" +
      "\nDeviceType\022\016\n\nDT_UNKNOWN\020\000\022\022\n\016UNKNOWN_D" +
      "EVICE\020\001\022\025\n\021PERSONAL_COMPUTER\020\002\022\006\n\002TV\020\003\022\t" +
      "\n\005PHONE\020\004\022\007\n\003PAD\020\005\"I\n\006TvType\022\013\n\007UNKNOWN\020" +
      "\000\022\017\n\013TV_TYPE_OTT\020\001\022\017\n\013TV_TYPE_STB\020\002\022\020\n\014T" +
      "V_TYPE_IPTV\020\003\"\206\001\n\016ConnectionType\022\026\n\022CONN" +
      "ECTION_UNKNOWN\020\000\022\014\n\010ETHERNET\020\001\022\010\n\004WIFI\020\002" +
      "\022\020\n\014CELL_UNKNOWN\020\003\022\013\n\007CELL_2G\020\004\022\013\n\007CELL_" +
      "3G\020\005\022\013\n\007CELL_4G\020\006\022\013\n\007CELL_5G\020\007B\005\n\003_ipB\007\n" +
      "\005_idfaB\013\n\t_idfa_md5B\013\n\t_imei_md5B\014\n\n_and" +
      "roididB\020\n\016_androidid_md5B\007\n\005_oaidB\013\n\t_oa" +
      "id_md5B\006\n\004_macB\024\n\022_processed_mac_md5B\007\n\005" +
      "_ipv6B\n\n\010_carrierB\007\n\005_makeB\010\n\006_modelB\005\n\003" +
      "_osB\004\n\002_wB\004\n\002_hB\021\n\017_connectiontypeB\r\n\013_d" +
      "evicetypeB\n\n\010_tv_typeB\014\n\n_caid_infoB\017\n\r_" +
      "country_codeB\020\n\016_time_zone_secB\022\n\020_devic" +
      "e_name_md5B\022\n\020_device_languageB\024\n\022_machi" +
      "ne_of_deviceB\014\n\n_boot_markB\016\n\014_update_ma" +
      "rkB\006\n\004_osvB\017\n\r_carrier_nameB\r\n\013_disk_tot" +
      "alB\014\n\n_mem_totalB\t\n\007_mnt_idB\021\n\017_file_ini" +
      "t_time\032\301\002\n\003Geo\022\020\n\003lat\030\001 \001(\001H\000\210\001\001\022\020\n\003lon\030" +
      "\002 \001(\001H\001\210\001\001\022\024\n\007country\030\003 \001(\tH\002\210\001\001\022\021\n\004prov" +
      "\030\006 \001(\tH\003\210\001\001\022\021\n\004city\030\007 \001(\tH\004\210\001\001\022\025\n\010distri" +
      "ct\030\n \001(\tH\005\210\001\001\0222\n\004type\030\t \001(\0162\037.ad.BidRequ" +
      "est.Geo.LocationTypeH\006\210\001\001\"K\n\014LocationTyp" +
      "e\022\016\n\nLT_UNKONWN\020\000\022\020\n\014GPS_LOCATION\020\001\022\006\n\002I" +
      "P\020\002\022\021\n\rUSER_PROVIDED\020\003B\006\n\004_latB\006\n\004_lonB\n" +
      "\n\010_countryB\007\n\005_provB\007\n\005_cityB\013\n\t_distric" +
      "tB\007\n\005_type\032\322\001\n\004User\022\017\n\002id\030\001 \001(\tH\000\210\001\001\022\020\n\003" +
      "age\030\006 \001(\005H\001\210\001\001\022\023\n\006gender\030\002 \001(\tH\002\210\001\001\022\025\n\010k" +
      "eywords\030\003 \001(\tH\003\210\001\001\022\024\n\007applist\030\004 \001(\tH\004\210\001\001" +
      "\022\021\n\004data\030\005 \001(\tH\005\210\001\001\022\017\n\002il\030\007 \001(\tH\006\210\001\001B\005\n\003" +
      "_idB\006\n\004_ageB\t\n\007_genderB\013\n\t_keywordsB\n\n\010_" +
      "applistB\007\n\005_dataB\005\n\003_il\032D\n\016DeduplicatedI" +
      "d\022\021\n\004type\030\001 \001(\005H\000\210\001\001\022\017\n\002id\030\002 \001(\tH\001\210\001\001B\007\n" +
      "\005_typeB\005\n\003_idB\017\n\r_resourcetypeB\007\n\005_siteB" +
      "\006\n\004_appB\t\n\007_deviceB\007\n\005_userB\007\n\005_test\"\374\037\n" +
      "\013BidResponse\022\n\n\002id\030\001 \001(\t\022 \n\003bid\030\005 \003(\0132\023." +
      "ad.BidResponse.Bid\022#\n\020extended_entries\030\006" +
      " \003(\0132\t.ad.Entry\022\027\n\ndebug_info\030\004 \001(\tH\000\210\001\001" +
      "\022\023\n\006status\030\007 \001(\005H\001\210\001\001\032\321\036\n\003Bid\022\021\n\tadzone_" +
      "id\030\001 \001(\t\0223\n\010admvideo\030\002 \001(\0132\034.ad.BidRespo" +
      "nse.Bid.AdmVideoH\000\210\001\001\0225\n\tadmnative\030\003 \001(\013" +
      "2\035.ad.BidResponse.Bid.AdmNativeH\001\210\001\001\022\014\n\004" +
      "crid\030\004 \001(\t\022\022\n\005price\030\005 \001(\005H\002\210\001\001\022\026\n\016win_no" +
      "tice_url\030\006 \003(\t\022\032\n\rmini_app_name\030\007 \001(\tH\003\210" +
      "\001\001\022\032\n\rmini_app_path\030\010 \001(\tH\004\210\001\001\022%\n\006action" +
      "\030\n \001(\0162\020.ad.AdActionTypeH\005\210\001\001\022\023\n\006ad_url\030" +
      "\013 \001(\tH\006\210\001\001\022\025\n\010ad_width\030\025 \001(\005H\007\210\001\001\022\026\n\tad_" +
      "height\030\026 \001(\005H\010\210\001\001\022\034\n\017detail_page_url\030\014 \001" +
      "(\tH\t\210\001\001\022\036\n\021app_desc_page_url\030\035 \001(\tH\n\210\001\001\022" +
      "\022\n\005title\030\016 \001(\tH\013\210\001\001\022\030\n\013description\030\017 \001(\t" +
      "H\014\210\001\001\022\025\n\010apk_name\030\020 \001(\tH\r\210\001\001\022\025\n\010app_name" +
      "\030\021 \001(\tH\016\210\001\001\022\030\n\013app_version\030\031 \001(\tH\017\210\001\001\022\032\n" +
      "\rapp_developer\030\032 \001(\tH\020\210\001\001\022\033\n\016app_permiss" +
      "ion\030\033 \001(\tH\021\210\001\001\022\030\n\013app_privacy\030\034 \001(\tH\022\210\001\001" +
      "\022\030\n\013app_feature\030  \001(\tH\023\210\001\001\022<\n\rcreative_t" +
      "ype\030\022 \001(\0162 .ad.BidResponse.Bid.CreativeT" +
      "ypeH\024\210\001\001\022\037\n\022creative_direction\030\030 \001(\005H\025\210\001" +
      "\001\022+\n\004link\030\023 \001(\0132\030.ad.BidResponse.Bid.Lin" +
      "kH\026\210\001\001\0222\n\010ad_video\030\024 \001(\0132\033.ad.BidRespons" +
      "e.Bid.AdVideoH\027\210\001\001\0221\n\007opening\030\027 \001(\0132\033.ad" +
      ".BidResponse.Bid.OpeningH\030\210\001\001\022,\n\005tv_ad\030\036" +
      " \001(\0132\030.ad.BidResponse.Bid.TvAdH\031\210\001\001\022#\n\020e" +
      "xtended_entries\030\037 \003(\0132\t.ad.Entry\032-\n\007AdVi" +
      "deo\022\025\n\010duration\030\001 \001(\005H\000\210\001\001B\013\n\t_duration\032" +
      "\220\001\n\007Opening\0223\n\004type\030\001 \001(\0162 .ad.BidRespon" +
      "se.Bid.Opening.TypeH\000\210\001\001\"G\n\004Type\022\021\n\rNON_" +
      "IMMERSIVE\020\000\022\035\n\031NON_IMMERSIVE_FULL_SCREEN" +
      "\020\001\022\r\n\tIMMERSIVE\020\002B\007\n\005_type\032\206\003\n\004TvAd\0222\n\005h" +
      "tml5\030\001 \001(\0132\036.ad.BidResponse.Bid.TvAd.Htm" +
      "l5H\000\210\001\001\032\277\002\n\005Html5\022\020\n\003url\030\001 \001(\tH\000\210\001\001\022\024\n\007x" +
      "_scale\030\002 \001(\002H\001\210\001\001\022\024\n\007y_scale\030\003 \001(\002H\002\210\001\001\022" +
      "\034\n\017max_width_scale\030\004 \001(\002H\003\210\001\001\022\035\n\020max_hei" +
      "ght_scale\030\005 \001(\002H\004\210\001\001\022\022\n\005width\030\006 \001(\005H\005\210\001\001" +
      "\022\023\n\006height\030\007 \001(\005H\006\210\001\001\022\036\n\021qr_overlay_acti" +
      "on\030\010 \001(\005H\007\210\001\001B\006\n\004_urlB\n\n\010_x_scaleB\n\n\010_y_" +
      "scaleB\022\n\020_max_width_scaleB\023\n\021_max_height" +
      "_scaleB\010\n\006_widthB\t\n\007_heightB\024\n\022_qr_overl" +
      "ay_actionB\010\n\006_html5\032\333\004\n\010AdmVideo\022\'\n\004imgs" +
      "\030\001 \003(\0132\031.ad.BidResponse.Bid.Image\022-\n\005vid" +
      "eo\030\002 \001(\0132\031.ad.BidResponse.Bid.VideoH\000\210\001\001" +
      "\022\022\n\005title\030\003 \001(\tH\001\210\001\001\022\021\n\004desc\030\004 \001(\tH\002\210\001\001\022" +
      "+\n\004link\030\007 \001(\0132\030.ad.BidResponse.Bid.LinkH" +
      "\003\210\001\001\022\031\n\014package_name\030\010 \001(\tH\004\210\001\001\022\025\n\010app_n" +
      "ame\030\t \001(\tH\005\210\001\001\022\025\n\010app_icon\030\n \001(\tH\006\210\001\001\022\030\n" +
      "\013app_version\030\013 \001(\tH\007\210\001\001\022\033\n\016ad_source_mar" +
      "k\030\014 \001(\tH\010\210\001\001\022 \n\023trueview_time_point\030\r \001(" +
      "\005H\t\210\001\001\022\031\n\021trueview_trackers\030\016 \003(\t\022!\n\024ski" +
      "ppable_time_point\030\017 \001(\005H\n\210\001\001\022\036\n\026trueview" +
      "_skip_trackers\030\020 \003(\tB\010\n\006_videoB\010\n\006_title" +
      "B\007\n\005_descB\007\n\005_linkB\017\n\r_package_nameB\013\n\t_" +
      "app_nameB\013\n\t_app_iconB\016\n\014_app_versionB\021\n" +
      "\017_ad_source_markB\026\n\024_trueview_time_point" +
      "B\027\n\025_skippable_time_point\032\337\002\n\tAdmNative\022" +
      "\022\n\005title\030\001 \001(\tH\000\210\001\001\022\'\n\004imgs\030\002 \003(\0132\031.ad.B" +
      "idResponse.Bid.Image\022-\n\005video\030\003 \001(\0132\031.ad" +
      ".BidResponse.Bid.VideoH\001\210\001\001\022+\n\004link\030\004 \001(" +
      "\0132\030.ad.BidResponse.Bid.LinkH\002\210\001\001\022\031\n\014pack" +
      "age_name\030\005 \001(\tH\003\210\001\001\022\025\n\010app_name\030\006 \001(\tH\004\210" +
      "\001\001\022\025\n\010app_icon\030\007 \001(\tH\005\210\001\001\022\030\n\013app_version" +
      "\030\010 \001(\tH\006\210\001\001B\010\n\006_titleB\010\n\006_videoB\007\n\005_link" +
      "B\017\n\r_package_nameB\013\n\t_app_nameB\013\n\t_app_i" +
      "conB\016\n\014_app_version\032\303\001\n\005Image\022\013\n\003url\030\001 \001" +
      "(\t\022\016\n\001w\030\002 \001(\005H\000\210\001\001\022\016\n\001h\030\003 \001(\005H\001\210\001\001\022;\n\004ty" +
      "pe\030\004 \001(\0162(.ad.BidResponse.Bid.Image.Imag" +
      "eAssetTypeH\002\210\001\001\";\n\016ImageAssetType\022\013\n\007UNK" +
      "NOWN\020\000\022\010\n\004ICON\020\001\022\010\n\004LOGO\020\002\022\010\n\004MAIN\020\003B\004\n\002" +
      "_wB\004\n\002_hB\007\n\005_type\032\332\001\n\005Video\022\013\n\003url\030\001 \001(\t" +
      "\022\025\n\010duration\030\002 \001(\005H\000\210\001\001\022\021\n\004desc\030\004 \001(\tH\001\210" +
      "\001\001\022\030\n\013start_cover\030\005 \001(\tH\002\210\001\001\022\033\n\016complete" +
      "_cover\030\006 \001(\tH\003\210\001\001\022\016\n\001w\030\007 \001(\005H\004\210\001\001\022\016\n\001h\030\010" +
      " \001(\005H\005\210\001\001B\013\n\t_durationB\007\n\005_descB\016\n\014_star" +
      "t_coverB\021\n\017_complete_coverB\004\n\002_wB\004\n\002_h\032m" +
      "\n\017DownloadTracker\022\025\n\rstartdownload\030\001 \003(\t" +
      "\022\026\n\016finishdownload\030\002 \003(\t\022\024\n\014startinstall" +
      "\030\003 \003(\t\022\025\n\rfinishinstall\030\004 \003(\t\032\203\003\n\004Link\022\014" +
      "\n\004curl\030\001 \001(\t\022\023\n\013imptrackers\030\002 \003(\t\022\025\n\rcli" +
      "cktrackers\030\003 \003(\t\022B\n\020downloadtrackers\030\004 \001" +
      "(\0132#.ad.BidResponse.Bid.DownloadTrackerH" +
      "\000\210\001\001\022\025\n\010deeplink\030\005 \001(\tH\001\210\001\001\022\030\n\020deeplinkt" +
      "rackers\030\006 \003(\t\022\025\n\rstarttrackers\030\007 \003(\t\022\030\n\020" +
      "completetrackers\030\010 \003(\t\022\037\n\027first_quartile" +
      "_trackers\030\t \003(\t\022\032\n\022mid_point_trackers\030\n " +
      "\003(\t\022\037\n\027third_quartile_trackers\030\013 \003(\t\022\033\n\023" +
      "conversion_trackers\030\014 \003(\tB\023\n\021_downloadtr" +
      "ackersB\013\n\t_deeplink\"\"\n\014CreativeType\022\007\n\003I" +
      "MG\020\000\022\t\n\005VIDEO\020\001\">\n\021CreativeDirection\022\013\n\007" +
      "UNKNOWN\020\000\022\016\n\nHORIZONTAL\020\001\022\014\n\010VERTICAL\020\002B" +
      "\013\n\t_admvideoB\014\n\n_admnativeB\010\n\006_priceB\020\n\016" +
      "_mini_app_nameB\020\n\016_mini_app_pathB\t\n\007_act" +
      "ionB\t\n\007_ad_urlB\013\n\t_ad_widthB\014\n\n_ad_heigh" +
      "tB\022\n\020_detail_page_urlB\024\n\022_app_desc_page_" +
      "urlB\010\n\006_titleB\016\n\014_descriptionB\013\n\t_apk_na" +
      "meB\013\n\t_app_nameB\016\n\014_app_versionB\020\n\016_app_" +
      "developerB\021\n\017_app_permissionB\016\n\014_app_pri" +
      "vacyB\016\n\014_app_featureB\020\n\016_creative_typeB\025" +
      "\n\023_creative_directionB\007\n\005_linkB\013\n\t_ad_vi" +
      "deoB\n\n\010_openingB\010\n\006_tv_adB\r\n\013_debug_info" +
      "B\t\n\007_status*\212\002\n\006Status\022\013\n\007SUCCESS\020\000\022+\n\'R" +
      "EQUEST_EMPTY__FIELD_OR_INVALID_MESSAGE\020\001" +
      "\022\"\n\036REQUEST_ROLL_LACK_VIDEO_OBJECT\020\002\022$\n " +
      "REQUEST_FEEDS_LACK_NATIVE_OBJECT\020\003\022#\n\037RE" +
      "QUEST_FEEDS_LACK_VIDEO_OR_IMG\020\004\022\035\n\031REQUE" +
      "ST_INVALID_DEVICE_IP\020\005\022\037\n\033REQUEST_INVALI" +
      "D_DEVICE_TYPE\020\006\022\027\n\023UNRECOGNIZED_DEVICE\020d" +
      "*\211\001\n\014AdActionType\022\013\n\007UNKNOWN\020\000\022\023\n\017OPEN_I" +
      "N_WEBVIEW\020\001\022\025\n\021OPEN_APP_DEEPLINK\020\002\022\020\n\014DO" +
      "WNLOAD_APP\020\003\022\033\n\027OPEN_APP_UNIVERSAL_LINK\020" +
      "\004\022\021\n\rOPEN_MINI_APP\020\005B6\n cn.taken.ad.logi" +
      "c.adv.aiqiyi.dtoB\020AiQiYiPeqRespDtoP\001b\006pr" +
      "oto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_ad_Entry_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ad_Entry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_Entry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_ad_BidRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ad_BidRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_descriptor,
        new java.lang.String[] { "Id", "Timestamp", "Imp", "Resourcetype", "Site", "App", "Device", "User", "Test", "DeduplicatedIds", });
    internal_static_ad_BidRequest_Imp_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidRequest_Imp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Imp_descriptor,
        new java.lang.String[] { "AdzoneId", "MediaAdzoneId", "AdType", "Video", "Native", "Bidfloor", "AllowedActionType", });
    internal_static_ad_BidRequest_Imp_Video_descriptor =
      internal_static_ad_BidRequest_Imp_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidRequest_Imp_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Imp_Video_descriptor,
        new java.lang.String[] { "W", "H", "Minduration", "Maxduration", "Startdelay", "Linearity", "AcceptedCreativeTypes", "Maxadscount", "Format", });
    internal_static_ad_BidRequest_Imp_Native_descriptor =
      internal_static_ad_BidRequest_Imp_descriptor.getNestedTypes().get(1);
    internal_static_ad_BidRequest_Imp_Native_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Imp_Native_descriptor,
        new java.lang.String[] { "TitleLen", "Imgs", "Video", "Maxadscount", });
    internal_static_ad_BidRequest_Imp_Native_Image_descriptor =
      internal_static_ad_BidRequest_Imp_Native_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidRequest_Imp_Native_Image_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Imp_Native_Image_descriptor,
        new java.lang.String[] { "Type", "W", "H", "Wmin", "Hmin", });
    internal_static_ad_BidRequest_Imp_Native_Video_descriptor =
      internal_static_ad_BidRequest_Imp_Native_descriptor.getNestedTypes().get(1);
    internal_static_ad_BidRequest_Imp_Native_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Imp_Native_Video_descriptor,
        new java.lang.String[] { "W", "H", "Minduration", "Maxduration", "Format", });
    internal_static_ad_BidRequest_Site_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(1);
    internal_static_ad_BidRequest_Site_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Site_descriptor,
        new java.lang.String[] { "Name", "Domain", "Cat", "Pagecat", "Page", "Privacypolicy", "Ref", "Search", "Publisher", "Content", "Keywords", "Mobile", });
    internal_static_ad_BidRequest_App_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(2);
    internal_static_ad_BidRequest_App_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_App_descriptor,
        new java.lang.String[] { "Name", "Domain", "Cat", "Ver", "Bundle", "Paid", "Publisher", "Content", "Keywords", "Storeurl", "Deeplinkstate", });
    internal_static_ad_BidRequest_Publisher_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(3);
    internal_static_ad_BidRequest_Publisher_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Publisher_descriptor,
        new java.lang.String[] { "Id", "Name", "Cat", "Domain", });
    internal_static_ad_BidRequest_Content_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(4);
    internal_static_ad_BidRequest_Content_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Content_descriptor,
        new java.lang.String[] { "Id", "Episode", "Title", "Series", "Season", "Artist", "Genre", "Album", "Url", "Cat", "Prodq", "Context", "Contentrating", "Userrating", "Keywords", "Livestream", "Sourcerelationship", "Producer", "Len", "Qagmediarating", "Embeddable", "Language", });
    internal_static_ad_BidRequest_Producer_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(5);
    internal_static_ad_BidRequest_Producer_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Producer_descriptor,
        new java.lang.String[] { "Id", "Name", "Cat", "Domain", });
    internal_static_ad_BidRequest_Device_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(6);
    internal_static_ad_BidRequest_Device_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Device_descriptor,
        new java.lang.String[] { "Ua", "Ip", "Geo", "Idfa", "IdfaMd5", "ImeiMd5", "Androidid", "AndroididMd5", "Oaid", "OaidMd5", "Mac", "ProcessedMacMd5", "Ipv6", "Carrier", "Make", "Model", "Os", "W", "H", "Connectiontype", "Devicetype", "TvType", "CaidInfo", "CountryCode", "TimeZoneSec", "DeviceNameMd5", "DeviceLanguage", "MachineOfDevice", "BootMark", "UpdateMark", "Osv", "CarrierName", "DiskTotal", "MemTotal", "MntId", "FileInitTime", });
    internal_static_ad_BidRequest_Device_Caid_descriptor =
      internal_static_ad_BidRequest_Device_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidRequest_Device_Caid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Device_Caid_descriptor,
        new java.lang.String[] { "Version", "Caid", });
    internal_static_ad_BidRequest_Device_CaidInfo_descriptor =
      internal_static_ad_BidRequest_Device_descriptor.getNestedTypes().get(1);
    internal_static_ad_BidRequest_Device_CaidInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Device_CaidInfo_descriptor,
        new java.lang.String[] { "Caid", });
    internal_static_ad_BidRequest_Geo_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(7);
    internal_static_ad_BidRequest_Geo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_Geo_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Country", "Prov", "City", "District", "Type", });
    internal_static_ad_BidRequest_User_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(8);
    internal_static_ad_BidRequest_User_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_User_descriptor,
        new java.lang.String[] { "Id", "Age", "Gender", "Keywords", "Applist", "Data", "Il", });
    internal_static_ad_BidRequest_DeduplicatedId_descriptor =
      internal_static_ad_BidRequest_descriptor.getNestedTypes().get(9);
    internal_static_ad_BidRequest_DeduplicatedId_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidRequest_DeduplicatedId_descriptor,
        new java.lang.String[] { "Type", "Id", });
    internal_static_ad_BidResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ad_BidResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_descriptor,
        new java.lang.String[] { "Id", "Bid", "ExtendedEntries", "DebugInfo", "Status", });
    internal_static_ad_BidResponse_Bid_descriptor =
      internal_static_ad_BidResponse_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidResponse_Bid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_descriptor,
        new java.lang.String[] { "AdzoneId", "Admvideo", "Admnative", "Crid", "Price", "WinNoticeUrl", "MiniAppName", "MiniAppPath", "Action", "AdUrl", "AdWidth", "AdHeight", "DetailPageUrl", "AppDescPageUrl", "Title", "Description", "ApkName", "AppName", "AppVersion", "AppDeveloper", "AppPermission", "AppPrivacy", "AppFeature", "CreativeType", "CreativeDirection", "Link", "AdVideo", "Opening", "TvAd", "ExtendedEntries", });
    internal_static_ad_BidResponse_Bid_AdVideo_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidResponse_Bid_AdVideo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_AdVideo_descriptor,
        new java.lang.String[] { "Duration", });
    internal_static_ad_BidResponse_Bid_Opening_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(1);
    internal_static_ad_BidResponse_Bid_Opening_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_Opening_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_ad_BidResponse_Bid_TvAd_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(2);
    internal_static_ad_BidResponse_Bid_TvAd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_TvAd_descriptor,
        new java.lang.String[] { "Html5", });
    internal_static_ad_BidResponse_Bid_TvAd_Html5_descriptor =
      internal_static_ad_BidResponse_Bid_TvAd_descriptor.getNestedTypes().get(0);
    internal_static_ad_BidResponse_Bid_TvAd_Html5_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_TvAd_Html5_descriptor,
        new java.lang.String[] { "Url", "XScale", "YScale", "MaxWidthScale", "MaxHeightScale", "Width", "Height", "QrOverlayAction", });
    internal_static_ad_BidResponse_Bid_AdmVideo_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(3);
    internal_static_ad_BidResponse_Bid_AdmVideo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_AdmVideo_descriptor,
        new java.lang.String[] { "Imgs", "Video", "Title", "Desc", "Link", "PackageName", "AppName", "AppIcon", "AppVersion", "AdSourceMark", "TrueviewTimePoint", "TrueviewTrackers", "SkippableTimePoint", "TrueviewSkipTrackers", });
    internal_static_ad_BidResponse_Bid_AdmNative_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(4);
    internal_static_ad_BidResponse_Bid_AdmNative_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_AdmNative_descriptor,
        new java.lang.String[] { "Title", "Imgs", "Video", "Link", "PackageName", "AppName", "AppIcon", "AppVersion", });
    internal_static_ad_BidResponse_Bid_Image_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(5);
    internal_static_ad_BidResponse_Bid_Image_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_Image_descriptor,
        new java.lang.String[] { "Url", "W", "H", "Type", });
    internal_static_ad_BidResponse_Bid_Video_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(6);
    internal_static_ad_BidResponse_Bid_Video_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_Video_descriptor,
        new java.lang.String[] { "Url", "Duration", "Desc", "StartCover", "CompleteCover", "W", "H", });
    internal_static_ad_BidResponse_Bid_DownloadTracker_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(7);
    internal_static_ad_BidResponse_Bid_DownloadTracker_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_DownloadTracker_descriptor,
        new java.lang.String[] { "Startdownload", "Finishdownload", "Startinstall", "Finishinstall", });
    internal_static_ad_BidResponse_Bid_Link_descriptor =
      internal_static_ad_BidResponse_Bid_descriptor.getNestedTypes().get(8);
    internal_static_ad_BidResponse_Bid_Link_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ad_BidResponse_Bid_Link_descriptor,
        new java.lang.String[] { "Curl", "Imptrackers", "Clicktrackers", "Downloadtrackers", "Deeplink", "Deeplinktrackers", "Starttrackers", "Completetrackers", "FirstQuartileTrackers", "MidPointTrackers", "ThirdQuartileTrackers", "ConversionTrackers", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
