// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: tianzao.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tianzao.dto;

public interface BidRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:BidRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 媒体请求时的唯一ID(需媒体自定义)
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * 媒体请求时的唯一ID(需媒体自定义)
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 本API版本号，例如：1.0.0
   * </pre>
   *
   * <code>string ver = 2;</code>
   * @return The ver.
   */
  java.lang.String getVer();
  /**
   * <pre>
   * 本API版本号，例如：1.0.0
   * </pre>
   *
   * <code>string ver = 2;</code>
   * @return The bytes for ver.
   */
  com.google.protobuf.ByteString
      getVerBytes();

  /**
   * <pre>
   * 广告位信息
   * </pre>
   *
   * <code>.BidRequest.Imp imp = 3;</code>
   * @return Whether the imp field is set.
   */
  boolean hasImp();
  /**
   * <pre>
   * 广告位信息
   * </pre>
   *
   * <code>.BidRequest.Imp imp = 3;</code>
   * @return The imp.
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.Imp getImp();
  /**
   * <pre>
   * 广告位信息
   * </pre>
   *
   * <code>.BidRequest.Imp imp = 3;</code>
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.ImpOrBuilder getImpOrBuilder();

  /**
   * <pre>
   * APP信息
   * </pre>
   *
   * <code>.BidRequest.App app = 4;</code>
   * @return Whether the app field is set.
   */
  boolean hasApp();
  /**
   * <pre>
   * APP信息
   * </pre>
   *
   * <code>.BidRequest.App app = 4;</code>
   * @return The app.
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.App getApp();
  /**
   * <pre>
   * APP信息
   * </pre>
   *
   * <code>.BidRequest.App app = 4;</code>
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.AppOrBuilder getAppOrBuilder();

  /**
   * <pre>
   * 用户信息
   * </pre>
   *
   * <code>optional .BidRequest.User user = 5;</code>
   * @return Whether the user field is set.
   */
  boolean hasUser();
  /**
   * <pre>
   * 用户信息
   * </pre>
   *
   * <code>optional .BidRequest.User user = 5;</code>
   * @return The user.
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.User getUser();
  /**
   * <pre>
   * 用户信息
   * </pre>
   *
   * <code>optional .BidRequest.User user = 5;</code>
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.UserOrBuilder getUserOrBuilder();

  /**
   * <code>.BidRequest.Device device = 6;</code>
   * @return Whether the device field is set.
   */
  boolean hasDevice();
  /**
   * <code>.BidRequest.Device device = 6;</code>
   * @return The device.
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.Device getDevice();
  /**
   * <code>.BidRequest.Device device = 6;</code>
   */
  cn.taken.ad.logic.adv.tianzao.dto.BidRequest.DeviceOrBuilder getDeviceOrBuilder();
}
