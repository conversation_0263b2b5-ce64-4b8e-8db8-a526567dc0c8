package cn.taken.ad.logic.adv.jym.dto.response;

import java.io.Serializable;
import java.util.List;

public class JymTrackingHeaderUrlDto implements Serializable {

    private static final long serialVersionUID = -6739966463866246763L;
    /**
     * 上报链接
     */
    private String url;
    /**
     * 当进行监测上报时，需要使用此字段循环里面的 key 之外，不要设置其他的 head 的 key 值，禁 止设置 cookie。
     * 注意：如该字段为空，则无需特别设置 head，保持原有处理逻辑即可
     */
    private List<JymReportHeaderDto> headers;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<JymReportHeaderDto> getHeaders() {
        return headers;
    }

    public void setHeaders(List<JymReportHeaderDto> headers) {
        this.headers = headers;
    }
}
