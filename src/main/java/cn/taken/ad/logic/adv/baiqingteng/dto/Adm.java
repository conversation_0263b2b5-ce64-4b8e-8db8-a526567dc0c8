// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: baiqingteng.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.baiqingteng.dto;

/**
 * Protobuf type {@code ad.Adm}
 */
public final class Adm extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:ad.Adm)
    AdmOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      Adm.class.getName());
  }
  // Use Adm.newBuilder() to construct.
  private Adm(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Adm() {
    templateId_ = 0;
    actionType_ = 0;
    title_ = com.google.protobuf.ByteString.EMPTY;
    desc_ = com.google.protobuf.ByteString.EMPTY;
    icon_ = com.google.protobuf.ByteString.EMPTY;
    img_ = java.util.Collections.emptyList();
    landdingPage_ = com.google.protobuf.ByteString.EMPTY;
    deeplink_ = com.google.protobuf.ByteString.EMPTY;
    appName_ = com.google.protobuf.ByteString.EMPTY;
    packageName_ = com.google.protobuf.ByteString.EMPTY;
    bundleId_ = com.google.protobuf.ByteString.EMPTY;
    publisher_ = com.google.protobuf.ByteString.EMPTY;
    appVersion_ = com.google.protobuf.ByteString.EMPTY;
    privacy_ = com.google.protobuf.ByteString.EMPTY;
    permission_ = com.google.protobuf.ByteString.EMPTY;
    ulkUrl_ = com.google.protobuf.ByteString.EMPTY;
    ulkScheme_ = com.google.protobuf.ByteString.EMPTY;
    appStoreLink_ = com.google.protobuf.ByteString.EMPTY;
    appIntroductionLink_ = com.google.protobuf.ByteString.EMPTY;
    downloadMidPage_ = com.google.protobuf.ByteString.EMPTY;
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return BaiQingTengPeqRespDto.internal_static_ad_Adm_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return BaiQingTengPeqRespDto.internal_static_ad_Adm_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            Adm.class, Adm.Builder.class);
  }

  public interface ImageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ad.Adm.Image)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 图片地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return Whether the url field is set.
     */
    boolean hasUrl();
    /**
     * <pre>
     * 图片地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return The url.
     */
    com.google.protobuf.ByteString getUrl();

    /**
     * <pre>
     * 图片宽
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return Whether the width field is set.
     */
    boolean hasWidth();
    /**
     * <pre>
     * 图片宽
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return The width.
     */
    int getWidth();

    /**
     * <pre>
     * 图片高
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return Whether the height field is set.
     */
    boolean hasHeight();
    /**
     * <pre>
     * 图片高
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return The height.
     */
    int getHeight();
  }
  /**
   * Protobuf type {@code ad.Adm.Image}
   */
  public static final class Image extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:ad.Adm.Image)
      ImageOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Image.class.getName());
    }
    // Use Image.newBuilder() to construct.
    private Image(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Image() {
      url_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_Image_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_Image_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Adm.Image.class, Adm.Image.Builder.class);
    }

    private int bitField0_;
    public static final int URL_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString url_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 图片地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return Whether the url field is set.
     */
    @java.lang.Override
    public boolean hasUrl() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 图片地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return The url.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUrl() {
      return url_;
    }

    public static final int WIDTH_FIELD_NUMBER = 2;
    private int width_ = 0;
    /**
     * <pre>
     * 图片宽
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return Whether the width field is set.
     */
    @java.lang.Override
    public boolean hasWidth() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 图片宽
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return The width.
     */
    @java.lang.Override
    public int getWidth() {
      return width_;
    }

    public static final int HEIGHT_FIELD_NUMBER = 3;
    private int height_ = 0;
    /**
     * <pre>
     * 图片高
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return Whether the height field is set.
     */
    @java.lang.Override
    public boolean hasHeight() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 图片高
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return The height.
     */
    @java.lang.Override
    public int getHeight() {
      return height_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, url_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, width_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, height_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, url_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, width_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, height_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Adm.Image)) {
        return super.equals(obj);
      }
      Adm.Image other = (Adm.Image) obj;

      if (hasUrl() != other.hasUrl()) return false;
      if (hasUrl()) {
        if (!getUrl()
            .equals(other.getUrl())) return false;
      }
      if (hasWidth() != other.hasWidth()) return false;
      if (hasWidth()) {
        if (getWidth()
            != other.getWidth()) return false;
      }
      if (hasHeight() != other.hasHeight()) return false;
      if (hasHeight()) {
        if (getHeight()
            != other.getHeight()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUrl()) {
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
      }
      if (hasWidth()) {
        hash = (37 * hash) + WIDTH_FIELD_NUMBER;
        hash = (53 * hash) + getWidth();
      }
      if (hasHeight()) {
        hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getHeight();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Adm.Image parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Image parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Image parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Image parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Image parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Image parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Image parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static Adm.Image parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static Adm.Image parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static Adm.Image parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Adm.Image parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static Adm.Image parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Adm.Image prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ad.Adm.Image}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ad.Adm.Image)
        Adm.ImageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Image_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Image_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Adm.Image.class, Adm.Image.Builder.class);
      }

      // Construct using cn.taken.ad.logic.adv.baiqingteng.dto.Adm.Image.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        url_ = com.google.protobuf.ByteString.EMPTY;
        width_ = 0;
        height_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Image_descriptor;
      }

      @java.lang.Override
      public Adm.Image getDefaultInstanceForType() {
        return Adm.Image.getDefaultInstance();
      }

      @java.lang.Override
      public Adm.Image build() {
        Adm.Image result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public Adm.Image buildPartial() {
        Adm.Image result = new Adm.Image(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(Adm.Image result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.url_ = url_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.width_ = width_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.height_ = height_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Adm.Image) {
          return mergeFrom((Adm.Image)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Adm.Image other) {
        if (other == Adm.Image.getDefaultInstance()) return this;
        if (other.hasUrl()) {
          setUrl(other.getUrl());
        }
        if (other.hasWidth()) {
          setWidth(other.getWidth());
        }
        if (other.hasHeight()) {
          setHeight(other.getHeight());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                url_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                width_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                height_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString url_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return Whether the url field is set.
       */
      @java.lang.Override
      public boolean hasUrl() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return The url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUrl() {
        return url_;
      }
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @param value The url to set.
       * @return This builder for chaining.
       */
      public Builder setUrl(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        url_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUrl() {
        bitField0_ = (bitField0_ & ~0x00000001);
        url_ = getDefaultInstance().getUrl();
        onChanged();
        return this;
      }

      private int width_ ;
      /**
       * <pre>
       * 图片宽
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return Whether the width field is set.
       */
      @java.lang.Override
      public boolean hasWidth() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 图片宽
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return The width.
       */
      @java.lang.Override
      public int getWidth() {
        return width_;
      }
      /**
       * <pre>
       * 图片宽
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @param value The width to set.
       * @return This builder for chaining.
       */
      public Builder setWidth(int value) {

        width_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片宽
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWidth() {
        bitField0_ = (bitField0_ & ~0x00000002);
        width_ = 0;
        onChanged();
        return this;
      }

      private int height_ ;
      /**
       * <pre>
       * 图片高
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return Whether the height field is set.
       */
      @java.lang.Override
      public boolean hasHeight() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 图片高
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return The height.
       */
      @java.lang.Override
      public int getHeight() {
        return height_;
      }
      /**
       * <pre>
       * 图片高
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @param value The height to set.
       * @return This builder for chaining.
       */
      public Builder setHeight(int value) {

        height_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片高
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeight() {
        bitField0_ = (bitField0_ & ~0x00000004);
        height_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:ad.Adm.Image)
    }

    // @@protoc_insertion_point(class_scope:ad.Adm.Image)
    private static final Adm.Image DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Adm.Image();
    }

    public static Adm.Image getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Image>
        PARSER = new com.google.protobuf.AbstractParser<Image>() {
      @java.lang.Override
      public Image parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Image> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Image> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public Adm.Image getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface VideoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ad.Adm.Video)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 视频地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return Whether the url field is set.
     */
    boolean hasUrl();
    /**
     * <pre>
     * 视频地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return The url.
     */
    com.google.protobuf.ByteString getUrl();

    /**
     * <pre>
     * 视频宽度
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return Whether the width field is set.
     */
    boolean hasWidth();
    /**
     * <pre>
     * 视频宽度
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return The width.
     */
    int getWidth();

    /**
     * <pre>
     * 视频高度
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return Whether the height field is set.
     */
    boolean hasHeight();
    /**
     * <pre>
     * 视频高度
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return The height.
     */
    int getHeight();

    /**
     * <pre>
     * 视频时长
     * </pre>
     *
     * <code>optional uint32 duration = 4;</code>
     * @return Whether the duration field is set.
     */
    boolean hasDuration();
    /**
     * <pre>
     * 视频时长
     * </pre>
     *
     * <code>optional uint32 duration = 4;</code>
     * @return The duration.
     */
    int getDuration();

    /**
     * <pre>
     * 视频大小，单位kb
     * </pre>
     *
     * <code>optional uint32 size = 5;</code>
     * @return Whether the size field is set.
     */
    boolean hasSize();
    /**
     * <pre>
     * 视频大小，单位kb
     * </pre>
     *
     * <code>optional uint32 size = 5;</code>
     * @return The size.
     */
    int getSize();
  }
  /**
   * Protobuf type {@code ad.Adm.Video}
   */
  public static final class Video extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:ad.Adm.Video)
      VideoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Video.class.getName());
    }
    // Use Video.newBuilder() to construct.
    private Video(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Video() {
      url_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_Video_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_Video_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Adm.Video.class, Adm.Video.Builder.class);
    }

    private int bitField0_;
    public static final int URL_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString url_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 视频地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return Whether the url field is set.
     */
    @java.lang.Override
    public boolean hasUrl() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 视频地址
     * </pre>
     *
     * <code>optional bytes url = 1;</code>
     * @return The url.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUrl() {
      return url_;
    }

    public static final int WIDTH_FIELD_NUMBER = 2;
    private int width_ = 0;
    /**
     * <pre>
     * 视频宽度
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return Whether the width field is set.
     */
    @java.lang.Override
    public boolean hasWidth() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 视频宽度
     * </pre>
     *
     * <code>optional uint32 width = 2;</code>
     * @return The width.
     */
    @java.lang.Override
    public int getWidth() {
      return width_;
    }

    public static final int HEIGHT_FIELD_NUMBER = 3;
    private int height_ = 0;
    /**
     * <pre>
     * 视频高度
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return Whether the height field is set.
     */
    @java.lang.Override
    public boolean hasHeight() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 视频高度
     * </pre>
     *
     * <code>optional uint32 height = 3;</code>
     * @return The height.
     */
    @java.lang.Override
    public int getHeight() {
      return height_;
    }

    public static final int DURATION_FIELD_NUMBER = 4;
    private int duration_ = 0;
    /**
     * <pre>
     * 视频时长
     * </pre>
     *
     * <code>optional uint32 duration = 4;</code>
     * @return Whether the duration field is set.
     */
    @java.lang.Override
    public boolean hasDuration() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 视频时长
     * </pre>
     *
     * <code>optional uint32 duration = 4;</code>
     * @return The duration.
     */
    @java.lang.Override
    public int getDuration() {
      return duration_;
    }

    public static final int SIZE_FIELD_NUMBER = 5;
    private int size_ = 0;
    /**
     * <pre>
     * 视频大小，单位kb
     * </pre>
     *
     * <code>optional uint32 size = 5;</code>
     * @return Whether the size field is set.
     */
    @java.lang.Override
    public boolean hasSize() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 视频大小，单位kb
     * </pre>
     *
     * <code>optional uint32 size = 5;</code>
     * @return The size.
     */
    @java.lang.Override
    public int getSize() {
      return size_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, url_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, width_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, height_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, duration_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt32(5, size_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, url_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, width_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, height_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, duration_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, size_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Adm.Video)) {
        return super.equals(obj);
      }
      Adm.Video other = (Adm.Video) obj;

      if (hasUrl() != other.hasUrl()) return false;
      if (hasUrl()) {
        if (!getUrl()
            .equals(other.getUrl())) return false;
      }
      if (hasWidth() != other.hasWidth()) return false;
      if (hasWidth()) {
        if (getWidth()
            != other.getWidth()) return false;
      }
      if (hasHeight() != other.hasHeight()) return false;
      if (hasHeight()) {
        if (getHeight()
            != other.getHeight()) return false;
      }
      if (hasDuration() != other.hasDuration()) return false;
      if (hasDuration()) {
        if (getDuration()
            != other.getDuration()) return false;
      }
      if (hasSize() != other.hasSize()) return false;
      if (hasSize()) {
        if (getSize()
            != other.getSize()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUrl()) {
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
      }
      if (hasWidth()) {
        hash = (37 * hash) + WIDTH_FIELD_NUMBER;
        hash = (53 * hash) + getWidth();
      }
      if (hasHeight()) {
        hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getHeight();
      }
      if (hasDuration()) {
        hash = (37 * hash) + DURATION_FIELD_NUMBER;
        hash = (53 * hash) + getDuration();
      }
      if (hasSize()) {
        hash = (37 * hash) + SIZE_FIELD_NUMBER;
        hash = (53 * hash) + getSize();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Adm.Video parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Video parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Video parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Video parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Video parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Adm.Video parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Adm.Video parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static Adm.Video parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static Adm.Video parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static Adm.Video parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Adm.Video parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static Adm.Video parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Adm.Video prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ad.Adm.Video}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ad.Adm.Video)
        Adm.VideoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Video_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Video_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Adm.Video.class, Adm.Video.Builder.class);
      }

      // Construct using cn.taken.ad.logic.adv.baiqingteng.dto.Adm.Video.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        url_ = com.google.protobuf.ByteString.EMPTY;
        width_ = 0;
        height_ = 0;
        duration_ = 0;
        size_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiQingTengPeqRespDto.internal_static_ad_Adm_Video_descriptor;
      }

      @java.lang.Override
      public Adm.Video getDefaultInstanceForType() {
        return Adm.Video.getDefaultInstance();
      }

      @java.lang.Override
      public Adm.Video build() {
        Adm.Video result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public Adm.Video buildPartial() {
        Adm.Video result = new Adm.Video(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(Adm.Video result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.url_ = url_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.width_ = width_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.height_ = height_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.duration_ = duration_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.size_ = size_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Adm.Video) {
          return mergeFrom((Adm.Video)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Adm.Video other) {
        if (other == Adm.Video.getDefaultInstance()) return this;
        if (other.hasUrl()) {
          setUrl(other.getUrl());
        }
        if (other.hasWidth()) {
          setWidth(other.getWidth());
        }
        if (other.hasHeight()) {
          setHeight(other.getHeight());
        }
        if (other.hasDuration()) {
          setDuration(other.getDuration());
        }
        if (other.hasSize()) {
          setSize(other.getSize());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                url_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                width_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                height_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                duration_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                size_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString url_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 视频地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return Whether the url field is set.
       */
      @java.lang.Override
      public boolean hasUrl() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 视频地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return The url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUrl() {
        return url_;
      }
      /**
       * <pre>
       * 视频地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @param value The url to set.
       * @return This builder for chaining.
       */
      public Builder setUrl(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        url_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频地址
       * </pre>
       *
       * <code>optional bytes url = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUrl() {
        bitField0_ = (bitField0_ & ~0x00000001);
        url_ = getDefaultInstance().getUrl();
        onChanged();
        return this;
      }

      private int width_ ;
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return Whether the width field is set.
       */
      @java.lang.Override
      public boolean hasWidth() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return The width.
       */
      @java.lang.Override
      public int getWidth() {
        return width_;
      }
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @param value The width to set.
       * @return This builder for chaining.
       */
      public Builder setWidth(int value) {

        width_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional uint32 width = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWidth() {
        bitField0_ = (bitField0_ & ~0x00000002);
        width_ = 0;
        onChanged();
        return this;
      }

      private int height_ ;
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return Whether the height field is set.
       */
      @java.lang.Override
      public boolean hasHeight() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return The height.
       */
      @java.lang.Override
      public int getHeight() {
        return height_;
      }
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @param value The height to set.
       * @return This builder for chaining.
       */
      public Builder setHeight(int value) {

        height_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional uint32 height = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeight() {
        bitField0_ = (bitField0_ & ~0x00000004);
        height_ = 0;
        onChanged();
        return this;
      }

      private int duration_ ;
      /**
       * <pre>
       * 视频时长
       * </pre>
       *
       * <code>optional uint32 duration = 4;</code>
       * @return Whether the duration field is set.
       */
      @java.lang.Override
      public boolean hasDuration() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 视频时长
       * </pre>
       *
       * <code>optional uint32 duration = 4;</code>
       * @return The duration.
       */
      @java.lang.Override
      public int getDuration() {
        return duration_;
      }
      /**
       * <pre>
       * 视频时长
       * </pre>
       *
       * <code>optional uint32 duration = 4;</code>
       * @param value The duration to set.
       * @return This builder for chaining.
       */
      public Builder setDuration(int value) {

        duration_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频时长
       * </pre>
       *
       * <code>optional uint32 duration = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDuration() {
        bitField0_ = (bitField0_ & ~0x00000008);
        duration_ = 0;
        onChanged();
        return this;
      }

      private int size_ ;
      /**
       * <pre>
       * 视频大小，单位kb
       * </pre>
       *
       * <code>optional uint32 size = 5;</code>
       * @return Whether the size field is set.
       */
      @java.lang.Override
      public boolean hasSize() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 视频大小，单位kb
       * </pre>
       *
       * <code>optional uint32 size = 5;</code>
       * @return The size.
       */
      @java.lang.Override
      public int getSize() {
        return size_;
      }
      /**
       * <pre>
       * 视频大小，单位kb
       * </pre>
       *
       * <code>optional uint32 size = 5;</code>
       * @param value The size to set.
       * @return This builder for chaining.
       */
      public Builder setSize(int value) {

        size_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频大小，单位kb
       * </pre>
       *
       * <code>optional uint32 size = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSize() {
        bitField0_ = (bitField0_ & ~0x00000010);
        size_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:ad.Adm.Video)
    }

    // @@protoc_insertion_point(class_scope:ad.Adm.Video)
    private static final Adm.Video DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Adm.Video();
    }

    public static Adm.Video getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Video>
        PARSER = new com.google.protobuf.AbstractParser<Video>() {
      @java.lang.Override
      public Video parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Video> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Video> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public Adm.Video getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private int bitField0_;
  public static final int TEMPLATEID_FIELD_NUMBER = 1;
  private int templateId_ = 0;
  /**
   * <pre>
   * 广告的模板类型
   * </pre>
   *
   * <code>optional .ad.Template templateId = 1;</code>
   * @return Whether the templateId field is set.
   */
  @java.lang.Override public boolean hasTemplateId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 广告的模板类型
   * </pre>
   *
   * <code>optional .ad.Template templateId = 1;</code>
   * @return The enum numeric value on the wire for templateId.
   */
  @java.lang.Override public int getTemplateIdValue() {
    return templateId_;
  }
  /**
   * <pre>
   * 广告的模板类型
   * </pre>
   *
   * <code>optional .ad.Template templateId = 1;</code>
   * @return The templateId.
   */
  @java.lang.Override public Template getTemplateId() {
    Template result = Template.forNumber(templateId_);
    return result == null ? Template.UNRECOGNIZED : result;
  }

  public static final int ACTIONTYPE_FIELD_NUMBER = 2;
  private int actionType_ = 0;
  /**
   * <pre>
   * 广告的交互类型
   * </pre>
   *
   * <code>optional .ad.ActionType actionType = 2;</code>
   * @return Whether the actionType field is set.
   */
  @java.lang.Override public boolean hasActionType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <pre>
   * 广告的交互类型
   * </pre>
   *
   * <code>optional .ad.ActionType actionType = 2;</code>
   * @return The enum numeric value on the wire for actionType.
   */
  @java.lang.Override public int getActionTypeValue() {
    return actionType_;
  }
  /**
   * <pre>
   * 广告的交互类型
   * </pre>
   *
   * <code>optional .ad.ActionType actionType = 2;</code>
   * @return The actionType.
   */
  @java.lang.Override public ActionType getActionType() {
    ActionType result = ActionType.forNumber(actionType_);
    return result == null ? ActionType.UNRECOGNIZED : result;
  }

  public static final int TITLE_FIELD_NUMBER = 3;
  private com.google.protobuf.ByteString title_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 标题
   * </pre>
   *
   * <code>optional bytes title = 3;</code>
   * @return Whether the title field is set.
   */
  @java.lang.Override
  public boolean hasTitle() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <pre>
   * 标题
   * </pre>
   *
   * <code>optional bytes title = 3;</code>
   * @return The title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getTitle() {
    return title_;
  }

  public static final int DESC_FIELD_NUMBER = 4;
  private com.google.protobuf.ByteString desc_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>optional bytes desc = 4;</code>
   * @return Whether the desc field is set.
   */
  @java.lang.Override
  public boolean hasDesc() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <pre>
   * 描述
   * </pre>
   *
   * <code>optional bytes desc = 4;</code>
   * @return The desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getDesc() {
    return desc_;
  }

  public static final int ICON_FIELD_NUMBER = 5;
  private com.google.protobuf.ByteString icon_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 图标地址
   * </pre>
   *
   * <code>optional bytes icon = 5;</code>
   * @return Whether the icon field is set.
   */
  @java.lang.Override
  public boolean hasIcon() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <pre>
   * 图标地址
   * </pre>
   *
   * <code>optional bytes icon = 5;</code>
   * @return The icon.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getIcon() {
    return icon_;
  }

  public static final int IMG_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<Adm.Image> img_;
  /**
   * <pre>
   * 图片信息
   * </pre>
   *
   * <code>repeated .ad.Adm.Image img = 6;</code>
   */
  @java.lang.Override
  public java.util.List<Adm.Image> getImgList() {
    return img_;
  }
  /**
   * <pre>
   * 图片信息
   * </pre>
   *
   * <code>repeated .ad.Adm.Image img = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends Adm.ImageOrBuilder>
      getImgOrBuilderList() {
    return img_;
  }
  /**
   * <pre>
   * 图片信息
   * </pre>
   *
   * <code>repeated .ad.Adm.Image img = 6;</code>
   */
  @java.lang.Override
  public int getImgCount() {
    return img_.size();
  }
  /**
   * <pre>
   * 图片信息
   * </pre>
   *
   * <code>repeated .ad.Adm.Image img = 6;</code>
   */
  @java.lang.Override
  public Adm.Image getImg(int index) {
    return img_.get(index);
  }
  /**
   * <pre>
   * 图片信息
   * </pre>
   *
   * <code>repeated .ad.Adm.Image img = 6;</code>
   */
  @java.lang.Override
  public Adm.ImageOrBuilder getImgOrBuilder(
      int index) {
    return img_.get(index);
  }

  public static final int VIDEO_FIELD_NUMBER = 7;
  private Adm.Video video_;
  /**
   * <pre>
   * 视频信息
   * </pre>
   *
   * <code>optional .ad.Adm.Video video = 7;</code>
   * @return Whether the video field is set.
   */
  @java.lang.Override
  public boolean hasVideo() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <pre>
   * 视频信息
   * </pre>
   *
   * <code>optional .ad.Adm.Video video = 7;</code>
   * @return The video.
   */
  @java.lang.Override
  public Adm.Video getVideo() {
    return video_ == null ? Adm.Video.getDefaultInstance() : video_;
  }
  /**
   * <pre>
   * 视频信息
   * </pre>
   *
   * <code>optional .ad.Adm.Video video = 7;</code>
   */
  @java.lang.Override
  public Adm.VideoOrBuilder getVideoOrBuilder() {
    return video_ == null ? Adm.Video.getDefaultInstance() : video_;
  }

  public static final int LANDDINGPAGE_FIELD_NUMBER = 8;
  private com.google.protobuf.ByteString landdingPage_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 广告点击落地页/下载地址，deeplink失败是备用地址
   * </pre>
   *
   * <code>optional bytes landdingPage = 8;</code>
   * @return Whether the landdingPage field is set.
   */
  @java.lang.Override
  public boolean hasLanddingPage() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <pre>
   * 广告点击落地页/下载地址，deeplink失败是备用地址
   * </pre>
   *
   * <code>optional bytes landdingPage = 8;</code>
   * @return The landdingPage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getLanddingPage() {
    return landdingPage_;
  }

  public static final int DEEPLINK_FIELD_NUMBER = 9;
  private com.google.protobuf.ByteString deeplink_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * deeplink地址
   * </pre>
   *
   * <code>optional bytes deeplink = 9;</code>
   * @return Whether the deeplink field is set.
   */
  @java.lang.Override
  public boolean hasDeeplink() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <pre>
   * deeplink地址
   * </pre>
   *
   * <code>optional bytes deeplink = 9;</code>
   * @return The deeplink.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getDeeplink() {
    return deeplink_;
  }

  public static final int APPNAME_FIELD_NUMBER = 10;
  private com.google.protobuf.ByteString appName_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 应用名称
   * </pre>
   *
   * <code>optional bytes appName = 10;</code>
   * @return Whether the appName field is set.
   */
  @java.lang.Override
  public boolean hasAppName() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <pre>
   * 应用名称
   * </pre>
   *
   * <code>optional bytes appName = 10;</code>
   * @return The appName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getAppName() {
    return appName_;
  }

  public static final int PACKAGENAME_FIELD_NUMBER = 11;
  private com.google.protobuf.ByteString packageName_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 应用包名
   * </pre>
   *
   * <code>optional bytes packageName = 11;</code>
   * @return Whether the packageName field is set.
   */
  @java.lang.Override
  public boolean hasPackageName() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <pre>
   * 应用包名
   * </pre>
   *
   * <code>optional bytes packageName = 11;</code>
   * @return The packageName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getPackageName() {
    return packageName_;
  }

  public static final int PACKAGESIZE_FIELD_NUMBER = 12;
  private int packageSize_ = 0;
  /**
   * <pre>
   * 应用包大小
   * </pre>
   *
   * <code>optional uint32 packageSize = 12;</code>
   * @return Whether the packageSize field is set.
   */
  @java.lang.Override
  public boolean hasPackageSize() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <pre>
   * 应用包大小
   * </pre>
   *
   * <code>optional uint32 packageSize = 12;</code>
   * @return The packageSize.
   */
  @java.lang.Override
  public int getPackageSize() {
    return packageSize_;
  }

  public static final int BUNDLEID_FIELD_NUMBER = 13;
  private com.google.protobuf.ByteString bundleId_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 用于IOS下载的id
   * </pre>
   *
   * <code>optional bytes bundleId = 13;</code>
   * @return Whether the bundleId field is set.
   */
  @java.lang.Override
  public boolean hasBundleId() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <pre>
   * 用于IOS下载的id
   * </pre>
   *
   * <code>optional bytes bundleId = 13;</code>
   * @return The bundleId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getBundleId() {
    return bundleId_;
  }

  public static final int PUBLISHER_FIELD_NUMBER = 14;
  private com.google.protobuf.ByteString publisher_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 开发者
   * </pre>
   *
   * <code>optional bytes publisher = 14;</code>
   * @return Whether the publisher field is set.
   */
  @java.lang.Override
  public boolean hasPublisher() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <pre>
   * 开发者
   * </pre>
   *
   * <code>optional bytes publisher = 14;</code>
   * @return The publisher.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getPublisher() {
    return publisher_;
  }

  public static final int APPVERSION_FIELD_NUMBER = 15;
  private com.google.protobuf.ByteString appVersion_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 版本号
   * </pre>
   *
   * <code>optional bytes appVersion = 15;</code>
   * @return Whether the appVersion field is set.
   */
  @java.lang.Override
  public boolean hasAppVersion() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <pre>
   * 版本号
   * </pre>
   *
   * <code>optional bytes appVersion = 15;</code>
   * @return The appVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getAppVersion() {
    return appVersion_;
  }

  public static final int PRIVACY_FIELD_NUMBER = 16;
  private com.google.protobuf.ByteString privacy_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 隐私协议
   * </pre>
   *
   * <code>optional bytes privacy = 16;</code>
   * @return Whether the privacy field is set.
   */
  @java.lang.Override
  public boolean hasPrivacy() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <pre>
   * 隐私协议
   * </pre>
   *
   * <code>optional bytes privacy = 16;</code>
   * @return The privacy.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getPrivacy() {
    return privacy_;
  }

  public static final int PERMISSION_FIELD_NUMBER = 17;
  private com.google.protobuf.ByteString permission_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 用户权限
   * </pre>
   *
   * <code>optional bytes permission = 17;</code>
   * @return Whether the permission field is set.
   */
  @java.lang.Override
  public boolean hasPermission() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <pre>
   * 用户权限
   * </pre>
   *
   * <code>optional bytes permission = 17;</code>
   * @return The permission.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getPermission() {
    return permission_;
  }

  public static final int ULK_URL_FIELD_NUMBER = 18;
  private com.google.protobuf.ByteString ulkUrl_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * universal linkurl,用于ios调起
   * </pre>
   *
   * <code>optional bytes ulk_url = 18;</code>
   * @return Whether the ulkUrl field is set.
   */
  @java.lang.Override
  public boolean hasUlkUrl() {
    return ((bitField0_ & 0x00010000) != 0);
  }
  /**
   * <pre>
   * universal linkurl,用于ios调起
   * </pre>
   *
   * <code>optional bytes ulk_url = 18;</code>
   * @return The ulkUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getUlkUrl() {
    return ulkUrl_;
  }

  public static final int ULK_SCHEME_FIELD_NUMBER = 19;
  private com.google.protobuf.ByteString ulkScheme_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * universal linkscheme,用于ios嗅探
   * </pre>
   *
   * <code>optional bytes ulk_scheme = 19;</code>
   * @return Whether the ulkScheme field is set.
   */
  @java.lang.Override
  public boolean hasUlkScheme() {
    return ((bitField0_ & 0x00020000) != 0);
  }
  /**
   * <pre>
   * universal linkscheme,用于ios嗅探
   * </pre>
   *
   * <code>optional bytes ulk_scheme = 19;</code>
   * @return The ulkScheme.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getUlkScheme() {
    return ulkScheme_;
  }

  public static final int APPSTORELINK_FIELD_NUMBER = 20;
  private com.google.protobuf.ByteString appStoreLink_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 安卓直投下载地址
   * </pre>
   *
   * <code>optional bytes appStoreLink = 20;</code>
   * @return Whether the appStoreLink field is set.
   */
  @java.lang.Override
  public boolean hasAppStoreLink() {
    return ((bitField0_ & 0x00040000) != 0);
  }
  /**
   * <pre>
   * 安卓直投下载地址
   * </pre>
   *
   * <code>optional bytes appStoreLink = 20;</code>
   * @return The appStoreLink.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getAppStoreLink() {
    return appStoreLink_;
  }

  public static final int APP_INTRODUCTION_LINK_FIELD_NUMBER = 21;
  private com.google.protobuf.ByteString appIntroductionLink_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 六要素之APP产品功能介绍链接
   * </pre>
   *
   * <code>optional bytes app_introduction_link = 21;</code>
   * @return Whether the appIntroductionLink field is set.
   */
  @java.lang.Override
  public boolean hasAppIntroductionLink() {
    return ((bitField0_ & 0x00080000) != 0);
  }
  /**
   * <pre>
   * 六要素之APP产品功能介绍链接
   * </pre>
   *
   * <code>optional bytes app_introduction_link = 21;</code>
   * @return The appIntroductionLink.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getAppIntroductionLink() {
    return appIntroductionLink_;
  }

  public static final int DOWNLOADMIDPAGE_FIELD_NUMBER = 22;
  private com.google.protobuf.ByteString downloadMidPage_ = com.google.protobuf.ByteString.EMPTY;
  /**
   * <pre>
   * 下载中间页
   * </pre>
   *
   * <code>optional bytes downloadMidPage = 22;</code>
   * @return Whether the downloadMidPage field is set.
   */
  @java.lang.Override
  public boolean hasDownloadMidPage() {
    return ((bitField0_ & 0x00100000) != 0);
  }
  /**
   * <pre>
   * 下载中间页
   * </pre>
   *
   * <code>optional bytes downloadMidPage = 22;</code>
   * @return The downloadMidPage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString getDownloadMidPage() {
    return downloadMidPage_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeEnum(1, templateId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeEnum(2, actionType_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBytes(3, title_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBytes(4, desc_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBytes(5, icon_);
    }
    for (int i = 0; i < img_.size(); i++) {
      output.writeMessage(6, img_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeMessage(7, getVideo());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBytes(8, landdingPage_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeBytes(9, deeplink_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeBytes(10, appName_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeBytes(11, packageName_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeUInt32(12, packageSize_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeBytes(13, bundleId_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeBytes(14, publisher_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeBytes(15, appVersion_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeBytes(16, privacy_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeBytes(17, permission_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      output.writeBytes(18, ulkUrl_);
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      output.writeBytes(19, ulkScheme_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      output.writeBytes(20, appStoreLink_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      output.writeBytes(21, appIntroductionLink_);
    }
    if (((bitField0_ & 0x00100000) != 0)) {
      output.writeBytes(22, downloadMidPage_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, templateId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, actionType_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(3, title_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(4, desc_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(5, icon_);
    }
    for (int i = 0; i < img_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, img_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getVideo());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(8, landdingPage_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(9, deeplink_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(10, appName_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(11, packageName_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(12, packageSize_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(13, bundleId_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(14, publisher_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(15, appVersion_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(16, privacy_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(17, permission_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(18, ulkUrl_);
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(19, ulkScheme_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(20, appStoreLink_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(21, appIntroductionLink_);
    }
    if (((bitField0_ & 0x00100000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(22, downloadMidPage_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof Adm)) {
      return super.equals(obj);
    }
    Adm other = (Adm) obj;

    if (hasTemplateId() != other.hasTemplateId()) return false;
    if (hasTemplateId()) {
      if (templateId_ != other.templateId_) return false;
    }
    if (hasActionType() != other.hasActionType()) return false;
    if (hasActionType()) {
      if (actionType_ != other.actionType_) return false;
    }
    if (hasTitle() != other.hasTitle()) return false;
    if (hasTitle()) {
      if (!getTitle()
          .equals(other.getTitle())) return false;
    }
    if (hasDesc() != other.hasDesc()) return false;
    if (hasDesc()) {
      if (!getDesc()
          .equals(other.getDesc())) return false;
    }
    if (hasIcon() != other.hasIcon()) return false;
    if (hasIcon()) {
      if (!getIcon()
          .equals(other.getIcon())) return false;
    }
    if (!getImgList()
        .equals(other.getImgList())) return false;
    if (hasVideo() != other.hasVideo()) return false;
    if (hasVideo()) {
      if (!getVideo()
          .equals(other.getVideo())) return false;
    }
    if (hasLanddingPage() != other.hasLanddingPage()) return false;
    if (hasLanddingPage()) {
      if (!getLanddingPage()
          .equals(other.getLanddingPage())) return false;
    }
    if (hasDeeplink() != other.hasDeeplink()) return false;
    if (hasDeeplink()) {
      if (!getDeeplink()
          .equals(other.getDeeplink())) return false;
    }
    if (hasAppName() != other.hasAppName()) return false;
    if (hasAppName()) {
      if (!getAppName()
          .equals(other.getAppName())) return false;
    }
    if (hasPackageName() != other.hasPackageName()) return false;
    if (hasPackageName()) {
      if (!getPackageName()
          .equals(other.getPackageName())) return false;
    }
    if (hasPackageSize() != other.hasPackageSize()) return false;
    if (hasPackageSize()) {
      if (getPackageSize()
          != other.getPackageSize()) return false;
    }
    if (hasBundleId() != other.hasBundleId()) return false;
    if (hasBundleId()) {
      if (!getBundleId()
          .equals(other.getBundleId())) return false;
    }
    if (hasPublisher() != other.hasPublisher()) return false;
    if (hasPublisher()) {
      if (!getPublisher()
          .equals(other.getPublisher())) return false;
    }
    if (hasAppVersion() != other.hasAppVersion()) return false;
    if (hasAppVersion()) {
      if (!getAppVersion()
          .equals(other.getAppVersion())) return false;
    }
    if (hasPrivacy() != other.hasPrivacy()) return false;
    if (hasPrivacy()) {
      if (!getPrivacy()
          .equals(other.getPrivacy())) return false;
    }
    if (hasPermission() != other.hasPermission()) return false;
    if (hasPermission()) {
      if (!getPermission()
          .equals(other.getPermission())) return false;
    }
    if (hasUlkUrl() != other.hasUlkUrl()) return false;
    if (hasUlkUrl()) {
      if (!getUlkUrl()
          .equals(other.getUlkUrl())) return false;
    }
    if (hasUlkScheme() != other.hasUlkScheme()) return false;
    if (hasUlkScheme()) {
      if (!getUlkScheme()
          .equals(other.getUlkScheme())) return false;
    }
    if (hasAppStoreLink() != other.hasAppStoreLink()) return false;
    if (hasAppStoreLink()) {
      if (!getAppStoreLink()
          .equals(other.getAppStoreLink())) return false;
    }
    if (hasAppIntroductionLink() != other.hasAppIntroductionLink()) return false;
    if (hasAppIntroductionLink()) {
      if (!getAppIntroductionLink()
          .equals(other.getAppIntroductionLink())) return false;
    }
    if (hasDownloadMidPage() != other.hasDownloadMidPage()) return false;
    if (hasDownloadMidPage()) {
      if (!getDownloadMidPage()
          .equals(other.getDownloadMidPage())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTemplateId()) {
      hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
      hash = (53 * hash) + templateId_;
    }
    if (hasActionType()) {
      hash = (37 * hash) + ACTIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + actionType_;
    }
    if (hasTitle()) {
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
    }
    if (hasDesc()) {
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
    }
    if (hasIcon()) {
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon().hashCode();
    }
    if (getImgCount() > 0) {
      hash = (37 * hash) + IMG_FIELD_NUMBER;
      hash = (53 * hash) + getImgList().hashCode();
    }
    if (hasVideo()) {
      hash = (37 * hash) + VIDEO_FIELD_NUMBER;
      hash = (53 * hash) + getVideo().hashCode();
    }
    if (hasLanddingPage()) {
      hash = (37 * hash) + LANDDINGPAGE_FIELD_NUMBER;
      hash = (53 * hash) + getLanddingPage().hashCode();
    }
    if (hasDeeplink()) {
      hash = (37 * hash) + DEEPLINK_FIELD_NUMBER;
      hash = (53 * hash) + getDeeplink().hashCode();
    }
    if (hasAppName()) {
      hash = (37 * hash) + APPNAME_FIELD_NUMBER;
      hash = (53 * hash) + getAppName().hashCode();
    }
    if (hasPackageName()) {
      hash = (37 * hash) + PACKAGENAME_FIELD_NUMBER;
      hash = (53 * hash) + getPackageName().hashCode();
    }
    if (hasPackageSize()) {
      hash = (37 * hash) + PACKAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPackageSize();
    }
    if (hasBundleId()) {
      hash = (37 * hash) + BUNDLEID_FIELD_NUMBER;
      hash = (53 * hash) + getBundleId().hashCode();
    }
    if (hasPublisher()) {
      hash = (37 * hash) + PUBLISHER_FIELD_NUMBER;
      hash = (53 * hash) + getPublisher().hashCode();
    }
    if (hasAppVersion()) {
      hash = (37 * hash) + APPVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getAppVersion().hashCode();
    }
    if (hasPrivacy()) {
      hash = (37 * hash) + PRIVACY_FIELD_NUMBER;
      hash = (53 * hash) + getPrivacy().hashCode();
    }
    if (hasPermission()) {
      hash = (37 * hash) + PERMISSION_FIELD_NUMBER;
      hash = (53 * hash) + getPermission().hashCode();
    }
    if (hasUlkUrl()) {
      hash = (37 * hash) + ULK_URL_FIELD_NUMBER;
      hash = (53 * hash) + getUlkUrl().hashCode();
    }
    if (hasUlkScheme()) {
      hash = (37 * hash) + ULK_SCHEME_FIELD_NUMBER;
      hash = (53 * hash) + getUlkScheme().hashCode();
    }
    if (hasAppStoreLink()) {
      hash = (37 * hash) + APPSTORELINK_FIELD_NUMBER;
      hash = (53 * hash) + getAppStoreLink().hashCode();
    }
    if (hasAppIntroductionLink()) {
      hash = (37 * hash) + APP_INTRODUCTION_LINK_FIELD_NUMBER;
      hash = (53 * hash) + getAppIntroductionLink().hashCode();
    }
    if (hasDownloadMidPage()) {
      hash = (37 * hash) + DOWNLOADMIDPAGE_FIELD_NUMBER;
      hash = (53 * hash) + getDownloadMidPage().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static Adm parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Adm parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Adm parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Adm parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Adm parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Adm parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Adm parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static Adm parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static Adm parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static Adm parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static Adm parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static Adm parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(Adm prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code ad.Adm}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:ad.Adm)
          AdmOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Adm.class, Adm.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.baiqingteng.dto.Adm.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getImgFieldBuilder();
        getVideoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      templateId_ = 0;
      actionType_ = 0;
      title_ = com.google.protobuf.ByteString.EMPTY;
      desc_ = com.google.protobuf.ByteString.EMPTY;
      icon_ = com.google.protobuf.ByteString.EMPTY;
      if (imgBuilder_ == null) {
        img_ = java.util.Collections.emptyList();
      } else {
        img_ = null;
        imgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      video_ = null;
      if (videoBuilder_ != null) {
        videoBuilder_.dispose();
        videoBuilder_ = null;
      }
      landdingPage_ = com.google.protobuf.ByteString.EMPTY;
      deeplink_ = com.google.protobuf.ByteString.EMPTY;
      appName_ = com.google.protobuf.ByteString.EMPTY;
      packageName_ = com.google.protobuf.ByteString.EMPTY;
      packageSize_ = 0;
      bundleId_ = com.google.protobuf.ByteString.EMPTY;
      publisher_ = com.google.protobuf.ByteString.EMPTY;
      appVersion_ = com.google.protobuf.ByteString.EMPTY;
      privacy_ = com.google.protobuf.ByteString.EMPTY;
      permission_ = com.google.protobuf.ByteString.EMPTY;
      ulkUrl_ = com.google.protobuf.ByteString.EMPTY;
      ulkScheme_ = com.google.protobuf.ByteString.EMPTY;
      appStoreLink_ = com.google.protobuf.ByteString.EMPTY;
      appIntroductionLink_ = com.google.protobuf.ByteString.EMPTY;
      downloadMidPage_ = com.google.protobuf.ByteString.EMPTY;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return BaiQingTengPeqRespDto.internal_static_ad_Adm_descriptor;
    }

    @java.lang.Override
    public Adm getDefaultInstanceForType() {
      return Adm.getDefaultInstance();
    }

    @java.lang.Override
    public Adm build() {
      Adm result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public Adm buildPartial() {
      Adm result = new Adm(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(Adm result) {
      if (imgBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          img_ = java.util.Collections.unmodifiableList(img_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.img_ = img_;
      } else {
        result.img_ = imgBuilder_.build();
      }
    }

    private void buildPartial0(Adm result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.templateId_ = templateId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.actionType_ = actionType_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.title_ = title_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.desc_ = desc_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.icon_ = icon_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.video_ = videoBuilder_ == null
            ? video_
            : videoBuilder_.build();
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.landdingPage_ = landdingPage_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.deeplink_ = deeplink_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.appName_ = appName_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.packageName_ = packageName_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.packageSize_ = packageSize_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.bundleId_ = bundleId_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.publisher_ = publisher_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.appVersion_ = appVersion_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.privacy_ = privacy_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.permission_ = permission_;
        to_bitField0_ |= 0x00008000;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.ulkUrl_ = ulkUrl_;
        to_bitField0_ |= 0x00010000;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.ulkScheme_ = ulkScheme_;
        to_bitField0_ |= 0x00020000;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.appStoreLink_ = appStoreLink_;
        to_bitField0_ |= 0x00040000;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.appIntroductionLink_ = appIntroductionLink_;
        to_bitField0_ |= 0x00080000;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.downloadMidPage_ = downloadMidPage_;
        to_bitField0_ |= 0x00100000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof Adm) {
        return mergeFrom((Adm)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(Adm other) {
      if (other == Adm.getDefaultInstance()) return this;
      if (other.hasTemplateId()) {
        setTemplateId(other.getTemplateId());
      }
      if (other.hasActionType()) {
        setActionType(other.getActionType());
      }
      if (other.hasTitle()) {
        setTitle(other.getTitle());
      }
      if (other.hasDesc()) {
        setDesc(other.getDesc());
      }
      if (other.hasIcon()) {
        setIcon(other.getIcon());
      }
      if (imgBuilder_ == null) {
        if (!other.img_.isEmpty()) {
          if (img_.isEmpty()) {
            img_ = other.img_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureImgIsMutable();
            img_.addAll(other.img_);
          }
          onChanged();
        }
      } else {
        if (!other.img_.isEmpty()) {
          if (imgBuilder_.isEmpty()) {
            imgBuilder_.dispose();
            imgBuilder_ = null;
            img_ = other.img_;
            bitField0_ = (bitField0_ & ~0x00000020);
            imgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getImgFieldBuilder() : null;
          } else {
            imgBuilder_.addAllMessages(other.img_);
          }
        }
      }
      if (other.hasVideo()) {
        mergeVideo(other.getVideo());
      }
      if (other.hasLanddingPage()) {
        setLanddingPage(other.getLanddingPage());
      }
      if (other.hasDeeplink()) {
        setDeeplink(other.getDeeplink());
      }
      if (other.hasAppName()) {
        setAppName(other.getAppName());
      }
      if (other.hasPackageName()) {
        setPackageName(other.getPackageName());
      }
      if (other.hasPackageSize()) {
        setPackageSize(other.getPackageSize());
      }
      if (other.hasBundleId()) {
        setBundleId(other.getBundleId());
      }
      if (other.hasPublisher()) {
        setPublisher(other.getPublisher());
      }
      if (other.hasAppVersion()) {
        setAppVersion(other.getAppVersion());
      }
      if (other.hasPrivacy()) {
        setPrivacy(other.getPrivacy());
      }
      if (other.hasPermission()) {
        setPermission(other.getPermission());
      }
      if (other.hasUlkUrl()) {
        setUlkUrl(other.getUlkUrl());
      }
      if (other.hasUlkScheme()) {
        setUlkScheme(other.getUlkScheme());
      }
      if (other.hasAppStoreLink()) {
        setAppStoreLink(other.getAppStoreLink());
      }
      if (other.hasAppIntroductionLink()) {
        setAppIntroductionLink(other.getAppIntroductionLink());
      }
      if (other.hasDownloadMidPage()) {
        setDownloadMidPage(other.getDownloadMidPage());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              templateId_ = input.readEnum();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              actionType_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              title_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              desc_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              icon_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              Adm.Image m =
                  input.readMessage(
                      Adm.Image.parser(),
                      extensionRegistry);
              if (imgBuilder_ == null) {
                ensureImgIsMutable();
                img_.add(m);
              } else {
                imgBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 58: {
              input.readMessage(
                  getVideoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              landdingPage_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              deeplink_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              appName_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              packageName_ = input.readBytes();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              packageSize_ = input.readUInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 106: {
              bundleId_ = input.readBytes();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 114: {
              publisher_ = input.readBytes();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              appVersion_ = input.readBytes();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 130: {
              privacy_ = input.readBytes();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              permission_ = input.readBytes();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              ulkUrl_ = input.readBytes();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              ulkScheme_ = input.readBytes();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 162: {
              appStoreLink_ = input.readBytes();
              bitField0_ |= 0x00080000;
              break;
            } // case 162
            case 170: {
              appIntroductionLink_ = input.readBytes();
              bitField0_ |= 0x00100000;
              break;
            } // case 170
            case 178: {
              downloadMidPage_ = input.readBytes();
              bitField0_ |= 0x00200000;
              break;
            } // case 178
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int templateId_ = 0;
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @return The enum numeric value on the wire for templateId.
     */
    @java.lang.Override public int getTemplateIdValue() {
      return templateId_;
    }
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @param value The enum numeric value on the wire for templateId to set.
     * @return This builder for chaining.
     */
    public Builder setTemplateIdValue(int value) {
      templateId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public Template getTemplateId() {
      Template result = Template.forNumber(templateId_);
      return result == null ? Template.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @param value The templateId to set.
     * @return This builder for chaining.
     */
    public Builder setTemplateId(Template value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000001;
      templateId_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告的模板类型
     * </pre>
     *
     * <code>optional .ad.Template templateId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTemplateId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      templateId_ = 0;
      onChanged();
      return this;
    }

    private int actionType_ = 0;
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @return Whether the actionType field is set.
     */
    @java.lang.Override public boolean hasActionType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @return The enum numeric value on the wire for actionType.
     */
    @java.lang.Override public int getActionTypeValue() {
      return actionType_;
    }
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @param value The enum numeric value on the wire for actionType to set.
     * @return This builder for chaining.
     */
    public Builder setActionTypeValue(int value) {
      actionType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @return The actionType.
     */
    @java.lang.Override
    public ActionType getActionType() {
      ActionType result = ActionType.forNumber(actionType_);
      return result == null ? ActionType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @param value The actionType to set.
     * @return This builder for chaining.
     */
    public Builder setActionType(ActionType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      actionType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告的交互类型
     * </pre>
     *
     * <code>optional .ad.ActionType actionType = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearActionType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      actionType_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString title_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>optional bytes title = 3;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>optional bytes title = 3;</code>
     * @return The title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTitle() {
      return title_;
    }
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>optional bytes title = 3;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>optional bytes title = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      bitField0_ = (bitField0_ & ~0x00000004);
      title_ = getDefaultInstance().getTitle();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString desc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>optional bytes desc = 4;</code>
     * @return Whether the desc field is set.
     */
    @java.lang.Override
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>optional bytes desc = 4;</code>
     * @return The desc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDesc() {
      return desc_;
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>optional bytes desc = 4;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 描述
     * </pre>
     *
     * <code>optional bytes desc = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      bitField0_ = (bitField0_ & ~0x00000008);
      desc_ = getDefaultInstance().getDesc();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString icon_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 图标地址
     * </pre>
     *
     * <code>optional bytes icon = 5;</code>
     * @return Whether the icon field is set.
     */
    @java.lang.Override
    public boolean hasIcon() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 图标地址
     * </pre>
     *
     * <code>optional bytes icon = 5;</code>
     * @return The icon.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIcon() {
      return icon_;
    }
    /**
     * <pre>
     * 图标地址
     * </pre>
     *
     * <code>optional bytes icon = 5;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 图标地址
     * </pre>
     *
     * <code>optional bytes icon = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      bitField0_ = (bitField0_ & ~0x00000010);
      icon_ = getDefaultInstance().getIcon();
      onChanged();
      return this;
    }

    private java.util.List<Adm.Image> img_ =
      java.util.Collections.emptyList();
    private void ensureImgIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        img_ = new java.util.ArrayList<Adm.Image>(img_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        Adm.Image, Adm.Image.Builder, Adm.ImageOrBuilder> imgBuilder_;

    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public java.util.List<Adm.Image> getImgList() {
      if (imgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(img_);
      } else {
        return imgBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public int getImgCount() {
      if (imgBuilder_ == null) {
        return img_.size();
      } else {
        return imgBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Adm.Image getImg(int index) {
      if (imgBuilder_ == null) {
        return img_.get(index);
      } else {
        return imgBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder setImg(
        int index, Adm.Image value) {
      if (imgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureImgIsMutable();
        img_.set(index, value);
        onChanged();
      } else {
        imgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder setImg(
        int index, Adm.Image.Builder builderForValue) {
      if (imgBuilder_ == null) {
        ensureImgIsMutable();
        img_.set(index, builderForValue.build());
        onChanged();
      } else {
        imgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder addImg(Adm.Image value) {
      if (imgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureImgIsMutable();
        img_.add(value);
        onChanged();
      } else {
        imgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder addImg(
        int index, Adm.Image value) {
      if (imgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureImgIsMutable();
        img_.add(index, value);
        onChanged();
      } else {
        imgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder addImg(
        Adm.Image.Builder builderForValue) {
      if (imgBuilder_ == null) {
        ensureImgIsMutable();
        img_.add(builderForValue.build());
        onChanged();
      } else {
        imgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder addImg(
        int index, Adm.Image.Builder builderForValue) {
      if (imgBuilder_ == null) {
        ensureImgIsMutable();
        img_.add(index, builderForValue.build());
        onChanged();
      } else {
        imgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder addAllImg(
        java.lang.Iterable<? extends Adm.Image> values) {
      if (imgBuilder_ == null) {
        ensureImgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, img_);
        onChanged();
      } else {
        imgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder clearImg() {
      if (imgBuilder_ == null) {
        img_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        imgBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Builder removeImg(int index) {
      if (imgBuilder_ == null) {
        ensureImgIsMutable();
        img_.remove(index);
        onChanged();
      } else {
        imgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Adm.Image.Builder getImgBuilder(
        int index) {
      return getImgFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Adm.ImageOrBuilder getImgOrBuilder(
        int index) {
      if (imgBuilder_ == null) {
        return img_.get(index);  } else {
        return imgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public java.util.List<? extends Adm.ImageOrBuilder>
         getImgOrBuilderList() {
      if (imgBuilder_ != null) {
        return imgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(img_);
      }
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Adm.Image.Builder addImgBuilder() {
      return getImgFieldBuilder().addBuilder(
          Adm.Image.getDefaultInstance());
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public Adm.Image.Builder addImgBuilder(
        int index) {
      return getImgFieldBuilder().addBuilder(
          index, Adm.Image.getDefaultInstance());
    }
    /**
     * <pre>
     * 图片信息
     * </pre>
     *
     * <code>repeated .ad.Adm.Image img = 6;</code>
     */
    public java.util.List<Adm.Image.Builder>
         getImgBuilderList() {
      return getImgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        Adm.Image, Adm.Image.Builder, Adm.ImageOrBuilder>
        getImgFieldBuilder() {
      if (imgBuilder_ == null) {
        imgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            Adm.Image, Adm.Image.Builder, Adm.ImageOrBuilder>(
                img_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        img_ = null;
      }
      return imgBuilder_;
    }

    private Adm.Video video_;
    private com.google.protobuf.SingleFieldBuilder<
        Adm.Video, Adm.Video.Builder, Adm.VideoOrBuilder> videoBuilder_;
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     * @return Whether the video field is set.
     */
    public boolean hasVideo() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     * @return The video.
     */
    public Adm.Video getVideo() {
      if (videoBuilder_ == null) {
        return video_ == null ? Adm.Video.getDefaultInstance() : video_;
      } else {
        return videoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Builder setVideo(Adm.Video value) {
      if (videoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        video_ = value;
      } else {
        videoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Builder setVideo(
        Adm.Video.Builder builderForValue) {
      if (videoBuilder_ == null) {
        video_ = builderForValue.build();
      } else {
        videoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Builder mergeVideo(Adm.Video value) {
      if (videoBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0) &&
          video_ != null &&
          video_ != Adm.Video.getDefaultInstance()) {
          getVideoBuilder().mergeFrom(value);
        } else {
          video_ = value;
        }
      } else {
        videoBuilder_.mergeFrom(value);
      }
      if (video_ != null) {
        bitField0_ |= 0x00000040;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Builder clearVideo() {
      bitField0_ = (bitField0_ & ~0x00000040);
      video_ = null;
      if (videoBuilder_ != null) {
        videoBuilder_.dispose();
        videoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Adm.Video.Builder getVideoBuilder() {
      bitField0_ |= 0x00000040;
      onChanged();
      return getVideoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    public Adm.VideoOrBuilder getVideoOrBuilder() {
      if (videoBuilder_ != null) {
        return videoBuilder_.getMessageOrBuilder();
      } else {
        return video_ == null ?
            Adm.Video.getDefaultInstance() : video_;
      }
    }
    /**
     * <pre>
     * 视频信息
     * </pre>
     *
     * <code>optional .ad.Adm.Video video = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        Adm.Video, Adm.Video.Builder, Adm.VideoOrBuilder>
        getVideoFieldBuilder() {
      if (videoBuilder_ == null) {
        videoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            Adm.Video, Adm.Video.Builder, Adm.VideoOrBuilder>(
                getVideo(),
                getParentForChildren(),
                isClean());
        video_ = null;
      }
      return videoBuilder_;
    }

    private com.google.protobuf.ByteString landdingPage_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 广告点击落地页/下载地址，deeplink失败是备用地址
     * </pre>
     *
     * <code>optional bytes landdingPage = 8;</code>
     * @return Whether the landdingPage field is set.
     */
    @java.lang.Override
    public boolean hasLanddingPage() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 广告点击落地页/下载地址，deeplink失败是备用地址
     * </pre>
     *
     * <code>optional bytes landdingPage = 8;</code>
     * @return The landdingPage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLanddingPage() {
      return landdingPage_;
    }
    /**
     * <pre>
     * 广告点击落地页/下载地址，deeplink失败是备用地址
     * </pre>
     *
     * <code>optional bytes landdingPage = 8;</code>
     * @param value The landdingPage to set.
     * @return This builder for chaining.
     */
    public Builder setLanddingPage(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      landdingPage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告点击落地页/下载地址，deeplink失败是备用地址
     * </pre>
     *
     * <code>optional bytes landdingPage = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLanddingPage() {
      bitField0_ = (bitField0_ & ~0x00000080);
      landdingPage_ = getDefaultInstance().getLanddingPage();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString deeplink_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * deeplink地址
     * </pre>
     *
     * <code>optional bytes deeplink = 9;</code>
     * @return Whether the deeplink field is set.
     */
    @java.lang.Override
    public boolean hasDeeplink() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * deeplink地址
     * </pre>
     *
     * <code>optional bytes deeplink = 9;</code>
     * @return The deeplink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDeeplink() {
      return deeplink_;
    }
    /**
     * <pre>
     * deeplink地址
     * </pre>
     *
     * <code>optional bytes deeplink = 9;</code>
     * @param value The deeplink to set.
     * @return This builder for chaining.
     */
    public Builder setDeeplink(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      deeplink_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * deeplink地址
     * </pre>
     *
     * <code>optional bytes deeplink = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeeplink() {
      bitField0_ = (bitField0_ & ~0x00000100);
      deeplink_ = getDefaultInstance().getDeeplink();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString appName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 应用名称
     * </pre>
     *
     * <code>optional bytes appName = 10;</code>
     * @return Whether the appName field is set.
     */
    @java.lang.Override
    public boolean hasAppName() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 应用名称
     * </pre>
     *
     * <code>optional bytes appName = 10;</code>
     * @return The appName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAppName() {
      return appName_;
    }
    /**
     * <pre>
     * 应用名称
     * </pre>
     *
     * <code>optional bytes appName = 10;</code>
     * @param value The appName to set.
     * @return This builder for chaining.
     */
    public Builder setAppName(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appName_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 应用名称
     * </pre>
     *
     * <code>optional bytes appName = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppName() {
      bitField0_ = (bitField0_ & ~0x00000200);
      appName_ = getDefaultInstance().getAppName();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString packageName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 应用包名
     * </pre>
     *
     * <code>optional bytes packageName = 11;</code>
     * @return Whether the packageName field is set.
     */
    @java.lang.Override
    public boolean hasPackageName() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 应用包名
     * </pre>
     *
     * <code>optional bytes packageName = 11;</code>
     * @return The packageName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPackageName() {
      return packageName_;
    }
    /**
     * <pre>
     * 应用包名
     * </pre>
     *
     * <code>optional bytes packageName = 11;</code>
     * @param value The packageName to set.
     * @return This builder for chaining.
     */
    public Builder setPackageName(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      packageName_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 应用包名
     * </pre>
     *
     * <code>optional bytes packageName = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPackageName() {
      bitField0_ = (bitField0_ & ~0x00000400);
      packageName_ = getDefaultInstance().getPackageName();
      onChanged();
      return this;
    }

    private int packageSize_ ;
    /**
     * <pre>
     * 应用包大小
     * </pre>
     *
     * <code>optional uint32 packageSize = 12;</code>
     * @return Whether the packageSize field is set.
     */
    @java.lang.Override
    public boolean hasPackageSize() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 应用包大小
     * </pre>
     *
     * <code>optional uint32 packageSize = 12;</code>
     * @return The packageSize.
     */
    @java.lang.Override
    public int getPackageSize() {
      return packageSize_;
    }
    /**
     * <pre>
     * 应用包大小
     * </pre>
     *
     * <code>optional uint32 packageSize = 12;</code>
     * @param value The packageSize to set.
     * @return This builder for chaining.
     */
    public Builder setPackageSize(int value) {

      packageSize_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 应用包大小
     * </pre>
     *
     * <code>optional uint32 packageSize = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearPackageSize() {
      bitField0_ = (bitField0_ & ~0x00000800);
      packageSize_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString bundleId_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 用于IOS下载的id
     * </pre>
     *
     * <code>optional bytes bundleId = 13;</code>
     * @return Whether the bundleId field is set.
     */
    @java.lang.Override
    public boolean hasBundleId() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 用于IOS下载的id
     * </pre>
     *
     * <code>optional bytes bundleId = 13;</code>
     * @return The bundleId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBundleId() {
      return bundleId_;
    }
    /**
     * <pre>
     * 用于IOS下载的id
     * </pre>
     *
     * <code>optional bytes bundleId = 13;</code>
     * @param value The bundleId to set.
     * @return This builder for chaining.
     */
    public Builder setBundleId(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      bundleId_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 用于IOS下载的id
     * </pre>
     *
     * <code>optional bytes bundleId = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearBundleId() {
      bitField0_ = (bitField0_ & ~0x00001000);
      bundleId_ = getDefaultInstance().getBundleId();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString publisher_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 开发者
     * </pre>
     *
     * <code>optional bytes publisher = 14;</code>
     * @return Whether the publisher field is set.
     */
    @java.lang.Override
    public boolean hasPublisher() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 开发者
     * </pre>
     *
     * <code>optional bytes publisher = 14;</code>
     * @return The publisher.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPublisher() {
      return publisher_;
    }
    /**
     * <pre>
     * 开发者
     * </pre>
     *
     * <code>optional bytes publisher = 14;</code>
     * @param value The publisher to set.
     * @return This builder for chaining.
     */
    public Builder setPublisher(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      publisher_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开发者
     * </pre>
     *
     * <code>optional bytes publisher = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearPublisher() {
      bitField0_ = (bitField0_ & ~0x00002000);
      publisher_ = getDefaultInstance().getPublisher();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString appVersion_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>optional bytes appVersion = 15;</code>
     * @return Whether the appVersion field is set.
     */
    @java.lang.Override
    public boolean hasAppVersion() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>optional bytes appVersion = 15;</code>
     * @return The appVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAppVersion() {
      return appVersion_;
    }
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>optional bytes appVersion = 15;</code>
     * @param value The appVersion to set.
     * @return This builder for chaining.
     */
    public Builder setAppVersion(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appVersion_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>optional bytes appVersion = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppVersion() {
      bitField0_ = (bitField0_ & ~0x00004000);
      appVersion_ = getDefaultInstance().getAppVersion();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString privacy_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 隐私协议
     * </pre>
     *
     * <code>optional bytes privacy = 16;</code>
     * @return Whether the privacy field is set.
     */
    @java.lang.Override
    public boolean hasPrivacy() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 隐私协议
     * </pre>
     *
     * <code>optional bytes privacy = 16;</code>
     * @return The privacy.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPrivacy() {
      return privacy_;
    }
    /**
     * <pre>
     * 隐私协议
     * </pre>
     *
     * <code>optional bytes privacy = 16;</code>
     * @param value The privacy to set.
     * @return This builder for chaining.
     */
    public Builder setPrivacy(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      privacy_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 隐私协议
     * </pre>
     *
     * <code>optional bytes privacy = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrivacy() {
      bitField0_ = (bitField0_ & ~0x00008000);
      privacy_ = getDefaultInstance().getPrivacy();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString permission_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 用户权限
     * </pre>
     *
     * <code>optional bytes permission = 17;</code>
     * @return Whether the permission field is set.
     */
    @java.lang.Override
    public boolean hasPermission() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 用户权限
     * </pre>
     *
     * <code>optional bytes permission = 17;</code>
     * @return The permission.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPermission() {
      return permission_;
    }
    /**
     * <pre>
     * 用户权限
     * </pre>
     *
     * <code>optional bytes permission = 17;</code>
     * @param value The permission to set.
     * @return This builder for chaining.
     */
    public Builder setPermission(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      permission_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 用户权限
     * </pre>
     *
     * <code>optional bytes permission = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearPermission() {
      bitField0_ = (bitField0_ & ~0x00010000);
      permission_ = getDefaultInstance().getPermission();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString ulkUrl_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * universal linkurl,用于ios调起
     * </pre>
     *
     * <code>optional bytes ulk_url = 18;</code>
     * @return Whether the ulkUrl field is set.
     */
    @java.lang.Override
    public boolean hasUlkUrl() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * universal linkurl,用于ios调起
     * </pre>
     *
     * <code>optional bytes ulk_url = 18;</code>
     * @return The ulkUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUlkUrl() {
      return ulkUrl_;
    }
    /**
     * <pre>
     * universal linkurl,用于ios调起
     * </pre>
     *
     * <code>optional bytes ulk_url = 18;</code>
     * @param value The ulkUrl to set.
     * @return This builder for chaining.
     */
    public Builder setUlkUrl(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      ulkUrl_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * universal linkurl,用于ios调起
     * </pre>
     *
     * <code>optional bytes ulk_url = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearUlkUrl() {
      bitField0_ = (bitField0_ & ~0x00020000);
      ulkUrl_ = getDefaultInstance().getUlkUrl();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString ulkScheme_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * universal linkscheme,用于ios嗅探
     * </pre>
     *
     * <code>optional bytes ulk_scheme = 19;</code>
     * @return Whether the ulkScheme field is set.
     */
    @java.lang.Override
    public boolean hasUlkScheme() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * universal linkscheme,用于ios嗅探
     * </pre>
     *
     * <code>optional bytes ulk_scheme = 19;</code>
     * @return The ulkScheme.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUlkScheme() {
      return ulkScheme_;
    }
    /**
     * <pre>
     * universal linkscheme,用于ios嗅探
     * </pre>
     *
     * <code>optional bytes ulk_scheme = 19;</code>
     * @param value The ulkScheme to set.
     * @return This builder for chaining.
     */
    public Builder setUlkScheme(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      ulkScheme_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * universal linkscheme,用于ios嗅探
     * </pre>
     *
     * <code>optional bytes ulk_scheme = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearUlkScheme() {
      bitField0_ = (bitField0_ & ~0x00040000);
      ulkScheme_ = getDefaultInstance().getUlkScheme();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString appStoreLink_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 安卓直投下载地址
     * </pre>
     *
     * <code>optional bytes appStoreLink = 20;</code>
     * @return Whether the appStoreLink field is set.
     */
    @java.lang.Override
    public boolean hasAppStoreLink() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 安卓直投下载地址
     * </pre>
     *
     * <code>optional bytes appStoreLink = 20;</code>
     * @return The appStoreLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAppStoreLink() {
      return appStoreLink_;
    }
    /**
     * <pre>
     * 安卓直投下载地址
     * </pre>
     *
     * <code>optional bytes appStoreLink = 20;</code>
     * @param value The appStoreLink to set.
     * @return This builder for chaining.
     */
    public Builder setAppStoreLink(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appStoreLink_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安卓直投下载地址
     * </pre>
     *
     * <code>optional bytes appStoreLink = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppStoreLink() {
      bitField0_ = (bitField0_ & ~0x00080000);
      appStoreLink_ = getDefaultInstance().getAppStoreLink();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString appIntroductionLink_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 六要素之APP产品功能介绍链接
     * </pre>
     *
     * <code>optional bytes app_introduction_link = 21;</code>
     * @return Whether the appIntroductionLink field is set.
     */
    @java.lang.Override
    public boolean hasAppIntroductionLink() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 六要素之APP产品功能介绍链接
     * </pre>
     *
     * <code>optional bytes app_introduction_link = 21;</code>
     * @return The appIntroductionLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAppIntroductionLink() {
      return appIntroductionLink_;
    }
    /**
     * <pre>
     * 六要素之APP产品功能介绍链接
     * </pre>
     *
     * <code>optional bytes app_introduction_link = 21;</code>
     * @param value The appIntroductionLink to set.
     * @return This builder for chaining.
     */
    public Builder setAppIntroductionLink(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appIntroductionLink_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 六要素之APP产品功能介绍链接
     * </pre>
     *
     * <code>optional bytes app_introduction_link = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppIntroductionLink() {
      bitField0_ = (bitField0_ & ~0x00100000);
      appIntroductionLink_ = getDefaultInstance().getAppIntroductionLink();
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString downloadMidPage_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 下载中间页
     * </pre>
     *
     * <code>optional bytes downloadMidPage = 22;</code>
     * @return Whether the downloadMidPage field is set.
     */
    @java.lang.Override
    public boolean hasDownloadMidPage() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 下载中间页
     * </pre>
     *
     * <code>optional bytes downloadMidPage = 22;</code>
     * @return The downloadMidPage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDownloadMidPage() {
      return downloadMidPage_;
    }
    /**
     * <pre>
     * 下载中间页
     * </pre>
     *
     * <code>optional bytes downloadMidPage = 22;</code>
     * @param value The downloadMidPage to set.
     * @return This builder for chaining.
     */
    public Builder setDownloadMidPage(com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      downloadMidPage_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 下载中间页
     * </pre>
     *
     * <code>optional bytes downloadMidPage = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearDownloadMidPage() {
      bitField0_ = (bitField0_ & ~0x00200000);
      downloadMidPage_ = getDefaultInstance().getDownloadMidPage();
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:ad.Adm)
  }

  // @@protoc_insertion_point(class_scope:ad.Adm)
  private static final Adm DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new Adm();
  }

  public static Adm getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Adm>
      PARSER = new com.google.protobuf.AbstractParser<Adm>() {
    @java.lang.Override
    public Adm parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Adm> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Adm> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public Adm getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

