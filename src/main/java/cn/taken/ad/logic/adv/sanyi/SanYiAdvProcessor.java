package cn.taken.ad.logic.adv.sanyi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;

import cn.taken.ad.logic.adv.sanyi.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.*;


@Component("SANYI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class SanYiAdvProcessor implements AdvProcessor {
    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {

        RequestDto requestDto=new RequestDto();
        requestDto.setApp(buildRequestApp(rtbDto,advDto));
        requestDto.setDevice(buildRequestDevice(rtbDto,advDto));
        requestDto.setAdslot(buildRequestAdslot(rtbDto,advDto));

        advDto.setReqObj(requestDto);
        String json = JsonHelper.toJsonStringWithoutNull(requestDto);
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json,"Utf-8", new Header[]{new BasicHeader("Content-Type", "application/json")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        advDto.setRespObj(resp);
        return parseResponse(resp,rtbDto);
    }

    private Map<String,String> buildRequestApp(RtbRequestDto rtbDto, RtbAdvDto advDto){
        Map<String,String> app=new HashMap<>();

        RequestAppDto appDto= rtbDto.getApp();
        if(StringUtils.isNotEmpty(advDto.getAppCode())) {
            app.put("appid",advDto.getAppCode());
        }
        if(StringUtils.isNotEmpty(appDto.getAppName())) {
            app.put("name",appDto.getAppName());
        }
        if(StringUtils.isNotEmpty(appDto.getBundle())) {
            app.put("package",appDto.getBundle());
        }
        if(StringUtils.isNotEmpty(appDto.getAppVersion())) {
            app.put("version",appDto.getAppVersion());
        }
        return app;
    }
    private RequestDevice buildRequestDevice(RtbRequestDto rtbDto, RtbAdvDto advDto){
        RequestDevice device=new RequestDevice();
        RequestDeviceDto deviceDto=rtbDto.getDevice();
        RequestNetworkDto networkDto=rtbDto.getNetwork();
        RequestGeoDto geoDto=rtbDto.getGeo();
        if(null!=deviceDto.getDeviceType()){
            if(deviceDto.getDeviceType()== DeviceType.PHONE){
                device.setDtype(1);
            }else if(deviceDto.getDeviceType()==DeviceType.PAD){
                device.setDtype(2);
            }else{
                device.setDtype(3);
            }
        }
        if(null!=deviceDto.getOsType()){
            if(deviceDto.getOsType()== OsType.ANDROID){
                device.setOs(1);
            }else{
                device.setOs(2);
            }
        }
        if(StringUtils.isNotEmpty(deviceDto.getOsVersion())){
            device.setOsv(deviceDto.getOsVersion());
        }

        if(null!=deviceDto.getHeight()){
            device.setH(deviceDto.getHeight());
        }
        if(null!=deviceDto.getWidth()){
            device.setW(deviceDto.getWidth());
        }

        if(StringUtils.isNotEmpty(deviceDto.getIdfa())){
            device.setIdfa(deviceDto.getIdfa());
        }
        if(StringUtils.isNotEmpty(deviceDto.getIdfaMd5())){
            device.setIdfa_md5(deviceDto.getIdfaMd5());
        }

        if(StringUtils.isNotEmpty(deviceDto.getIdfv())){
            device.setIdfv(deviceDto.getIdfv());
        }
        if(StringUtils.isNotEmpty(deviceDto.getImei())){
            device.setImei(deviceDto.getImei());
        }
        if(StringUtils.isNotEmpty(deviceDto.getImeiMd5())){
            device.setImei_md5(deviceDto.getImeiMd5());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAndroidId())){
            device.setAndroidid(deviceDto.getAndroidId());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())){
            device.setAndroidid_md5(deviceDto.getAndroidIdMd5());
        }
        if(StringUtils.isNotEmpty(deviceDto.getUserAgent())){
            device.setUa(deviceDto.getUserAgent());
        }
        if(StringUtils.isNotEmpty(networkDto.getMac())){
            device.setMac(networkDto.getMac().toLowerCase());
        }
        if(StringUtils.isNotEmpty(networkDto.getMacMd5())){
            device.setMac_md5(networkDto.getMacMd5());
        }
        if(StringUtils.isNotEmpty(deviceDto.getVendor())){
            device.setVendor(deviceDto.getVendor());
        }

        if(StringUtils.isNotEmpty(deviceDto.getBrand())){
            device.setBrand(deviceDto.getBrand());
        }
        if(StringUtils.isNotEmpty(deviceDto.getModel())){
            device.setModel(deviceDto.getModel());
        }
        if(StringUtils.isNotEmpty(deviceDto.getImsi())){
            device.setImsi(deviceDto.getImsi());
        }
        if(StringUtils.isNotEmpty(networkDto.getIp())){
            device.setIp(networkDto.getIp());
        }

        if(null!=networkDto.getConnectType()){
             if(networkDto.getConnectType()==ConnectionType.WIFI){
                device.setNetwork(1);
            }else if(networkDto.getConnectType()==ConnectionType.NETWORK_2G){
                device.setNetwork(2);
            }else if(networkDto.getConnectType()==ConnectionType.NETWORK_3G){
                device.setNetwork(3);
            }else if(networkDto.getConnectType()==ConnectionType.NETWORK_4G){
                device.setNetwork(4);
            }else if(networkDto.getConnectType()==ConnectionType.NETWORK_5G){
                device.setNetwork(5);
            }else{
                device.setNetwork(0);
            }
        }

        if(null!=networkDto.getCarrierType()){
            if(networkDto.getCarrierType()== CarrierType.CM){
                device.setCarrier(1);
            }else if(networkDto.getCarrierType()== CarrierType.CU){
                device.setCarrier(2);
            }else if(networkDto.getCarrierType()== CarrierType.CT){
                device.setCarrier(3);
            }else{
                device.setCarrier(0);
            }
        }
        if(null!=deviceDto.getApiLevel()){
            device.setAv(deviceDto.getApiLevel());
        }
        if(StringUtils.isNotEmpty(deviceDto.getSerialNO())){
            device.setSn(deviceDto.getSerialNO());
        }
        if(StringUtils.isNotEmpty(deviceDto.getOaid())){
            device.setOaid(deviceDto.getOaid());
        }
        if(StringUtils.isNotEmpty(deviceDto.getOaidMd5())){
            device.setOaid_md5(deviceDto.getOaidMd5());
        }
        if(StringUtils.isNotEmpty(networkDto.getSsid())){
            device.setSsid(networkDto.getSsid());
        }

        if(null!=deviceDto.getScreenDensity()){
            device.setDensity(deviceDto.getScreenDensity().floatValue());
        }
        if(null!=deviceDto.getPpi()){
            device.setPpi(deviceDto.getPpi());
        }

        if(StringUtils.isNotEmpty(deviceDto.getBootMark())){
            device.setBootmark(deviceDto.getBootMark());
        }
        if(StringUtils.isNotEmpty(deviceDto.getUpdateMark())){
            device.setUpdatemark(deviceDto.getUpdateMark());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAaid())){
            device.setAaid(deviceDto.getAaid());
        }
        if(null!=deviceDto.getCaids()&&!deviceDto.getCaids().isEmpty()){
            RequestCaidDto caidDto=deviceDto.getCaids().get(0);
            if(StringUtils.isNotEmpty(caidDto.getCaid())){
                device.setCaid(caidDto.getCaid());
            }
            if(StringUtils.isNotEmpty(caidDto.getVersion())){
                device.setCaidv(caidDto.getVersion());
            }
        }
        if(null!=geoDto.getLongitude()){
            device.setLon(geoDto.getLongitude().floatValue());
        }
        if(null!=geoDto.getLatitude()){
            device.setLat(geoDto.getLatitude().floatValue());
        }
        if(null!=geoDto.getTimestamp()){
            device.setTs(geoDto.getTimestamp().intValue());
        }
        if(StringUtils.isNotEmpty(deviceDto.getDeviceName())){
            device.setDname(deviceDto.getDeviceName());
        }
        if(null!=deviceDto.getDeviceMemory()){
            device.setMem(String.valueOf(deviceDto.getDeviceMemory()/1024));
        }
        if(null!=deviceDto.getDeviceHardDisk()){
            device.setDisk(String.valueOf(deviceDto.getDeviceHardDisk()/1024));
        }

        if(StringUtils.isNotEmpty(deviceDto.getHmsVersion())){
            device.setHmsv(deviceDto.getHmsVersion());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAppStoreVersion())){
            device.setAppstorev(deviceDto.getAppStoreVersion());
        }
        if(StringUtils.isNotEmpty(deviceDto.getOpenUdId())){
            device.setUdid(deviceDto.getOpenUdId());
        }
        if(StringUtils.isNotEmpty(deviceDto.getLanguage())){
            device.setLanguage(deviceDto.getLanguage());
        }
        if(StringUtils.isNotEmpty(deviceDto.getRomVersion())){
            device.setRomv(deviceDto.getRomVersion());
        }
        if(StringUtils.isNotEmpty(deviceDto.getSysCompileTime())){
            device.setCompilingtime(TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime())/1000);
        }
        if(StringUtils.isNotEmpty(deviceDto.getSysUpdateTime())){
            device.setUptime(TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime())/1000);
        }
        if(StringUtils.isNotEmpty(deviceDto.getSysStartTime())){
            device.setBootime(TimeUtils.convertMilliSecond(deviceDto.getSysStartTime())/1000);
        }
        if(StringUtils.isNotEmpty(deviceDto.getHardwareModel())){
            device.setHwmodel(deviceDto.getHardwareModel());
        }
        if(StringUtils.isNotEmpty(deviceDto.getHardwareMachine())){
            device.setHwmachine(deviceDto.getHardwareMachine());
        }
        if(null!=deviceDto.getCpuNum()){
            device.setCpu(deviceDto.getCpuNum());
        }
        if(null!=deviceDto.getCpuFreq()){
            device.setCpufreq(deviceDto.getCpuFreq().floatValue());
        }
        if(null!=deviceDto.getBatteryStatus()){
            if(deviceDto.getBatteryStatus()==1){
                device.setBattery(0);
            }else if(deviceDto.getBatteryStatus()==2){
                device.setBattery(1);
            }else if(deviceDto.getBatteryStatus()==3){
                device.setBattery(2);
            }else{
                device.setBattery(3);
            }
        }
        if(null!=deviceDto.getBatteryPower()){
            device.setBatterystatus(deviceDto.getBatteryPower());
        }
        if(null!=deviceDto.getOrientation()){
            if(deviceDto.getOrientation()== OrientationType.HORIZONTAL){
                device.setOrien(2);
            }else {
                device.setOrien(1);
            }
        }
        if(StringUtils.isNotEmpty(deviceDto.getTimeZone())){
            device.setTimezone(deviceDto.getTimeZone());
        }
        if(null!=deviceDto.getIdfaPolicy()){
            device.setIdfapolicy(deviceDto.getIdfaPolicy());
        }
        if(StringUtils.isNotEmpty(deviceDto.getModelCode())){
            device.setModelcode(deviceDto.getModelCode());
        }
        if(StringUtils.isNotEmpty(deviceDto.getVaid())){
            device.setVaid(deviceDto.getVaid());
        }
        if(StringUtils.isNotEmpty(deviceDto.getSysUiVersion())){
            device.setMiuiVersion(deviceDto.getSysUiVersion());
        }
        return device;
    }
    private RequestAd buildRequestAdslot(RtbRequestDto rtbDto, RtbAdvDto advDto){
        RequestTagDto tagDto=rtbDto.getTag();
        RequestAd ad=new RequestAd();

        if(StringUtils.isNotEmpty(advDto.getTagCode())){
            ad.setAdid(advDto.getTagCode());
        }
        if(null!=tagDto.getWidth()){
            ad.setW(tagDto.getWidth());
        }
        if(null!=tagDto.getHeight()){
            ad.setH(tagDto.getHeight());
        }

        if(null!=tagDto.getPrice()){
            ad.setTbf(tagDto.getPrice().intValue());
        }
        return ad;
    }

    private RtbResponseDto parseResponse(String resp,RtbRequestDto rtbDto) throws Exception {
        ResponseDto rspDto = JsonHelper.fromJson(ResponseDto.class, resp);
        if (rspDto.getCode() != 0) {
            if (404 == rspDto.getCode()) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            }
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), rspDto.getCode() + "");
        }

        if (null == rspDto.getAds() || rspDto.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        // 广告信息
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "");
        List<ResponseAd> ad = rspDto.getAds();
        ad.forEach(item -> {
            if(null!=item.getMetas()) {
                for (ResponseMeta meta : item.getMetas()) {
                    TagResponseDto dto = new TagResponseDto();

                        if (null != meta.getAd_type()) {
                            if (meta.getAd_type() == 5 || meta.getAd_type() == 6) {
                                dto.setMaterialType(MaterialType.VIDEO);
                            } else {
                                dto.setMaterialType(MaterialType.IMAGE_TEXT);
                            }
                        }
                        if (null != meta.getInteraction()) {
                            if (meta.getInteraction() == 0) {
                                dto.setActionType(ActionType.WEB_VIEW_H5);
                            } else if (meta.getInteraction() == 1) {
                                dto.setActionType(ActionType.SYSTEM_BROWSER_H5);
                            } else {
                                dto.setActionType(ActionType.DOWNLOAD);
                            }
                        }
                        if (StringUtils.isNotEmpty(meta.getDeeplink())) {
                            dto.setDeepLinkUrl(meta.getDeeplink());
                            dto.setActionType(ActionType.DEEPLINK);
                        }
                        if (StringUtils.isNotEmpty(meta.getUniversal())) {
                            dto.setUniversalLink(meta.getUniversal());
                            dto.setActionType(ActionType.DEEPLINK);
                        }

                        if (StringUtils.isNotEmpty(meta.getClkurl())) {
                            dto.setClickUrl(meta.getClkurl());
                        }
                        if (StringUtils.isNotEmpty(meta.getTitle())) {
                            dto.setTitle(meta.getTitle());
                        }
                        if (StringUtils.isNotEmpty(meta.getDescription())) {
                            dto.setDesc(meta.getDescription());
                        }

                        if (null != meta.getImages() && !meta.getImages().isEmpty()) {
                            dto.setImgUrls(meta.getImages());
                        }

                        if (null != meta.getSize()) {
                            ResponseSize size = meta.getSize();
                            if (null != size.getWidth()) {
                                dto.setMaterialWidth(size.getWidth());
                            }
                            if (null != size.getHeight()) {
                                dto.setMaterialHeight(size.getHeight());
                            }
                        }

                        if (StringUtils.isNotEmpty(meta.getIcon())) {
                            dto.setIconUrl(meta.getIcon());
                        }

                        ResponseAppDto appdto = new ResponseAppDto();
                        ResponseAppInfo app = meta.getApp();
                        if (null != app) {
                            if (StringUtils.isNotEmpty(app.getName())) {
                                appdto.setAppName(app.getName());
                            }
                            if (StringUtils.isNotEmpty(app.getPackage_name())) {
                                appdto.setPackageName(app.getPackage_name());
                            }
                            if (null != app.getSize()) {
                                appdto.setAppSize(app.getSize().longValue());
                            }
                            if (StringUtils.isNotEmpty(app.getVersion())) {
                                appdto.setAppVersion(app.getVersion());
                            }
                            dto.setAppInfo(appdto);
                        }

                        ResponseVideoDto videoDto = new ResponseVideoDto();
                        ResponseVideoInfo video = meta.getVideo();
                        if (null != video) {
                            if (StringUtils.isNotEmpty(video.getUrl())) {
                                videoDto.setVideoUrl(video.getUrl());
                            }
                            if (null != video.getDuration()) {
                                videoDto.setDuration(video.getDuration());
                            }
                            if (StringUtils.isNotEmpty(video.getCover_url())) {
                                videoDto.setCoverImgUrls(new ArrayList<>(Collections.singletonList(video.getCover_url())));
                            }
                            if (StringUtils.isNotEmpty(video.getEnd_html())) {
                                videoDto.setEndHtml(video.getEnd_html());
                            }
                            dto.setVideoInfo(videoDto);
                        }
                        if (StringUtils.isNotEmpty(meta.getHtml())) {
                            dto.setHtmlContent(meta.getHtml());
                        }
                        if (null != meta.getPrice()) {
                            dto.setPrice(meta.getPrice().doubleValue());
                        }

                        List<ResponseTrackDto> tracks = new ArrayList<>();
                        dto.setTracks(tracks);
                        if (null != item.getTracks() && !item.getTracks().isEmpty()) {
                            List<ResponseTrack> respTrack = item.getTracks();
                            respTrack.forEach(track -> {
                                if (null != track.getEvent() && track.getEvent().equals("AD_IMPRESSION")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_CLICK")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("DOWN_LOAD_START")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("DOWN_LOAD_END")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("INSTALL_START")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("INSTALL_END")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("ACTIVE_END")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_AD_START")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_ONE_QUARTER")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_ONE_HALF")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_THREE_QUARTER")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_AD_END")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_AD_CLOSE")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_AD_FAIL")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_FAIL.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_MUTE")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_UN_MUTE")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_SKIP")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_FULLSCREEN")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_PAUSE")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_RESUME")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("VIDEO_REPLY")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_DEEPLINK")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_DEEPLINK_FAILED")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_DEEPLINK_CLICK")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_DEEPLINK_INSTALLED")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.APP_INSTALLED.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("AD_DEEPLINK_UN_INSTALL")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        tracks.add(new ResponseTrackDto(EventType.APP_NOT_INSTALL.getType(), new ArrayList<>(track.getUrls())));
                                    }
                                }
                                if (null != track.getEvent() && track.getEvent().equals("WIN_NOTICE")) {
                                    if (null != track.getUrls() && !track.getUrls().isEmpty()) {
                                        dto.setWinNoticeUrls(new ArrayList<>(track.getUrls()));
                                    }
                                }
                            });
                        }
                        // 宏替换 统一替换成平台的宏(事件)
                        dto.getTracks().forEach(track -> {
                            List<String> urls = track.getTrackUrls();
                            List<String> urlsList = replaceAllMacro(urls,rtbDto);
                            track.setTrackUrls(urlsList);
                        });
                        responseDto.getTags().add(dto);

                    }

            }
        });
        return responseDto;
    }

    private List<String> replaceAllMacro(List<String> urls, RtbRequestDto rtbDto){
        RequestNetworkDto networkDto=rtbDto.getNetwork();
        RequestDeviceDto  deviceDto=rtbDto.getDevice();

        urls = replaceMacro("__TS__", urls, MacroType.TIME.getCode());
        urls = replaceMacro("__TS10__", urls, MacroType.TIME_SECONDS.getCode());

        urls = replaceMacro("__REQ_WIDTH__", urls, MacroType.REQ_WIDTH.getCode());
        urls = replaceMacro("__REQ_HEIGHT__", urls, MacroType.REQ_HEIGHT.getCode());

        urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
        urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());

        urls = replaceMacro("__DOWN_X__", urls, MacroType.DOWN_X.getCode());
        urls = replaceMacro("__DOWN_Y__", urls, MacroType.DOWN_Y.getCode());
        urls = replaceMacro("__UP_X__", urls, MacroType.UP_X.getCode());
        urls = replaceMacro("__UP_Y__", urls, MacroType.UP_Y.getCode());


        urls = replaceMacro("__ABS_DOWN_X__", urls, MacroType.ABS_DOWN_X.getCode());
        urls = replaceMacro("__ABS_DOWN_Y__", urls, MacroType.ABS_DOWN_Y.getCode());
        urls = replaceMacro("__ABS_UP_X__", urls, MacroType.ABS_UP_X.getCode());
        urls = replaceMacro("__ABS_UP_Y__", urls, MacroType.ABS_UP_Y.getCode());


        if(StringUtils.isNotEmpty(deviceDto.getImei())){
            urls = replaceMacro("__IMEI__", urls, deviceDto.getImei());
        }
        if(StringUtils.isNotEmpty(deviceDto.getImeiMd5())){
            urls = replaceMacro("__IMEI2__", urls, deviceDto.getImeiMd5());
        }
        urls = replaceMacro("__IP__", urls,MacroType.IP.getCode());


        if(null!=networkDto.getConnectType()){
            ConnectionType connectionType=networkDto.getConnectType();

            if(connectionType==ConnectionType.ETHERNET||
                    connectionType==ConnectionType.NETWORK_CELLULAR||
                    connectionType==ConnectionType.UNKNOWN){
                urls = replaceMacro("__NET_TYPE__", urls,"UNKNOWN");
            }else{
                urls = replaceMacro("__NET_TYPE__", urls,connectionType.getDesc());

            }
        }
        if(null!=networkDto.getCarrierType()){
            CarrierType type=  networkDto.getCarrierType();
            Integer car=0;
            if(type==CarrierType.OTHER||type==CarrierType.UNKNOWN){
                urls = replaceMacro("__CARRIER__", urls,"0");
            }else{
                urls = replaceMacro("__CARRIER__", urls,type.getType().toString());
            }
        }
        if(StringUtils.isNotEmpty(deviceDto.getIdfa())){
            urls = replaceMacro("__IDFA__", urls, deviceDto.getIdfa());
        }
        if(StringUtils.isNotEmpty(deviceDto.getIdfaMd5())){
            urls = replaceMacro("__IDFA2__", urls, deviceDto.getIdfaMd5());
        }

        if(StringUtils.isNotEmpty(deviceDto.getAndroidId())){
            urls = replaceMacro("__ANDROIDID__", urls, deviceDto.getAndroidId());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())){
            urls = replaceMacro("__ANDROIDID2__", urls, deviceDto.getAndroidIdMd5());
        }
        if(StringUtils.isNotEmpty(deviceDto.getAaid())){
            urls = replaceMacro("__ALI_AAID__", urls, deviceDto.getAaid());
        }

        if(null!=deviceDto.getCaids()&&!deviceDto.getCaids().isEmpty()){
            List<String> list=new ArrayList<>();
            for(RequestCaidDto caid:deviceDto.getCaids()){
                StringBuffer buffer=new StringBuffer();
                if(StringUtils.isNotEmpty(caid.getVersion())){
                    buffer.append(caid.getVersion()).append("_");
                }else{
                    buffer.append("0").append("_");
                }
                if(StringUtils.isNotEmpty(caid.getCaid())){
                    buffer.append(caid);
                }
                list.add(buffer.toString());
            }
            String caidStr= org.apache.commons.lang3.StringUtils.join(list.toArray(new String[list.size()]),",");
            String newCaid=caidStr;
            try {
                newCaid=URLEncoder.encode(caidStr, "utf-8");
            }catch (Exception E){
            }
            urls = replaceMacro("__CAID__", urls, newCaid);
        }

        if(StringUtils.isNotEmpty(deviceDto.getOaid())){
            urls = replaceMacro("__OAID__", urls, deviceDto.getOaid());
        }

        if(StringUtils.isNotEmpty(networkDto.getMac())){
            urls = replaceMacro("__MAC__", urls, networkDto.getMac());
        }
        if(StringUtils.isNotEmpty(networkDto.getMacMd5())){
            urls = replaceMacro("__MAC2__", urls, networkDto.getMacMd5());
        }
        if(StringUtils.isNotEmpty(networkDto.getMac())){
            String mac=networkDto.getMac().replace(":","");
            urls = replaceMacro("__MAC3__", urls, Md5.md5(mac));
        }

        if(null!=deviceDto.getOsType()){
            OsType type=deviceDto.getOsType();
            if(type==OsType.ANDROID){
                urls = replaceMacro("__OS__", urls, "1");
            }else{
                urls = replaceMacro("__OS__", urls, "2");
            }
        }

        urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());

        urls = replaceMacro("__E_START__", urls, MacroType.START_TIME.getCode());
        urls = replaceMacro("__E_END__", urls, MacroType.END_TIME.getCode());

        urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
        urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());
        urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
        urls = replaceMacro("__DP_DOWN_Y__", urls, MacroType.DP_DOWN_Y.getCode());
        urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
        urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());


        urls = replaceMacro("__AD_LT_X__", urls, MacroType.DISPLAY_LUX.getCode());
        urls = replaceMacro("__AD_LT_Y__", urls, MacroType.DISPLAY_LUY.getCode());
        urls = replaceMacro("__AD_RB_X__", urls, MacroType.DISPLAY_RDX.getCode());
        urls = replaceMacro("__AD_RB_Y__", urls, MacroType.DISPLAY_RDY.getCode());

        urls = replaceMacro("__BT_LT_X__", urls, MacroType.BUTTON_LUX.getCode());
        urls = replaceMacro("__BT_LT_Y__", urls, MacroType.BUTTON_LUY.getCode());
        urls = replaceMacro("__BT_RB_X__", urls, MacroType.BUTTON_RDX.getCode());
        urls = replaceMacro("__BT_RB_Y__", urls, MacroType.BUTTON_RDY.getCode());


        urls = replaceMacro("__CLICK_TIME__", urls, MacroType.START_TIME.getCode());

        urls = replaceMacro("__LONGITUDE__", urls, MacroType.LON.getCode());
        urls = replaceMacro("__LATITUDE__", urls, MacroType.LAT.getCode());


        if(StringUtils.isNotEmpty(deviceDto.getOpenUdId())){
            urls = replaceMacro("__OPENUDID__", urls, deviceDto.getOpenUdId());
        }

        urls = replaceMacro("__PLAY_MSEC__", urls, MacroType.VIDEO_PROGRESS.getCode());

        urls = replaceMacro("__P_RATE__", urls, MacroType.VIDEO_PROGRESS_SEC.getCode());

        urls = replaceMacro("__END_PLAY_SEC__", urls, MacroType.VIDEO_TIME.getCode());

        urls = replaceMacro("__PLAY_BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());

        urls = replaceMacro("__PLAY_FINISH__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());

        urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());

        urls = replaceMacro("__PLAY_SCENE__", urls, MacroType.VIDEO_SCENE.getCode());

        urls = replaceMacro("__PLAY_TYPE__", urls, MacroType.VIDEO_TYPE.getCode());

        urls = replaceMacro("__PLAY_BEHAVIOR__", urls, MacroType.VIDEO_BEHAVIOR.getCode());

        urls = replaceMacro("__PLAY_STATUS__", urls, MacroType.VIDEO_STATUS.getCode());


        return urls;
    }



    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) throws Exception {
        // 是否有请求成功的
        boolean hasRight = false;
        List<String> urls = reqDto.getUrls();
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }
}
