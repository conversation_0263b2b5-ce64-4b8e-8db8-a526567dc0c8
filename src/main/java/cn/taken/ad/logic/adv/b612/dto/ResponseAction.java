package cn.taken.ad.logic.adv.b612.dto;

import java.io.Serializable;

public class ResponseAction implements Serializable {

    private String landingpage_url;

    private String deeplink_url;

    private String wxoid;

    private String wxp;

    private String store_id;

    private String universal_link;

    private String market_url;

    private String app_download_url;

    private String quick_app_url;

    public String getLandingpage_url() {
        return landingpage_url;
    }

    public void setLandingpage_url(String landingpage_url) {
        this.landingpage_url = landingpage_url;
    }

    public String getDeeplink_url() {
        return deeplink_url;
    }

    public void setDeeplink_url(String deeplink_url) {
        this.deeplink_url = deeplink_url;
    }

    public String getWxoid() {
        return wxoid;
    }

    public void setWxoid(String wxoid) {
        this.wxoid = wxoid;
    }

    public String getWxp() {
        return wxp;
    }

    public void setWxp(String wxp) {
        this.wxp = wxp;
    }

    public String getStore_id() {
        return store_id;
    }

    public void setStore_id(String store_id) {
        this.store_id = store_id;
    }

    public String getUniversal_link() {
        return universal_link;
    }

    public void setUniversal_link(String universal_link) {
        this.universal_link = universal_link;
    }

    public String getMarket_url() {
        return market_url;
    }

    public void setMarket_url(String market_url) {
        this.market_url = market_url;
    }

    public String getApp_download_url() {
        return app_download_url;
    }

    public void setApp_download_url(String app_download_url) {
        this.app_download_url = app_download_url;
    }

    public String getQuick_app_url() {
        return quick_app_url;
    }

    public void setQuick_app_url(String quick_app_url) {
        this.quick_app_url = quick_app_url;
    }
}
