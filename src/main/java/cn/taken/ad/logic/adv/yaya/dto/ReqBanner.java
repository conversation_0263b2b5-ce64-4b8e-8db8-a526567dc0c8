// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf type {@code ReqBanner}
 */
public final class ReqBanner extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:ReqBanner)
    ReqBannerOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ReqBanner.class.getName());
  }
  // Use ReqBanner.newBuilder() to construct.
  private ReqBanner(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ReqBanner() {
    mines_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqBanner_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqBanner_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.yaya.dto.ReqBanner.class, cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder.class);
  }

  public static final int W_FIELD_NUMBER = 1;
  private int w_ = 0;
  /**
   * <code>int32 w = 1;</code>
   * @return The w.
   */
  @java.lang.Override
  public int getW() {
    return w_;
  }

  public static final int H_FIELD_NUMBER = 2;
  private int h_ = 0;
  /**
   * <code>int32 h = 2;</code>
   * @return The h.
   */
  @java.lang.Override
  public int getH() {
    return h_;
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>int32 type = 3;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int MINES_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object mines_ = "";
  /**
   * <code>string mines = 4;</code>
   * @return The mines.
   */
  @java.lang.Override
  public java.lang.String getMines() {
    java.lang.Object ref = mines_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mines_ = s;
      return s;
    }
  }
  /**
   * <code>string mines = 4;</code>
   * @return The bytes for mines.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMinesBytes() {
    java.lang.Object ref = mines_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mines_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISSUPPORTVIDEO_FIELD_NUMBER = 5;
  private int isSupportVideo_ = 0;
  /**
   * <code>int32 isSupportVideo = 5;</code>
   * @return The isSupportVideo.
   */
  @java.lang.Override
  public int getIsSupportVideo() {
    return isSupportVideo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (w_ != 0) {
      output.writeInt32(1, w_);
    }
    if (h_ != 0) {
      output.writeInt32(2, h_);
    }
    if (type_ != 0) {
      output.writeInt32(3, type_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mines_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, mines_);
    }
    if (isSupportVideo_ != 0) {
      output.writeInt32(5, isSupportVideo_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (w_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, w_);
    }
    if (h_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, h_);
    }
    if (type_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, type_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(mines_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, mines_);
    }
    if (isSupportVideo_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, isSupportVideo_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.yaya.dto.ReqBanner)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.yaya.dto.ReqBanner other = (cn.taken.ad.logic.adv.yaya.dto.ReqBanner) obj;

    if (getW()
        != other.getW()) return false;
    if (getH()
        != other.getH()) return false;
    if (getType()
        != other.getType()) return false;
    if (!getMines()
        .equals(other.getMines())) return false;
    if (getIsSupportVideo()
        != other.getIsSupportVideo()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + W_FIELD_NUMBER;
    hash = (53 * hash) + getW();
    hash = (37 * hash) + H_FIELD_NUMBER;
    hash = (53 * hash) + getH();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType();
    hash = (37 * hash) + MINES_FIELD_NUMBER;
    hash = (53 * hash) + getMines().hashCode();
    hash = (37 * hash) + ISSUPPORTVIDEO_FIELD_NUMBER;
    hash = (53 * hash) + getIsSupportVideo();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.yaya.dto.ReqBanner prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code ReqBanner}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:ReqBanner)
      cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqBanner_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqBanner_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.yaya.dto.ReqBanner.class, cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.yaya.dto.ReqBanner.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      w_ = 0;
      h_ = 0;
      type_ = 0;
      mines_ = "";
      isSupportVideo_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqBanner_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqBanner getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqBanner build() {
      cn.taken.ad.logic.adv.yaya.dto.ReqBanner result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqBanner buildPartial() {
      cn.taken.ad.logic.adv.yaya.dto.ReqBanner result = new cn.taken.ad.logic.adv.yaya.dto.ReqBanner(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.yaya.dto.ReqBanner result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.w_ = w_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.h_ = h_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.mines_ = mines_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.isSupportVideo_ = isSupportVideo_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.yaya.dto.ReqBanner) {
        return mergeFrom((cn.taken.ad.logic.adv.yaya.dto.ReqBanner)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.yaya.dto.ReqBanner other) {
      if (other == cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance()) return this;
      if (other.getW() != 0) {
        setW(other.getW());
      }
      if (other.getH() != 0) {
        setH(other.getH());
      }
      if (other.getType() != 0) {
        setType(other.getType());
      }
      if (!other.getMines().isEmpty()) {
        mines_ = other.mines_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.getIsSupportVideo() != 0) {
        setIsSupportVideo(other.getIsSupportVideo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              w_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              h_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              mines_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              isSupportVideo_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int w_ ;
    /**
     * <code>int32 w = 1;</code>
     * @return The w.
     */
    @java.lang.Override
    public int getW() {
      return w_;
    }
    /**
     * <code>int32 w = 1;</code>
     * @param value The w to set.
     * @return This builder for chaining.
     */
    public Builder setW(int value) {

      w_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int32 w = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearW() {
      bitField0_ = (bitField0_ & ~0x00000001);
      w_ = 0;
      onChanged();
      return this;
    }

    private int h_ ;
    /**
     * <code>int32 h = 2;</code>
     * @return The h.
     */
    @java.lang.Override
    public int getH() {
      return h_;
    }
    /**
     * <code>int32 h = 2;</code>
     * @param value The h to set.
     * @return This builder for chaining.
     */
    public Builder setH(int value) {

      h_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int32 h = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearH() {
      bitField0_ = (bitField0_ & ~0x00000002);
      h_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>int32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>int32 type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>int32 type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object mines_ = "";
    /**
     * <code>string mines = 4;</code>
     * @return The mines.
     */
    public java.lang.String getMines() {
      java.lang.Object ref = mines_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mines_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string mines = 4;</code>
     * @return The bytes for mines.
     */
    public com.google.protobuf.ByteString
        getMinesBytes() {
      java.lang.Object ref = mines_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mines_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string mines = 4;</code>
     * @param value The mines to set.
     * @return This builder for chaining.
     */
    public Builder setMines(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      mines_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string mines = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMines() {
      mines_ = getDefaultInstance().getMines();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string mines = 4;</code>
     * @param value The bytes for mines to set.
     * @return This builder for chaining.
     */
    public Builder setMinesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      mines_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int isSupportVideo_ ;
    /**
     * <code>int32 isSupportVideo = 5;</code>
     * @return The isSupportVideo.
     */
    @java.lang.Override
    public int getIsSupportVideo() {
      return isSupportVideo_;
    }
    /**
     * <code>int32 isSupportVideo = 5;</code>
     * @param value The isSupportVideo to set.
     * @return This builder for chaining.
     */
    public Builder setIsSupportVideo(int value) {

      isSupportVideo_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>int32 isSupportVideo = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsSupportVideo() {
      bitField0_ = (bitField0_ & ~0x00000010);
      isSupportVideo_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:ReqBanner)
  }

  // @@protoc_insertion_point(class_scope:ReqBanner)
  private static final cn.taken.ad.logic.adv.yaya.dto.ReqBanner DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.yaya.dto.ReqBanner();
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqBanner getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqBanner>
      PARSER = new com.google.protobuf.AbstractParser<ReqBanner>() {
    @java.lang.Override
    public ReqBanner parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ReqBanner> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqBanner> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqBanner getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

