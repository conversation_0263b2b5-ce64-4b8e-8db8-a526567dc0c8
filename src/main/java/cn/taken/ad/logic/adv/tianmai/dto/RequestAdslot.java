package cn.taken.ad.logic.adv.tianmai.dto;

import java.io.Serializable;

public class RequestAdslot implements Serializable {

    private String adslot_id;//广告位id
    private Integer render_type;//素材渲染形式：0-任意形式，1-json格式（开发者需要自己渲染）2-html格式（已经渲染好的代码段，目前较少）。 任意形式时，会选择收益较高的广告形式返回
    private Integer deeplink;//是否支持deeplink，0-不支持，1-支持
    private Integer bidfloor;//广告位底价单位(分）RTB 必填

    public String getAdslot_id() {
        return adslot_id;
    }

    public void setAdslot_id(String adslot_id) {
        this.adslot_id = adslot_id;
    }

    public Integer getRender_type() {
        return render_type;
    }

    public void setRender_type(Integer render_type) {
        this.render_type = render_type;
    }

    public Integer getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(Integer deeplink) {
        this.deeplink = deeplink;
    }

    public Integer getBidfloor() {
        return bidfloor;
    }

    public void setBidfloor(Integer bidfloor) {
        this.bidfloor = bidfloor;
    }
}
