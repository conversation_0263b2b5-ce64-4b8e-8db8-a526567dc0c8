// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: most_mob_api.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.mostmob.dto;

/**
 * Protobuf enum {@code AdType}
 */
public enum AdType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未知
   * </pre>
   *
   * <code>UnknownAdType = 0;</code>
   */
  UnknownAdType(0),
  /**
   * <pre>
   * 开屏
   * </pre>
   *
   * <code>Splash = 1;</code>
   */
  Splash(1),
  /**
   * <pre>
   * 插屏
   * </pre>
   *
   * <code>Interstitial = 2;</code>
   */
  Interstitial(2),
  /**
   * <pre>
   * 原生
   * </pre>
   *
   * <code>Native = 3;</code>
   */
  Native(3),
  /**
   * <pre>
   * 激励视频
   * </pre>
   *
   * <code>RewardedVideo = 4;</code>
   */
  RewardedVideo(4),
  /**
   * <pre>
   * 横幅
   * </pre>
   *
   * <code>Banner = 5;</code>
   */
  Banner(5),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      AdType.class.getName());
  }
  /**
   * <pre>
   * 未知
   * </pre>
   *
   * <code>UnknownAdType = 0;</code>
   */
  public static final int UnknownAdType_VALUE = 0;
  /**
   * <pre>
   * 开屏
   * </pre>
   *
   * <code>Splash = 1;</code>
   */
  public static final int Splash_VALUE = 1;
  /**
   * <pre>
   * 插屏
   * </pre>
   *
   * <code>Interstitial = 2;</code>
   */
  public static final int Interstitial_VALUE = 2;
  /**
   * <pre>
   * 原生
   * </pre>
   *
   * <code>Native = 3;</code>
   */
  public static final int Native_VALUE = 3;
  /**
   * <pre>
   * 激励视频
   * </pre>
   *
   * <code>RewardedVideo = 4;</code>
   */
  public static final int RewardedVideo_VALUE = 4;
  /**
   * <pre>
   * 横幅
   * </pre>
   *
   * <code>Banner = 5;</code>
   */
  public static final int Banner_VALUE = 5;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static AdType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static AdType forNumber(int value) {
    switch (value) {
      case 0: return UnknownAdType;
      case 1: return Splash;
      case 2: return Interstitial;
      case 3: return Native;
      case 4: return RewardedVideo;
      case 5: return Banner;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<AdType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      AdType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<AdType>() {
          public AdType findValueByNumber(int number) {
            return AdType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.mostmob.dto.MostMobAdvRequest.getDescriptor().getEnumTypes().get(5);
  }

  private static final AdType[] VALUES = values();

  public static AdType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private AdType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:AdType)
}

