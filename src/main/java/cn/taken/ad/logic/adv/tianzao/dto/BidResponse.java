// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: tianzao.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.tianzao.dto;

/**
 * <pre>
 * 响应体
 * </pre>
 *
 * Protobuf type {@code BidResponse}
 */
public final class BidResponse extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:BidResponse)
    BidResponseOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      BidResponse.class.getName());
  }
  // Use BidResponse.newBuilder() to construct.
  private BidResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BidResponse() {
    id_ = "";
    msg_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.tianzao.dto.BidResponse.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Builder.class);
  }

  public interface BidOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BidResponse.Bid)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return Whether the bidId field is set.
     */
    boolean hasBidId();
    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return The bidId.
     */
    java.lang.String getBidId();
    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return The bytes for bidId.
     */
    com.google.protobuf.ByteString
        getBidIdBytes();

    /**
     * <pre>
     * 广告位ID
     * </pre>
     *
     * <code>string tagId = 2;</code>
     * @return The tagId.
     */
    java.lang.String getTagId();
    /**
     * <pre>
     * 广告位ID
     * </pre>
     *
     * <code>string tagId = 2;</code>
     * @return The bytes for tagId.
     */
    com.google.protobuf.ByteString
        getTagIdBytes();

    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return Whether the desc field is set.
     */
    boolean hasDesc();
    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return The desc.
     */
    java.lang.String getDesc();
    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return The bytes for desc.
     */
    com.google.protobuf.ByteString
        getDescBytes();

    /**
     * <pre>
     * 广告图标icon链接
     * </pre>
     *
     * <code>string iconUrl = 5;</code>
     * @return The iconUrl.
     */
    java.lang.String getIconUrl();
    /**
     * <pre>
     * 广告图标icon链接
     * </pre>
     *
     * <code>string iconUrl = 5;</code>
     * @return The bytes for iconUrl.
     */
    com.google.protobuf.ByteString
        getIconUrlBytes();

    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @return A list containing the imgUrls.
     */
    java.util.List<java.lang.String>
        getImgUrlsList();
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @return The count of imgUrls.
     */
    int getImgUrlsCount();
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @param index The index of the element to return.
     * @return The imgUrls at the given index.
     */
    java.lang.String getImgUrls(int index);
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imgUrls at the given index.
     */
    com.google.protobuf.ByteString
        getImgUrlsBytes(int index);

    /**
     * <pre>
     * 广告宽度，可能为空
     * </pre>
     *
     * <code>optional sint32 w = 7;</code>
     * @return Whether the w field is set.
     */
    boolean hasW();
    /**
     * <pre>
     * 广告宽度，可能为空
     * </pre>
     *
     * <code>optional sint32 w = 7;</code>
     * @return The w.
     */
    int getW();

    /**
     * <pre>
     * 广告高度，可能为空
     * </pre>
     *
     * <code>optional sint32 h = 8;</code>
     * @return Whether the h field is set.
     */
    boolean hasH();
    /**
     * <pre>
     * 广告高度，可能为空
     * </pre>
     *
     * <code>optional sint32 h = 8;</code>
     * @return The h.
     */
    int getH();

    /**
     * <pre>
     * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
     * </pre>
     *
     * <code>string landingUrl = 9;</code>
     * @return The landingUrl.
     */
    java.lang.String getLandingUrl();
    /**
     * <pre>
     * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
     * </pre>
     *
     * <code>string landingUrl = 9;</code>
     * @return The bytes for landingUrl.
     */
    com.google.protobuf.ByteString
        getLandingUrlBytes();

    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return Whether the deeplink field is set.
     */
    boolean hasDeeplink();
    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return The deeplink.
     */
    java.lang.String getDeeplink();
    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return The bytes for deeplink.
     */
    com.google.protobuf.ByteString
        getDeeplinkBytes();

    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return Whether the downloadUrl field is set.
     */
    boolean hasDownloadUrl();
    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return The downloadUrl.
     */
    java.lang.String getDownloadUrl();
    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return The bytes for downloadUrl.
     */
    com.google.protobuf.ByteString
        getDownloadUrlBytes();

    /**
     * <pre>
     * 竞价广告出价，单位: 分/cpm(竞价模式)
     * </pre>
     *
     * <code>optional sint32 bidFloor = 12;</code>
     * @return Whether the bidFloor field is set.
     */
    boolean hasBidFloor();
    /**
     * <pre>
     * 竞价广告出价，单位: 分/cpm(竞价模式)
     * </pre>
     *
     * <code>optional sint32 bidFloor = 12;</code>
     * @return The bidFloor.
     */
    int getBidFloor();

    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @return A list containing the winUrls.
     */
    java.util.List<java.lang.String>
        getWinUrlsList();
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @return The count of winUrls.
     */
    int getWinUrlsCount();
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @param index The index of the element to return.
     * @return The winUrls at the given index.
     */
    java.lang.String getWinUrls(int index);
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @param index The index of the value to return.
     * @return The bytes of the winUrls at the given index.
     */
    com.google.protobuf.ByteString
        getWinUrlsBytes(int index);

    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @return A list containing the loseUrls.
     */
    java.util.List<java.lang.String>
        getLoseUrlsList();
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @return The count of loseUrls.
     */
    int getLoseUrlsCount();
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @param index The index of the element to return.
     * @return The loseUrls at the given index.
     */
    java.lang.String getLoseUrls(int index);
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @param index The index of the value to return.
     * @return The bytes of the loseUrls at the given index.
     */
    com.google.protobuf.ByteString
        getLoseUrlsBytes(int index);

    /**
     * <pre>
     * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
     * </pre>
     *
     * <code>optional sint32 cType = 15;</code>
     * @return Whether the cType field is set.
     */
    boolean hasCType();
    /**
     * <pre>
     * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
     * </pre>
     *
     * <code>optional sint32 cType = 15;</code>
     * @return The cType.
     */
    int getCType();

    /**
     * <pre>
     * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
     * </pre>
     *
     * <code>optional sint32 ciType = 16;</code>
     * @return Whether the ciType field is set.
     */
    boolean hasCiType();
    /**
     * <pre>
     * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
     * </pre>
     *
     * <code>optional sint32 ciType = 16;</code>
     * @return The ciType.
     */
    int getCiType();

    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     * @return Whether the app field is set.
     */
    boolean hasApp();
    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     * @return The app.
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getApp();
    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder getAppOrBuilder();

    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     * @return Whether the video field is set.
     */
    boolean hasVideo();
    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     * @return The video.
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getVideo();
    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder getVideoOrBuilder();

    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> 
        getTrackersList();
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getTrackers(int index);
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    int getTrackersCount();
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    java.util.List<? extends cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder> 
        getTrackersOrBuilderList();
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder getTrackersOrBuilder(
        int index);

    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return Whether the universalLink field is set.
     */
    boolean hasUniversalLink();
    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return The universalLink.
     */
    java.lang.String getUniversalLink();
    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return The bytes for universalLink.
     */
    com.google.protobuf.ByteString
        getUniversalLinkBytes();

    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @return A list containing the clickAreaReportUrls.
     */
    java.util.List<java.lang.String>
        getClickAreaReportUrlsList();
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @return The count of clickAreaReportUrls.
     */
    int getClickAreaReportUrlsCount();
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @param index The index of the element to return.
     * @return The clickAreaReportUrls at the given index.
     */
    java.lang.String getClickAreaReportUrls(int index);
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @param index The index of the value to return.
     * @return The bytes of the clickAreaReportUrls at the given index.
     */
    com.google.protobuf.ByteString
        getClickAreaReportUrlsBytes(int index);
  }
  /**
   * Protobuf type {@code BidResponse.Bid}
   */
  public static final class Bid extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:BidResponse.Bid)
      BidOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Bid.class.getName());
    }
    // Use Bid.newBuilder() to construct.
    private Bid(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Bid() {
      bidId_ = "";
      tagId_ = "";
      title_ = "";
      desc_ = "";
      iconUrl_ = "";
      imgUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      landingUrl_ = "";
      deeplink_ = "";
      downloadUrl_ = "";
      winUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      loseUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      trackers_ = java.util.Collections.emptyList();
      universalLink_ = "";
      clickAreaReportUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder.class);
    }

    public interface AppOrBuilder extends
        // @@protoc_insertion_point(interface_extends:BidResponse.Bid.App)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 应用名称
       * </pre>
       *
       * <code>string name = 1;</code>
       * @return The name.
       */
      java.lang.String getName();
      /**
       * <pre>
       * 应用名称
       * </pre>
       *
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>string bundle = 2;</code>
       * @return The bundle.
       */
      java.lang.String getBundle();
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>string bundle = 2;</code>
       * @return The bytes for bundle.
       */
      com.google.protobuf.ByteString
          getBundleBytes();

      /**
       * <pre>
       * 应用包大小，单位：KB
       * </pre>
       *
       * <code>optional sint32 size = 3;</code>
       * @return Whether the size field is set.
       */
      boolean hasSize();
      /**
       * <pre>
       * 应用包大小，单位：KB
       * </pre>
       *
       * <code>optional sint32 size = 3;</code>
       * @return The size.
       */
      int getSize();

      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return Whether the ver field is set.
       */
      boolean hasVer();
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return The ver.
       */
      java.lang.String getVer();
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return The bytes for ver.
       */
      com.google.protobuf.ByteString
          getVerBytes();

      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return Whether the privacyUrl field is set.
       */
      boolean hasPrivacyUrl();
      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return The privacyUrl.
       */
      java.lang.String getPrivacyUrl();
      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return The bytes for privacyUrl.
       */
      com.google.protobuf.ByteString
          getPrivacyUrlBytes();

      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return Whether the permContent field is set.
       */
      boolean hasPermContent();
      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return The permContent.
       */
      java.lang.String getPermContent();
      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return The bytes for permContent.
       */
      com.google.protobuf.ByteString
          getPermContentBytes();

      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return Whether the developer field is set.
       */
      boolean hasDeveloper();
      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return The developer.
       */
      java.lang.String getDeveloper();
      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return The bytes for developer.
       */
      com.google.protobuf.ByteString
          getDeveloperBytes();
    }
    /**
     * Protobuf type {@code BidResponse.Bid.App}
     */
    public static final class App extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:BidResponse.Bid.App)
        AppOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          App.class.getName());
      }
      // Use App.newBuilder() to construct.
      private App(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private App() {
        name_ = "";
        bundle_ = "";
        ver_ = "";
        privacyUrl_ = "";
        permContent_ = "";
        developer_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_App_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_App_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder.class);
      }

      private int bitField0_;
      public static final int NAME_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile java.lang.Object name_ = "";
      /**
       * <pre>
       * 应用名称
       * </pre>
       *
       * <code>string name = 1;</code>
       * @return The name.
       */
      @java.lang.Override
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用名称
       * </pre>
       *
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int BUNDLE_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private volatile java.lang.Object bundle_ = "";
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>string bundle = 2;</code>
       * @return The bundle.
       */
      @java.lang.Override
      public java.lang.String getBundle() {
        java.lang.Object ref = bundle_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bundle_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用包名
       * </pre>
       *
       * <code>string bundle = 2;</code>
       * @return The bytes for bundle.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getBundleBytes() {
        java.lang.Object ref = bundle_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bundle_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SIZE_FIELD_NUMBER = 3;
      private int size_ = 0;
      /**
       * <pre>
       * 应用包大小，单位：KB
       * </pre>
       *
       * <code>optional sint32 size = 3;</code>
       * @return Whether the size field is set.
       */
      @java.lang.Override
      public boolean hasSize() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 应用包大小，单位：KB
       * </pre>
       *
       * <code>optional sint32 size = 3;</code>
       * @return The size.
       */
      @java.lang.Override
      public int getSize() {
        return size_;
      }

      public static final int VER_FIELD_NUMBER = 4;
      @SuppressWarnings("serial")
      private volatile java.lang.Object ver_ = "";
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return Whether the ver field is set.
       */
      @java.lang.Override
      public boolean hasVer() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return The ver.
       */
      @java.lang.Override
      public java.lang.String getVer() {
        java.lang.Object ref = ver_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          ver_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用版本号
       * </pre>
       *
       * <code>optional string ver = 4;</code>
       * @return The bytes for ver.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getVerBytes() {
        java.lang.Object ref = ver_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ver_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PRIVACYURL_FIELD_NUMBER = 5;
      @SuppressWarnings("serial")
      private volatile java.lang.Object privacyUrl_ = "";
      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return Whether the privacyUrl field is set.
       */
      @java.lang.Override
      public boolean hasPrivacyUrl() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return The privacyUrl.
       */
      @java.lang.Override
      public java.lang.String getPrivacyUrl() {
        java.lang.Object ref = privacyUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          privacyUrl_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 隐私说明链接
       * </pre>
       *
       * <code>optional string privacyUrl = 5;</code>
       * @return The bytes for privacyUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPrivacyUrlBytes() {
        java.lang.Object ref = privacyUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          privacyUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PERMCONTENT_FIELD_NUMBER = 6;
      @SuppressWarnings("serial")
      private volatile java.lang.Object permContent_ = "";
      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return Whether the permContent field is set.
       */
      @java.lang.Override
      public boolean hasPermContent() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return The permContent.
       */
      @java.lang.Override
      public java.lang.String getPermContent() {
        java.lang.Object ref = permContent_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          permContent_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用权限列表
       * </pre>
       *
       * <code>optional string permContent = 6;</code>
       * @return The bytes for permContent.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPermContentBytes() {
        java.lang.Object ref = permContent_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          permContent_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DEVELOPER_FIELD_NUMBER = 7;
      @SuppressWarnings("serial")
      private volatile java.lang.Object developer_ = "";
      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return Whether the developer field is set.
       */
      @java.lang.Override
      public boolean hasDeveloper() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return The developer.
       */
      @java.lang.Override
      public java.lang.String getDeveloper() {
        java.lang.Object ref = developer_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          developer_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 应用开发者
       * </pre>
       *
       * <code>optional string developer = 7;</code>
       * @return The bytes for developer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDeveloperBytes() {
        java.lang.Object ref = developer_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          developer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(name_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, name_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bundle_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, bundle_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt32(3, size_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 4, ver_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 5, privacyUrl_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 6, permContent_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 7, developer_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(name_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, name_);
        }
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bundle_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(2, bundle_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(3, size_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(4, ver_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(5, privacyUrl_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(6, permContent_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(7, developer_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App other = (cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App) obj;

        if (!getName()
            .equals(other.getName())) return false;
        if (!getBundle()
            .equals(other.getBundle())) return false;
        if (hasSize() != other.hasSize()) return false;
        if (hasSize()) {
          if (getSize()
              != other.getSize()) return false;
        }
        if (hasVer() != other.hasVer()) return false;
        if (hasVer()) {
          if (!getVer()
              .equals(other.getVer())) return false;
        }
        if (hasPrivacyUrl() != other.hasPrivacyUrl()) return false;
        if (hasPrivacyUrl()) {
          if (!getPrivacyUrl()
              .equals(other.getPrivacyUrl())) return false;
        }
        if (hasPermContent() != other.hasPermContent()) return false;
        if (hasPermContent()) {
          if (!getPermContent()
              .equals(other.getPermContent())) return false;
        }
        if (hasDeveloper() != other.hasDeveloper()) return false;
        if (hasDeveloper()) {
          if (!getDeveloper()
              .equals(other.getDeveloper())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + BUNDLE_FIELD_NUMBER;
        hash = (53 * hash) + getBundle().hashCode();
        if (hasSize()) {
          hash = (37 * hash) + SIZE_FIELD_NUMBER;
          hash = (53 * hash) + getSize();
        }
        if (hasVer()) {
          hash = (37 * hash) + VER_FIELD_NUMBER;
          hash = (53 * hash) + getVer().hashCode();
        }
        if (hasPrivacyUrl()) {
          hash = (37 * hash) + PRIVACYURL_FIELD_NUMBER;
          hash = (53 * hash) + getPrivacyUrl().hashCode();
        }
        if (hasPermContent()) {
          hash = (37 * hash) + PERMCONTENT_FIELD_NUMBER;
          hash = (53 * hash) + getPermContent().hashCode();
        }
        if (hasDeveloper()) {
          hash = (37 * hash) + DEVELOPER_FIELD_NUMBER;
          hash = (53 * hash) + getDeveloper().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code BidResponse.Bid.App}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:BidResponse.Bid.App)
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_App_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_App_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder.class);
        }

        // Construct using cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          name_ = "";
          bundle_ = "";
          size_ = 0;
          ver_ = "";
          privacyUrl_ = "";
          permContent_ = "";
          developer_ = "";
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_App_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getDefaultInstanceForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App build() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App buildPartial() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App result = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.name_ = name_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.bundle_ = bundle_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.size_ = size_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.ver_ = ver_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.privacyUrl_ = privacyUrl_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.permContent_ = permContent_;
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.developer_ = developer_;
            to_bitField0_ |= 0x00000010;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App) {
            return mergeFrom((cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App other) {
          if (other == cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance()) return this;
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (!other.getBundle().isEmpty()) {
            bundle_ = other.bundle_;
            bitField0_ |= 0x00000002;
            onChanged();
          }
          if (other.hasSize()) {
            setSize(other.getSize());
          }
          if (other.hasVer()) {
            ver_ = other.ver_;
            bitField0_ |= 0x00000008;
            onChanged();
          }
          if (other.hasPrivacyUrl()) {
            privacyUrl_ = other.privacyUrl_;
            bitField0_ |= 0x00000010;
            onChanged();
          }
          if (other.hasPermContent()) {
            permContent_ = other.permContent_;
            bitField0_ |= 0x00000020;
            onChanged();
          }
          if (other.hasDeveloper()) {
            developer_ = other.developer_;
            bitField0_ |= 0x00000040;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  name_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 18: {
                  bundle_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 18
                case 24: {
                  size_ = input.readSInt32();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 34: {
                  ver_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 34
                case 42: {
                  privacyUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000010;
                  break;
                } // case 42
                case 50: {
                  permContent_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000020;
                  break;
                } // case 50
                case 58: {
                  developer_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 58
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.lang.Object name_ = "";
        /**
         * <pre>
         * 应用名称
         * </pre>
         *
         * <code>string name = 1;</code>
         * @return The name.
         */
        public java.lang.String getName() {
          java.lang.Object ref = name_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用名称
         * </pre>
         *
         * <code>string name = 1;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          java.lang.Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用名称
         * </pre>
         *
         * <code>string name = 1;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          name_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用名称
         * </pre>
         *
         * <code>string name = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {
          name_ = getDefaultInstance().getName();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用名称
         * </pre>
         *
         * <code>string name = 1;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          name_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private java.lang.Object bundle_ = "";
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>string bundle = 2;</code>
         * @return The bundle.
         */
        public java.lang.String getBundle() {
          java.lang.Object ref = bundle_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            bundle_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>string bundle = 2;</code>
         * @return The bytes for bundle.
         */
        public com.google.protobuf.ByteString
            getBundleBytes() {
          java.lang.Object ref = bundle_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            bundle_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>string bundle = 2;</code>
         * @param value The bundle to set.
         * @return This builder for chaining.
         */
        public Builder setBundle(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          bundle_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>string bundle = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearBundle() {
          bundle_ = getDefaultInstance().getBundle();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用包名
         * </pre>
         *
         * <code>string bundle = 2;</code>
         * @param value The bytes for bundle to set.
         * @return This builder for chaining.
         */
        public Builder setBundleBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          bundle_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        private int size_ ;
        /**
         * <pre>
         * 应用包大小，单位：KB
         * </pre>
         *
         * <code>optional sint32 size = 3;</code>
         * @return Whether the size field is set.
         */
        @java.lang.Override
        public boolean hasSize() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <pre>
         * 应用包大小，单位：KB
         * </pre>
         *
         * <code>optional sint32 size = 3;</code>
         * @return The size.
         */
        @java.lang.Override
        public int getSize() {
          return size_;
        }
        /**
         * <pre>
         * 应用包大小，单位：KB
         * </pre>
         *
         * <code>optional sint32 size = 3;</code>
         * @param value The size to set.
         * @return This builder for chaining.
         */
        public Builder setSize(int value) {

          size_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用包大小，单位：KB
         * </pre>
         *
         * <code>optional sint32 size = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearSize() {
          bitField0_ = (bitField0_ & ~0x00000004);
          size_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object ver_ = "";
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @return Whether the ver field is set.
         */
        public boolean hasVer() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @return The ver.
         */
        public java.lang.String getVer() {
          java.lang.Object ref = ver_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            ver_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @return The bytes for ver.
         */
        public com.google.protobuf.ByteString
            getVerBytes() {
          java.lang.Object ref = ver_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            ver_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @param value The ver to set.
         * @return This builder for chaining.
         */
        public Builder setVer(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ver_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearVer() {
          ver_ = getDefaultInstance().getVer();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用版本号
         * </pre>
         *
         * <code>optional string ver = 4;</code>
         * @param value The bytes for ver to set.
         * @return This builder for chaining.
         */
        public Builder setVerBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ver_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }

        private java.lang.Object privacyUrl_ = "";
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @return Whether the privacyUrl field is set.
         */
        public boolean hasPrivacyUrl() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @return The privacyUrl.
         */
        public java.lang.String getPrivacyUrl() {
          java.lang.Object ref = privacyUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            privacyUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @return The bytes for privacyUrl.
         */
        public com.google.protobuf.ByteString
            getPrivacyUrlBytes() {
          java.lang.Object ref = privacyUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            privacyUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @param value The privacyUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPrivacyUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          privacyUrl_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrivacyUrl() {
          privacyUrl_ = getDefaultInstance().getPrivacyUrl();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 隐私说明链接
         * </pre>
         *
         * <code>optional string privacyUrl = 5;</code>
         * @param value The bytes for privacyUrl to set.
         * @return This builder for chaining.
         */
        public Builder setPrivacyUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          privacyUrl_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }

        private java.lang.Object permContent_ = "";
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @return Whether the permContent field is set.
         */
        public boolean hasPermContent() {
          return ((bitField0_ & 0x00000020) != 0);
        }
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @return The permContent.
         */
        public java.lang.String getPermContent() {
          java.lang.Object ref = permContent_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            permContent_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @return The bytes for permContent.
         */
        public com.google.protobuf.ByteString
            getPermContentBytes() {
          java.lang.Object ref = permContent_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            permContent_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @param value The permContent to set.
         * @return This builder for chaining.
         */
        public Builder setPermContent(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          permContent_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearPermContent() {
          permContent_ = getDefaultInstance().getPermContent();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用权限列表
         * </pre>
         *
         * <code>optional string permContent = 6;</code>
         * @param value The bytes for permContent to set.
         * @return This builder for chaining.
         */
        public Builder setPermContentBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          permContent_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }

        private java.lang.Object developer_ = "";
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @return Whether the developer field is set.
         */
        public boolean hasDeveloper() {
          return ((bitField0_ & 0x00000040) != 0);
        }
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @return The developer.
         */
        public java.lang.String getDeveloper() {
          java.lang.Object ref = developer_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            developer_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @return The bytes for developer.
         */
        public com.google.protobuf.ByteString
            getDeveloperBytes() {
          java.lang.Object ref = developer_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            developer_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @param value The developer to set.
         * @return This builder for chaining.
         */
        public Builder setDeveloper(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          developer_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearDeveloper() {
          developer_ = getDefaultInstance().getDeveloper();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 应用开发者
         * </pre>
         *
         * <code>optional string developer = 7;</code>
         * @param value The bytes for developer to set.
         * @return This builder for chaining.
         */
        public Builder setDeveloperBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          developer_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:BidResponse.Bid.App)
      }

      // @@protoc_insertion_point(class_scope:BidResponse.Bid.App)
      private static final cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App();
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<App>
          PARSER = new com.google.protobuf.AbstractParser<App>() {
        @java.lang.Override
        public App parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<App> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<App> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface VideoOrBuilder extends
        // @@protoc_insertion_point(interface_extends:BidResponse.Bid.Video)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 视频地址URL
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <pre>
       * 视频地址URL
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();

      /**
       * <pre>
       * 视频时长，单位：秒
       * </pre>
       *
       * <code>sint32 duration = 2;</code>
       * @return The duration.
       */
      int getDuration();

      /**
       * <pre>
       * 视频体积，单位：byte
       * </pre>
       *
       * <code>optional sint64 size = 3;</code>
       * @return Whether the size field is set.
       */
      boolean hasSize();
      /**
       * <pre>
       * 视频体积，单位：byte
       * </pre>
       *
       * <code>optional sint64 size = 3;</code>
       * @return The size.
       */
      long getSize();

      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional sint32 vw = 4;</code>
       * @return Whether the vw field is set.
       */
      boolean hasVw();
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional sint32 vw = 4;</code>
       * @return The vw.
       */
      int getVw();

      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional sint32 vh = 5;</code>
       * @return Whether the vh field is set.
       */
      boolean hasVh();
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional sint32 vh = 5;</code>
       * @return The vh.
       */
      int getVh();

      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @return A list containing the coverImgUrl.
       */
      java.util.List<java.lang.String>
          getCoverImgUrlList();
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @return The count of coverImgUrl.
       */
      int getCoverImgUrlCount();
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @param index The index of the element to return.
       * @return The coverImgUrl at the given index.
       */
      java.lang.String getCoverImgUrl(int index);
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the coverImgUrl at the given index.
       */
      com.google.protobuf.ByteString
          getCoverImgUrlBytes(int index);

      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return Whether the buttonText field is set.
       */
      boolean hasButtonText();
      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return The buttonText.
       */
      java.lang.String getButtonText();
      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return The bytes for buttonText.
       */
      com.google.protobuf.ByteString
          getButtonTextBytes();

      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return Whether the endImgUrl field is set.
       */
      boolean hasEndImgUrl();
      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return The endImgUrl.
       */
      java.lang.String getEndImgUrl();
      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return The bytes for endImgUrl.
       */
      com.google.protobuf.ByteString
          getEndImgUrlBytes();

      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return Whether the endHtml field is set.
       */
      boolean hasEndHtml();
      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return The endHtml.
       */
      java.lang.String getEndHtml();
      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return The bytes for endHtml.
       */
      com.google.protobuf.ByteString
          getEndHtmlBytes();

      /**
       * <pre>
       * 播放多少秒才可以跳过，有值且大于0才处理
       * </pre>
       *
       * <code>optional sint32 skipSec = 10;</code>
       * @return Whether the skipSec field is set.
       */
      boolean hasSkipSec();
      /**
       * <pre>
       * 播放多少秒才可以跳过，有值且大于0才处理
       * </pre>
       *
       * <code>optional sint32 skipSec = 10;</code>
       * @return The skipSec.
       */
      int getSkipSec();

      /**
       * <pre>
       * 视频播放完成是否自动跳 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool comLanding = 11;</code>
       * @return Whether the comLanding field is set.
       */
      boolean hasComLanding();
      /**
       * <pre>
       * 视频播放完成是否自动跳 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool comLanding = 11;</code>
       * @return The comLanding.
       */
      boolean getComLanding();

      /**
       * <pre>
       * 视频播放过程中是否可以点击跳转 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool proLanding = 12;</code>
       * @return Whether the proLanding field is set.
       */
      boolean hasProLanding();
      /**
       * <pre>
       * 视频播放过程中是否可以点击跳转 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool proLanding = 12;</code>
       * @return The proLanding.
       */
      boolean getProLanding();

      /**
       * <pre>
       * 是否需要预先加载广告视频，默认否
       * </pre>
       *
       * <code>optional bool prefetch = 13;</code>
       * @return Whether the prefetch field is set.
       */
      boolean hasPrefetch();
      /**
       * <pre>
       * 是否需要预先加载广告视频，默认否
       * </pre>
       *
       * <code>optional bool prefetch = 13;</code>
       * @return The prefetch.
       */
      boolean getPrefetch();
    }
    /**
     * Protobuf type {@code BidResponse.Bid.Video}
     */
    public static final class Video extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:BidResponse.Bid.Video)
        VideoOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Video.class.getName());
      }
      // Use Video.newBuilder() to construct.
      private Video(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Video() {
        url_ = "";
        coverImgUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        buttonText_ = "";
        endImgUrl_ = "";
        endHtml_ = "";
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Video_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Video_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder.class);
      }

      private int bitField0_;
      public static final int URL_FIELD_NUMBER = 1;
      @SuppressWarnings("serial")
      private volatile java.lang.Object url_ = "";
      /**
       * <pre>
       * 视频地址URL
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 视频地址URL
       * </pre>
       *
       * <code>string url = 1;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DURATION_FIELD_NUMBER = 2;
      private int duration_ = 0;
      /**
       * <pre>
       * 视频时长，单位：秒
       * </pre>
       *
       * <code>sint32 duration = 2;</code>
       * @return The duration.
       */
      @java.lang.Override
      public int getDuration() {
        return duration_;
      }

      public static final int SIZE_FIELD_NUMBER = 3;
      private long size_ = 0L;
      /**
       * <pre>
       * 视频体积，单位：byte
       * </pre>
       *
       * <code>optional sint64 size = 3;</code>
       * @return Whether the size field is set.
       */
      @java.lang.Override
      public boolean hasSize() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 视频体积，单位：byte
       * </pre>
       *
       * <code>optional sint64 size = 3;</code>
       * @return The size.
       */
      @java.lang.Override
      public long getSize() {
        return size_;
      }

      public static final int VW_FIELD_NUMBER = 4;
      private int vw_ = 0;
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional sint32 vw = 4;</code>
       * @return Whether the vw field is set.
       */
      @java.lang.Override
      public boolean hasVw() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 视频宽度
       * </pre>
       *
       * <code>optional sint32 vw = 4;</code>
       * @return The vw.
       */
      @java.lang.Override
      public int getVw() {
        return vw_;
      }

      public static final int VH_FIELD_NUMBER = 5;
      private int vh_ = 0;
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional sint32 vh = 5;</code>
       * @return Whether the vh field is set.
       */
      @java.lang.Override
      public boolean hasVh() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 视频高度
       * </pre>
       *
       * <code>optional sint32 vh = 5;</code>
       * @return The vh.
       */
      @java.lang.Override
      public int getVh() {
        return vh_;
      }

      public static final int COVERIMGURL_FIELD_NUMBER = 6;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList coverImgUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @return A list containing the coverImgUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getCoverImgUrlList() {
        return coverImgUrl_;
      }
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @return The count of coverImgUrl.
       */
      public int getCoverImgUrlCount() {
        return coverImgUrl_.size();
      }
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @param index The index of the element to return.
       * @return The coverImgUrl at the given index.
       */
      public java.lang.String getCoverImgUrl(int index) {
        return coverImgUrl_.get(index);
      }
      /**
       * <pre>
       * 视频封面图链接
       * </pre>
       *
       * <code>repeated string coverImgUrl = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the coverImgUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getCoverImgUrlBytes(int index) {
        return coverImgUrl_.getByteString(index);
      }

      public static final int BUTTONTEXT_FIELD_NUMBER = 7;
      @SuppressWarnings("serial")
      private volatile java.lang.Object buttonText_ = "";
      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return Whether the buttonText field is set.
       */
      @java.lang.Override
      public boolean hasButtonText() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return The buttonText.
       */
      @java.lang.Override
      public java.lang.String getButtonText() {
        java.lang.Object ref = buttonText_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          buttonText_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 视频播放过程中或完成后显示的按钮文章
       * </pre>
       *
       * <code>optional string buttonText = 7;</code>
       * @return The bytes for buttonText.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getButtonTextBytes() {
        java.lang.Object ref = buttonText_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          buttonText_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ENDIMGURL_FIELD_NUMBER = 8;
      @SuppressWarnings("serial")
      private volatile java.lang.Object endImgUrl_ = "";
      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return Whether the endImgUrl field is set.
       */
      @java.lang.Override
      public boolean hasEndImgUrl() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return The endImgUrl.
       */
      @java.lang.Override
      public java.lang.String getEndImgUrl() {
        java.lang.Object ref = endImgUrl_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          endImgUrl_ = s;
          return s;
        }
      }
      /**
       * <code>optional string endImgUrl = 8;</code>
       * @return The bytes for endImgUrl.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getEndImgUrlBytes() {
        java.lang.Object ref = endImgUrl_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          endImgUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ENDHTML_FIELD_NUMBER = 9;
      @SuppressWarnings("serial")
      private volatile java.lang.Object endHtml_ = "";
      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return Whether the endHtml field is set.
       */
      @java.lang.Override
      public boolean hasEndHtml() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return The endHtml.
       */
      @java.lang.Override
      public java.lang.String getEndHtml() {
        java.lang.Object ref = endHtml_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          endHtml_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
       * </pre>
       *
       * <code>optional string endHtml = 9;</code>
       * @return The bytes for endHtml.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getEndHtmlBytes() {
        java.lang.Object ref = endHtml_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          endHtml_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SKIPSEC_FIELD_NUMBER = 10;
      private int skipSec_ = 0;
      /**
       * <pre>
       * 播放多少秒才可以跳过，有值且大于0才处理
       * </pre>
       *
       * <code>optional sint32 skipSec = 10;</code>
       * @return Whether the skipSec field is set.
       */
      @java.lang.Override
      public boolean hasSkipSec() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 播放多少秒才可以跳过，有值且大于0才处理
       * </pre>
       *
       * <code>optional sint32 skipSec = 10;</code>
       * @return The skipSec.
       */
      @java.lang.Override
      public int getSkipSec() {
        return skipSec_;
      }

      public static final int COMLANDING_FIELD_NUMBER = 11;
      private boolean comLanding_ = false;
      /**
       * <pre>
       * 视频播放完成是否自动跳 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool comLanding = 11;</code>
       * @return Whether the comLanding field is set.
       */
      @java.lang.Override
      public boolean hasComLanding() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 视频播放完成是否自动跳 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool comLanding = 11;</code>
       * @return The comLanding.
       */
      @java.lang.Override
      public boolean getComLanding() {
        return comLanding_;
      }

      public static final int PROLANDING_FIELD_NUMBER = 12;
      private boolean proLanding_ = false;
      /**
       * <pre>
       * 视频播放过程中是否可以点击跳转 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool proLanding = 12;</code>
       * @return Whether the proLanding field is set.
       */
      @java.lang.Override
      public boolean hasProLanding() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 视频播放过程中是否可以点击跳转 landingUrl，默认否
       * </pre>
       *
       * <code>optional bool proLanding = 12;</code>
       * @return The proLanding.
       */
      @java.lang.Override
      public boolean getProLanding() {
        return proLanding_;
      }

      public static final int PREFETCH_FIELD_NUMBER = 13;
      private boolean prefetch_ = false;
      /**
       * <pre>
       * 是否需要预先加载广告视频，默认否
       * </pre>
       *
       * <code>optional bool prefetch = 13;</code>
       * @return Whether the prefetch field is set.
       */
      @java.lang.Override
      public boolean hasPrefetch() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 是否需要预先加载广告视频，默认否
       * </pre>
       *
       * <code>optional bool prefetch = 13;</code>
       * @return The prefetch.
       */
      @java.lang.Override
      public boolean getPrefetch() {
        return prefetch_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 1, url_);
        }
        if (duration_ != 0) {
          output.writeSInt32(2, duration_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeSInt64(3, size_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeSInt32(4, vw_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeSInt32(5, vh_);
        }
        for (int i = 0; i < coverImgUrl_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 6, coverImgUrl_.getRaw(i));
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 7, buttonText_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 8, endImgUrl_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          com.google.protobuf.GeneratedMessage.writeString(output, 9, endHtml_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          output.writeSInt32(10, skipSec_);
        }
        if (((bitField0_ & 0x00000080) != 0)) {
          output.writeBool(11, comLanding_);
        }
        if (((bitField0_ & 0x00000100) != 0)) {
          output.writeBool(12, proLanding_);
        }
        if (((bitField0_ & 0x00000200) != 0)) {
          output.writeBool(13, prefetch_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessage.isStringEmpty(url_)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(1, url_);
        }
        if (duration_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(2, duration_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt64Size(3, size_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(4, vw_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(5, vh_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < coverImgUrl_.size(); i++) {
            dataSize += computeStringSizeNoTag(coverImgUrl_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getCoverImgUrlList().size();
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(7, buttonText_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(8, endImgUrl_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          size += com.google.protobuf.GeneratedMessage.computeStringSize(9, endHtml_);
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(10, skipSec_);
        }
        if (((bitField0_ & 0x00000080) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(11, comLanding_);
        }
        if (((bitField0_ & 0x00000100) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(12, proLanding_);
        }
        if (((bitField0_ & 0x00000200) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(13, prefetch_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video other = (cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video) obj;

        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (getDuration()
            != other.getDuration()) return false;
        if (hasSize() != other.hasSize()) return false;
        if (hasSize()) {
          if (getSize()
              != other.getSize()) return false;
        }
        if (hasVw() != other.hasVw()) return false;
        if (hasVw()) {
          if (getVw()
              != other.getVw()) return false;
        }
        if (hasVh() != other.hasVh()) return false;
        if (hasVh()) {
          if (getVh()
              != other.getVh()) return false;
        }
        if (!getCoverImgUrlList()
            .equals(other.getCoverImgUrlList())) return false;
        if (hasButtonText() != other.hasButtonText()) return false;
        if (hasButtonText()) {
          if (!getButtonText()
              .equals(other.getButtonText())) return false;
        }
        if (hasEndImgUrl() != other.hasEndImgUrl()) return false;
        if (hasEndImgUrl()) {
          if (!getEndImgUrl()
              .equals(other.getEndImgUrl())) return false;
        }
        if (hasEndHtml() != other.hasEndHtml()) return false;
        if (hasEndHtml()) {
          if (!getEndHtml()
              .equals(other.getEndHtml())) return false;
        }
        if (hasSkipSec() != other.hasSkipSec()) return false;
        if (hasSkipSec()) {
          if (getSkipSec()
              != other.getSkipSec()) return false;
        }
        if (hasComLanding() != other.hasComLanding()) return false;
        if (hasComLanding()) {
          if (getComLanding()
              != other.getComLanding()) return false;
        }
        if (hasProLanding() != other.hasProLanding()) return false;
        if (hasProLanding()) {
          if (getProLanding()
              != other.getProLanding()) return false;
        }
        if (hasPrefetch() != other.hasPrefetch()) return false;
        if (hasPrefetch()) {
          if (getPrefetch()
              != other.getPrefetch()) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (37 * hash) + DURATION_FIELD_NUMBER;
        hash = (53 * hash) + getDuration();
        if (hasSize()) {
          hash = (37 * hash) + SIZE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getSize());
        }
        if (hasVw()) {
          hash = (37 * hash) + VW_FIELD_NUMBER;
          hash = (53 * hash) + getVw();
        }
        if (hasVh()) {
          hash = (37 * hash) + VH_FIELD_NUMBER;
          hash = (53 * hash) + getVh();
        }
        if (getCoverImgUrlCount() > 0) {
          hash = (37 * hash) + COVERIMGURL_FIELD_NUMBER;
          hash = (53 * hash) + getCoverImgUrlList().hashCode();
        }
        if (hasButtonText()) {
          hash = (37 * hash) + BUTTONTEXT_FIELD_NUMBER;
          hash = (53 * hash) + getButtonText().hashCode();
        }
        if (hasEndImgUrl()) {
          hash = (37 * hash) + ENDIMGURL_FIELD_NUMBER;
          hash = (53 * hash) + getEndImgUrl().hashCode();
        }
        if (hasEndHtml()) {
          hash = (37 * hash) + ENDHTML_FIELD_NUMBER;
          hash = (53 * hash) + getEndHtml().hashCode();
        }
        if (hasSkipSec()) {
          hash = (37 * hash) + SKIPSEC_FIELD_NUMBER;
          hash = (53 * hash) + getSkipSec();
        }
        if (hasComLanding()) {
          hash = (37 * hash) + COMLANDING_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getComLanding());
        }
        if (hasProLanding()) {
          hash = (37 * hash) + PROLANDING_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getProLanding());
        }
        if (hasPrefetch()) {
          hash = (37 * hash) + PREFETCH_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getPrefetch());
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code BidResponse.Bid.Video}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:BidResponse.Bid.Video)
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Video_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Video_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder.class);
        }

        // Construct using cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          url_ = "";
          duration_ = 0;
          size_ = 0L;
          vw_ = 0;
          vh_ = 0;
          coverImgUrl_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          buttonText_ = "";
          endImgUrl_ = "";
          endHtml_ = "";
          skipSec_ = 0;
          comLanding_ = false;
          proLanding_ = false;
          prefetch_ = false;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Video_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getDefaultInstanceForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video build() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video buildPartial() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video result = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.url_ = url_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.duration_ = duration_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.size_ = size_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.vw_ = vw_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.vh_ = vh_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            coverImgUrl_.makeImmutable();
            result.coverImgUrl_ = coverImgUrl_;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.buttonText_ = buttonText_;
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000080) != 0)) {
            result.endImgUrl_ = endImgUrl_;
            to_bitField0_ |= 0x00000010;
          }
          if (((from_bitField0_ & 0x00000100) != 0)) {
            result.endHtml_ = endHtml_;
            to_bitField0_ |= 0x00000020;
          }
          if (((from_bitField0_ & 0x00000200) != 0)) {
            result.skipSec_ = skipSec_;
            to_bitField0_ |= 0x00000040;
          }
          if (((from_bitField0_ & 0x00000400) != 0)) {
            result.comLanding_ = comLanding_;
            to_bitField0_ |= 0x00000080;
          }
          if (((from_bitField0_ & 0x00000800) != 0)) {
            result.proLanding_ = proLanding_;
            to_bitField0_ |= 0x00000100;
          }
          if (((from_bitField0_ & 0x00001000) != 0)) {
            result.prefetch_ = prefetch_;
            to_bitField0_ |= 0x00000200;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video) {
            return mergeFrom((cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video other) {
          if (other == cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance()) return this;
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            bitField0_ |= 0x00000001;
            onChanged();
          }
          if (other.getDuration() != 0) {
            setDuration(other.getDuration());
          }
          if (other.hasSize()) {
            setSize(other.getSize());
          }
          if (other.hasVw()) {
            setVw(other.getVw());
          }
          if (other.hasVh()) {
            setVh(other.getVh());
          }
          if (!other.coverImgUrl_.isEmpty()) {
            if (coverImgUrl_.isEmpty()) {
              coverImgUrl_ = other.coverImgUrl_;
              bitField0_ |= 0x00000020;
            } else {
              ensureCoverImgUrlIsMutable();
              coverImgUrl_.addAll(other.coverImgUrl_);
            }
            onChanged();
          }
          if (other.hasButtonText()) {
            buttonText_ = other.buttonText_;
            bitField0_ |= 0x00000040;
            onChanged();
          }
          if (other.hasEndImgUrl()) {
            endImgUrl_ = other.endImgUrl_;
            bitField0_ |= 0x00000080;
            onChanged();
          }
          if (other.hasEndHtml()) {
            endHtml_ = other.endHtml_;
            bitField0_ |= 0x00000100;
            onChanged();
          }
          if (other.hasSkipSec()) {
            setSkipSec(other.getSkipSec());
          }
          if (other.hasComLanding()) {
            setComLanding(other.getComLanding());
          }
          if (other.hasProLanding()) {
            setProLanding(other.getProLanding());
          }
          if (other.hasPrefetch()) {
            setPrefetch(other.getPrefetch());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  url_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 16: {
                  duration_ = input.readSInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 24: {
                  size_ = input.readSInt64();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 32: {
                  vw_ = input.readSInt32();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 32
                case 40: {
                  vh_ = input.readSInt32();
                  bitField0_ |= 0x00000010;
                  break;
                } // case 40
                case 50: {
                  java.lang.String s = input.readStringRequireUtf8();
                  ensureCoverImgUrlIsMutable();
                  coverImgUrl_.add(s);
                  break;
                } // case 50
                case 58: {
                  buttonText_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 58
                case 66: {
                  endImgUrl_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000080;
                  break;
                } // case 66
                case 74: {
                  endHtml_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000100;
                  break;
                } // case 74
                case 80: {
                  skipSec_ = input.readSInt32();
                  bitField0_ |= 0x00000200;
                  break;
                } // case 80
                case 88: {
                  comLanding_ = input.readBool();
                  bitField0_ |= 0x00000400;
                  break;
                } // case 88
                case 96: {
                  proLanding_ = input.readBool();
                  bitField0_ |= 0x00000800;
                  break;
                } // case 96
                case 104: {
                  prefetch_ = input.readBool();
                  bitField0_ |= 0x00001000;
                  break;
                } // case 104
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.lang.Object url_ = "";
        /**
         * <pre>
         * 视频地址URL
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 视频地址URL
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 视频地址URL
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频地址URL
         * </pre>
         *
         * <code>string url = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {
          url_ = getDefaultInstance().getUrl();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频地址URL
         * </pre>
         *
         * <code>string url = 1;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          url_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }

        private int duration_ ;
        /**
         * <pre>
         * 视频时长，单位：秒
         * </pre>
         *
         * <code>sint32 duration = 2;</code>
         * @return The duration.
         */
        @java.lang.Override
        public int getDuration() {
          return duration_;
        }
        /**
         * <pre>
         * 视频时长，单位：秒
         * </pre>
         *
         * <code>sint32 duration = 2;</code>
         * @param value The duration to set.
         * @return This builder for chaining.
         */
        public Builder setDuration(int value) {

          duration_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频时长，单位：秒
         * </pre>
         *
         * <code>sint32 duration = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearDuration() {
          bitField0_ = (bitField0_ & ~0x00000002);
          duration_ = 0;
          onChanged();
          return this;
        }

        private long size_ ;
        /**
         * <pre>
         * 视频体积，单位：byte
         * </pre>
         *
         * <code>optional sint64 size = 3;</code>
         * @return Whether the size field is set.
         */
        @java.lang.Override
        public boolean hasSize() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <pre>
         * 视频体积，单位：byte
         * </pre>
         *
         * <code>optional sint64 size = 3;</code>
         * @return The size.
         */
        @java.lang.Override
        public long getSize() {
          return size_;
        }
        /**
         * <pre>
         * 视频体积，单位：byte
         * </pre>
         *
         * <code>optional sint64 size = 3;</code>
         * @param value The size to set.
         * @return This builder for chaining.
         */
        public Builder setSize(long value) {

          size_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频体积，单位：byte
         * </pre>
         *
         * <code>optional sint64 size = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearSize() {
          bitField0_ = (bitField0_ & ~0x00000004);
          size_ = 0L;
          onChanged();
          return this;
        }

        private int vw_ ;
        /**
         * <pre>
         * 视频宽度
         * </pre>
         *
         * <code>optional sint32 vw = 4;</code>
         * @return Whether the vw field is set.
         */
        @java.lang.Override
        public boolean hasVw() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <pre>
         * 视频宽度
         * </pre>
         *
         * <code>optional sint32 vw = 4;</code>
         * @return The vw.
         */
        @java.lang.Override
        public int getVw() {
          return vw_;
        }
        /**
         * <pre>
         * 视频宽度
         * </pre>
         *
         * <code>optional sint32 vw = 4;</code>
         * @param value The vw to set.
         * @return This builder for chaining.
         */
        public Builder setVw(int value) {

          vw_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频宽度
         * </pre>
         *
         * <code>optional sint32 vw = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearVw() {
          bitField0_ = (bitField0_ & ~0x00000008);
          vw_ = 0;
          onChanged();
          return this;
        }

        private int vh_ ;
        /**
         * <pre>
         * 视频高度
         * </pre>
         *
         * <code>optional sint32 vh = 5;</code>
         * @return Whether the vh field is set.
         */
        @java.lang.Override
        public boolean hasVh() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <pre>
         * 视频高度
         * </pre>
         *
         * <code>optional sint32 vh = 5;</code>
         * @return The vh.
         */
        @java.lang.Override
        public int getVh() {
          return vh_;
        }
        /**
         * <pre>
         * 视频高度
         * </pre>
         *
         * <code>optional sint32 vh = 5;</code>
         * @param value The vh to set.
         * @return This builder for chaining.
         */
        public Builder setVh(int value) {

          vh_ = value;
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频高度
         * </pre>
         *
         * <code>optional sint32 vh = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearVh() {
          bitField0_ = (bitField0_ & ~0x00000010);
          vh_ = 0;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList coverImgUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureCoverImgUrlIsMutable() {
          if (!coverImgUrl_.isModifiable()) {
            coverImgUrl_ = new com.google.protobuf.LazyStringArrayList(coverImgUrl_);
          }
          bitField0_ |= 0x00000020;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @return A list containing the coverImgUrl.
         */
        public com.google.protobuf.ProtocolStringList
            getCoverImgUrlList() {
          coverImgUrl_.makeImmutable();
          return coverImgUrl_;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @return The count of coverImgUrl.
         */
        public int getCoverImgUrlCount() {
          return coverImgUrl_.size();
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param index The index of the element to return.
         * @return The coverImgUrl at the given index.
         */
        public java.lang.String getCoverImgUrl(int index) {
          return coverImgUrl_.get(index);
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param index The index of the value to return.
         * @return The bytes of the coverImgUrl at the given index.
         */
        public com.google.protobuf.ByteString
            getCoverImgUrlBytes(int index) {
          return coverImgUrl_.getByteString(index);
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param index The index to set the value at.
         * @param value The coverImgUrl to set.
         * @return This builder for chaining.
         */
        public Builder setCoverImgUrl(
            int index, java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureCoverImgUrlIsMutable();
          coverImgUrl_.set(index, value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param value The coverImgUrl to add.
         * @return This builder for chaining.
         */
        public Builder addCoverImgUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureCoverImgUrlIsMutable();
          coverImgUrl_.add(value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param values The coverImgUrl to add.
         * @return This builder for chaining.
         */
        public Builder addAllCoverImgUrl(
            java.lang.Iterable<java.lang.String> values) {
          ensureCoverImgUrlIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, coverImgUrl_);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearCoverImgUrl() {
          coverImgUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频封面图链接
         * </pre>
         *
         * <code>repeated string coverImgUrl = 6;</code>
         * @param value The bytes of the coverImgUrl to add.
         * @return This builder for chaining.
         */
        public Builder addCoverImgUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureCoverImgUrlIsMutable();
          coverImgUrl_.add(value);
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }

        private java.lang.Object buttonText_ = "";
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @return Whether the buttonText field is set.
         */
        public boolean hasButtonText() {
          return ((bitField0_ & 0x00000040) != 0);
        }
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @return The buttonText.
         */
        public java.lang.String getButtonText() {
          java.lang.Object ref = buttonText_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            buttonText_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @return The bytes for buttonText.
         */
        public com.google.protobuf.ByteString
            getButtonTextBytes() {
          java.lang.Object ref = buttonText_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            buttonText_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @param value The buttonText to set.
         * @return This builder for chaining.
         */
        public Builder setButtonText(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          buttonText_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearButtonText() {
          buttonText_ = getDefaultInstance().getButtonText();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放过程中或完成后显示的按钮文章
         * </pre>
         *
         * <code>optional string buttonText = 7;</code>
         * @param value The bytes for buttonText to set.
         * @return This builder for chaining.
         */
        public Builder setButtonTextBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          buttonText_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }

        private java.lang.Object endImgUrl_ = "";
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @return Whether the endImgUrl field is set.
         */
        public boolean hasEndImgUrl() {
          return ((bitField0_ & 0x00000080) != 0);
        }
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @return The endImgUrl.
         */
        public java.lang.String getEndImgUrl() {
          java.lang.Object ref = endImgUrl_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            endImgUrl_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @return The bytes for endImgUrl.
         */
        public com.google.protobuf.ByteString
            getEndImgUrlBytes() {
          java.lang.Object ref = endImgUrl_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            endImgUrl_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @param value The endImgUrl to set.
         * @return This builder for chaining.
         */
        public Builder setEndImgUrl(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          endImgUrl_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearEndImgUrl() {
          endImgUrl_ = getDefaultInstance().getEndImgUrl();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
          return this;
        }
        /**
         * <code>optional string endImgUrl = 8;</code>
         * @param value The bytes for endImgUrl to set.
         * @return This builder for chaining.
         */
        public Builder setEndImgUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          endImgUrl_ = value;
          bitField0_ |= 0x00000080;
          onChanged();
          return this;
        }

        private java.lang.Object endHtml_ = "";
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @return Whether the endHtml field is set.
         */
        public boolean hasEndHtml() {
          return ((bitField0_ & 0x00000100) != 0);
        }
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @return The endHtml.
         */
        public java.lang.String getEndHtml() {
          java.lang.Object ref = endHtml_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            endHtml_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @return The bytes for endHtml.
         */
        public com.google.protobuf.ByteString
            getEndHtmlBytes() {
          java.lang.Object ref = endHtml_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            endHtml_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @param value The endHtml to set.
         * @return This builder for chaining.
         */
        public Builder setEndHtml(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          endHtml_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @return This builder for chaining.
         */
        public Builder clearEndHtml() {
          endHtml_ = getDefaultInstance().getEndHtml();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放完成展示HTML(页面)，endImgUrl与endHtml，选择其中一个有值的进行展示；
         * </pre>
         *
         * <code>optional string endHtml = 9;</code>
         * @param value The bytes for endHtml to set.
         * @return This builder for chaining.
         */
        public Builder setEndHtmlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          endHtml_ = value;
          bitField0_ |= 0x00000100;
          onChanged();
          return this;
        }

        private int skipSec_ ;
        /**
         * <pre>
         * 播放多少秒才可以跳过，有值且大于0才处理
         * </pre>
         *
         * <code>optional sint32 skipSec = 10;</code>
         * @return Whether the skipSec field is set.
         */
        @java.lang.Override
        public boolean hasSkipSec() {
          return ((bitField0_ & 0x00000200) != 0);
        }
        /**
         * <pre>
         * 播放多少秒才可以跳过，有值且大于0才处理
         * </pre>
         *
         * <code>optional sint32 skipSec = 10;</code>
         * @return The skipSec.
         */
        @java.lang.Override
        public int getSkipSec() {
          return skipSec_;
        }
        /**
         * <pre>
         * 播放多少秒才可以跳过，有值且大于0才处理
         * </pre>
         *
         * <code>optional sint32 skipSec = 10;</code>
         * @param value The skipSec to set.
         * @return This builder for chaining.
         */
        public Builder setSkipSec(int value) {

          skipSec_ = value;
          bitField0_ |= 0x00000200;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 播放多少秒才可以跳过，有值且大于0才处理
         * </pre>
         *
         * <code>optional sint32 skipSec = 10;</code>
         * @return This builder for chaining.
         */
        public Builder clearSkipSec() {
          bitField0_ = (bitField0_ & ~0x00000200);
          skipSec_ = 0;
          onChanged();
          return this;
        }

        private boolean comLanding_ ;
        /**
         * <pre>
         * 视频播放完成是否自动跳 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool comLanding = 11;</code>
         * @return Whether the comLanding field is set.
         */
        @java.lang.Override
        public boolean hasComLanding() {
          return ((bitField0_ & 0x00000400) != 0);
        }
        /**
         * <pre>
         * 视频播放完成是否自动跳 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool comLanding = 11;</code>
         * @return The comLanding.
         */
        @java.lang.Override
        public boolean getComLanding() {
          return comLanding_;
        }
        /**
         * <pre>
         * 视频播放完成是否自动跳 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool comLanding = 11;</code>
         * @param value The comLanding to set.
         * @return This builder for chaining.
         */
        public Builder setComLanding(boolean value) {

          comLanding_ = value;
          bitField0_ |= 0x00000400;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放完成是否自动跳 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool comLanding = 11;</code>
         * @return This builder for chaining.
         */
        public Builder clearComLanding() {
          bitField0_ = (bitField0_ & ~0x00000400);
          comLanding_ = false;
          onChanged();
          return this;
        }

        private boolean proLanding_ ;
        /**
         * <pre>
         * 视频播放过程中是否可以点击跳转 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool proLanding = 12;</code>
         * @return Whether the proLanding field is set.
         */
        @java.lang.Override
        public boolean hasProLanding() {
          return ((bitField0_ & 0x00000800) != 0);
        }
        /**
         * <pre>
         * 视频播放过程中是否可以点击跳转 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool proLanding = 12;</code>
         * @return The proLanding.
         */
        @java.lang.Override
        public boolean getProLanding() {
          return proLanding_;
        }
        /**
         * <pre>
         * 视频播放过程中是否可以点击跳转 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool proLanding = 12;</code>
         * @param value The proLanding to set.
         * @return This builder for chaining.
         */
        public Builder setProLanding(boolean value) {

          proLanding_ = value;
          bitField0_ |= 0x00000800;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 视频播放过程中是否可以点击跳转 landingUrl，默认否
         * </pre>
         *
         * <code>optional bool proLanding = 12;</code>
         * @return This builder for chaining.
         */
        public Builder clearProLanding() {
          bitField0_ = (bitField0_ & ~0x00000800);
          proLanding_ = false;
          onChanged();
          return this;
        }

        private boolean prefetch_ ;
        /**
         * <pre>
         * 是否需要预先加载广告视频，默认否
         * </pre>
         *
         * <code>optional bool prefetch = 13;</code>
         * @return Whether the prefetch field is set.
         */
        @java.lang.Override
        public boolean hasPrefetch() {
          return ((bitField0_ & 0x00001000) != 0);
        }
        /**
         * <pre>
         * 是否需要预先加载广告视频，默认否
         * </pre>
         *
         * <code>optional bool prefetch = 13;</code>
         * @return The prefetch.
         */
        @java.lang.Override
        public boolean getPrefetch() {
          return prefetch_;
        }
        /**
         * <pre>
         * 是否需要预先加载广告视频，默认否
         * </pre>
         *
         * <code>optional bool prefetch = 13;</code>
         * @param value The prefetch to set.
         * @return This builder for chaining.
         */
        public Builder setPrefetch(boolean value) {

          prefetch_ = value;
          bitField0_ |= 0x00001000;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 是否需要预先加载广告视频，默认否
         * </pre>
         *
         * <code>optional bool prefetch = 13;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrefetch() {
          bitField0_ = (bitField0_ & ~0x00001000);
          prefetch_ = false;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:BidResponse.Bid.Video)
      }

      // @@protoc_insertion_point(class_scope:BidResponse.Bid.Video)
      private static final cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video();
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Video>
          PARSER = new com.google.protobuf.AbstractParser<Video>() {
        @java.lang.Override
        public Video parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Video> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Video> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface TrackerOrBuilder extends
        // @@protoc_insertion_point(interface_extends:BidResponse.Bid.Tracker)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 监测上报类型, 参考 Tracker 对象 type 对应关系表
       * </pre>
       *
       * <code>sint32 type = 1;</code>
       * @return The type.
       */
      int getType();

      /**
       * <code>repeated string urls = 2;</code>
       * @return A list containing the urls.
       */
      java.util.List<java.lang.String>
          getUrlsList();
      /**
       * <code>repeated string urls = 2;</code>
       * @return The count of urls.
       */
      int getUrlsCount();
      /**
       * <code>repeated string urls = 2;</code>
       * @param index The index of the element to return.
       * @return The urls at the given index.
       */
      java.lang.String getUrls(int index);
      /**
       * <code>repeated string urls = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the urls at the given index.
       */
      com.google.protobuf.ByteString
          getUrlsBytes(int index);
    }
    /**
     * Protobuf type {@code BidResponse.Bid.Tracker}
     */
    public static final class Tracker extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:BidResponse.Bid.Tracker)
        TrackerOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 28,
          /* patch= */ 3,
          /* suffix= */ "",
          Tracker.class.getName());
      }
      // Use Tracker.newBuilder() to construct.
      private Tracker(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Tracker() {
        urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Tracker_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Tracker_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder.class);
      }

      public static final int TYPE_FIELD_NUMBER = 1;
      private int type_ = 0;
      /**
       * <pre>
       * 监测上报类型, 参考 Tracker 对象 type 对应关系表
       * </pre>
       *
       * <code>sint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }

      public static final int URLS_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private com.google.protobuf.LazyStringArrayList urls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      /**
       * <code>repeated string urls = 2;</code>
       * @return A list containing the urls.
       */
      public com.google.protobuf.ProtocolStringList
          getUrlsList() {
        return urls_;
      }
      /**
       * <code>repeated string urls = 2;</code>
       * @return The count of urls.
       */
      public int getUrlsCount() {
        return urls_.size();
      }
      /**
       * <code>repeated string urls = 2;</code>
       * @param index The index of the element to return.
       * @return The urls at the given index.
       */
      public java.lang.String getUrls(int index) {
        return urls_.get(index);
      }
      /**
       * <code>repeated string urls = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the urls at the given index.
       */
      public com.google.protobuf.ByteString
          getUrlsBytes(int index) {
        return urls_.getByteString(index);
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (type_ != 0) {
          output.writeSInt32(1, type_);
        }
        for (int i = 0; i < urls_.size(); i++) {
          com.google.protobuf.GeneratedMessage.writeString(output, 2, urls_.getRaw(i));
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (type_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeSInt32Size(1, type_);
        }
        {
          int dataSize = 0;
          for (int i = 0; i < urls_.size(); i++) {
            dataSize += computeStringSizeNoTag(urls_.getRaw(i));
          }
          size += dataSize;
          size += 1 * getUrlsList().size();
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker)) {
          return super.equals(obj);
        }
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker other = (cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker) obj;

        if (getType()
            != other.getType()) return false;
        if (!getUrlsList()
            .equals(other.getUrlsList())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType();
        if (getUrlsCount() > 0) {
          hash = (37 * hash) + URLS_FIELD_NUMBER;
          hash = (53 * hash) + getUrlsList().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code BidResponse.Bid.Tracker}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:BidResponse.Bid.Tracker)
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Tracker_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Tracker_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder.class);
        }

        // Construct using cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          type_ = 0;
          urls_ =
              com.google.protobuf.LazyStringArrayList.emptyList();
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_Tracker_descriptor;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getDefaultInstanceForType() {
          return cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.getDefaultInstance();
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker build() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker buildPartial() {
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker result = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.type_ = type_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            urls_.makeImmutable();
            result.urls_ = urls_;
          }
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker) {
            return mergeFrom((cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker other) {
          if (other == cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.getDefaultInstance()) return this;
          if (other.getType() != 0) {
            setType(other.getType());
          }
          if (!other.urls_.isEmpty()) {
            if (urls_.isEmpty()) {
              urls_ = other.urls_;
              bitField0_ |= 0x00000002;
            } else {
              ensureUrlsIsMutable();
              urls_.addAll(other.urls_);
            }
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  type_ = input.readSInt32();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 18: {
                  java.lang.String s = input.readStringRequireUtf8();
                  ensureUrlsIsMutable();
                  urls_.add(s);
                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private int type_ ;
        /**
         * <pre>
         * 监测上报类型, 参考 Tracker 对象 type 对应关系表
         * </pre>
         *
         * <code>sint32 type = 1;</code>
         * @return The type.
         */
        @java.lang.Override
        public int getType() {
          return type_;
        }
        /**
         * <pre>
         * 监测上报类型, 参考 Tracker 对象 type 对应关系表
         * </pre>
         *
         * <code>sint32 type = 1;</code>
         * @param value The type to set.
         * @return This builder for chaining.
         */
        public Builder setType(int value) {

          type_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 监测上报类型, 参考 Tracker 对象 type 对应关系表
         * </pre>
         *
         * <code>sint32 type = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearType() {
          bitField0_ = (bitField0_ & ~0x00000001);
          type_ = 0;
          onChanged();
          return this;
        }

        private com.google.protobuf.LazyStringArrayList urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        private void ensureUrlsIsMutable() {
          if (!urls_.isModifiable()) {
            urls_ = new com.google.protobuf.LazyStringArrayList(urls_);
          }
          bitField0_ |= 0x00000002;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @return A list containing the urls.
         */
        public com.google.protobuf.ProtocolStringList
            getUrlsList() {
          urls_.makeImmutable();
          return urls_;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @return The count of urls.
         */
        public int getUrlsCount() {
          return urls_.size();
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the element to return.
         * @return The urls at the given index.
         */
        public java.lang.String getUrls(int index) {
          return urls_.get(index);
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index of the value to return.
         * @return The bytes of the urls at the given index.
         */
        public com.google.protobuf.ByteString
            getUrlsBytes(int index) {
          return urls_.getByteString(index);
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param index The index to set the value at.
         * @param value The urls to set.
         * @return This builder for chaining.
         */
        public Builder setUrls(
            int index, java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureUrlsIsMutable();
          urls_.set(index, value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param value The urls to add.
         * @return This builder for chaining.
         */
        public Builder addUrls(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          ensureUrlsIsMutable();
          urls_.add(value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param values The urls to add.
         * @return This builder for chaining.
         */
        public Builder addAllUrls(
            java.lang.Iterable<java.lang.String> values) {
          ensureUrlsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, urls_);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrls() {
          urls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);;
          onChanged();
          return this;
        }
        /**
         * <code>repeated string urls = 2;</code>
         * @param value The bytes of the urls to add.
         * @return This builder for chaining.
         */
        public Builder addUrlsBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          ensureUrlsIsMutable();
          urls_.add(value);
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:BidResponse.Bid.Tracker)
      }

      // @@protoc_insertion_point(class_scope:BidResponse.Bid.Tracker)
      private static final cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker();
      }

      public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Tracker>
          PARSER = new com.google.protobuf.AbstractParser<Tracker>() {
        @java.lang.Override
        public Tracker parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Tracker> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Tracker> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int BIDID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object bidId_ = "";
    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return Whether the bidId field is set.
     */
    @java.lang.Override
    public boolean hasBidId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return The bidId.
     */
    @java.lang.Override
    public java.lang.String getBidId() {
      java.lang.Object ref = bidId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bidId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 本次竞价唯一ID(竞价模式)
     * </pre>
     *
     * <code>optional string bidId = 1;</code>
     * @return The bytes for bidId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBidIdBytes() {
      java.lang.Object ref = bidId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bidId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TAGID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object tagId_ = "";
    /**
     * <pre>
     * 广告位ID
     * </pre>
     *
     * <code>string tagId = 2;</code>
     * @return The tagId.
     */
    @java.lang.Override
    public java.lang.String getTagId() {
      java.lang.Object ref = tagId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tagId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告位ID
     * </pre>
     *
     * <code>string tagId = 2;</code>
     * @return The bytes for tagId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTagIdBytes() {
      java.lang.Object ref = tagId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tagId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object title_ = "";
    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告标题
     * </pre>
     *
     * <code>optional string title = 3;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DESC_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object desc_ = "";
    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return Whether the desc field is set.
     */
    @java.lang.Override
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return The desc.
     */
    @java.lang.Override
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告描述
     * </pre>
     *
     * <code>optional string desc = 4;</code>
     * @return The bytes for desc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ICONURL_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iconUrl_ = "";
    /**
     * <pre>
     * 广告图标icon链接
     * </pre>
     *
     * <code>string iconUrl = 5;</code>
     * @return The iconUrl.
     */
    @java.lang.Override
    public java.lang.String getIconUrl() {
      java.lang.Object ref = iconUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        iconUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 广告图标icon链接
     * </pre>
     *
     * <code>string iconUrl = 5;</code>
     * @return The bytes for iconUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIconUrlBytes() {
      java.lang.Object ref = iconUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iconUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IMGURLS_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList imgUrls_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @return A list containing the imgUrls.
     */
    public com.google.protobuf.ProtocolStringList
        getImgUrlsList() {
      return imgUrls_;
    }
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @return The count of imgUrls.
     */
    public int getImgUrlsCount() {
      return imgUrls_.size();
    }
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @param index The index of the element to return.
     * @return The imgUrls at the given index.
     */
    public java.lang.String getImgUrls(int index) {
      return imgUrls_.get(index);
    }
    /**
     * <pre>
     * 广告图片链接，单个广告可能多个图片地址
     * </pre>
     *
     * <code>repeated string imgUrls = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imgUrls at the given index.
     */
    public com.google.protobuf.ByteString
        getImgUrlsBytes(int index) {
      return imgUrls_.getByteString(index);
    }

    public static final int W_FIELD_NUMBER = 7;
    private int w_ = 0;
    /**
     * <pre>
     * 广告宽度，可能为空
     * </pre>
     *
     * <code>optional sint32 w = 7;</code>
     * @return Whether the w field is set.
     */
    @java.lang.Override
    public boolean hasW() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 广告宽度，可能为空
     * </pre>
     *
     * <code>optional sint32 w = 7;</code>
     * @return The w.
     */
    @java.lang.Override
    public int getW() {
      return w_;
    }

    public static final int H_FIELD_NUMBER = 8;
    private int h_ = 0;
    /**
     * <pre>
     * 广告高度，可能为空
     * </pre>
     *
     * <code>optional sint32 h = 8;</code>
     * @return Whether the h field is set.
     */
    @java.lang.Override
    public boolean hasH() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 广告高度，可能为空
     * </pre>
     *
     * <code>optional sint32 h = 8;</code>
     * @return The h.
     */
    @java.lang.Override
    public int getH() {
      return h_;
    }

    public static final int LANDINGURL_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object landingUrl_ = "";
    /**
     * <pre>
     * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
     * </pre>
     *
     * <code>string landingUrl = 9;</code>
     * @return The landingUrl.
     */
    @java.lang.Override
    public java.lang.String getLandingUrl() {
      java.lang.Object ref = landingUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        landingUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
     * </pre>
     *
     * <code>string landingUrl = 9;</code>
     * @return The bytes for landingUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLandingUrlBytes() {
      java.lang.Object ref = landingUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        landingUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEEPLINK_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deeplink_ = "";
    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return Whether the deeplink field is set.
     */
    @java.lang.Override
    public boolean hasDeeplink() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return The deeplink.
     */
    @java.lang.Override
    public java.lang.String getDeeplink() {
      java.lang.Object ref = deeplink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deeplink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
     * </pre>
     *
     * <code>optional string deeplink = 10;</code>
     * @return The bytes for deeplink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeeplinkBytes() {
      java.lang.Object ref = deeplink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deeplink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DOWNLOADURL_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object downloadUrl_ = "";
    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return Whether the downloadUrl field is set.
     */
    @java.lang.Override
    public boolean hasDownloadUrl() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return The downloadUrl.
     */
    @java.lang.Override
    public java.lang.String getDownloadUrl() {
      java.lang.Object ref = downloadUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        downloadUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * APP应用下载链接
     * </pre>
     *
     * <code>optional string downloadUrl = 11;</code>
     * @return The bytes for downloadUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDownloadUrlBytes() {
      java.lang.Object ref = downloadUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        downloadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BIDFLOOR_FIELD_NUMBER = 12;
    private int bidFloor_ = 0;
    /**
     * <pre>
     * 竞价广告出价，单位: 分/cpm(竞价模式)
     * </pre>
     *
     * <code>optional sint32 bidFloor = 12;</code>
     * @return Whether the bidFloor field is set.
     */
    @java.lang.Override
    public boolean hasBidFloor() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 竞价广告出价，单位: 分/cpm(竞价模式)
     * </pre>
     *
     * <code>optional sint32 bidFloor = 12;</code>
     * @return The bidFloor.
     */
    @java.lang.Override
    public int getBidFloor() {
      return bidFloor_;
    }

    public static final int WINURLS_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList winUrls_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @return A list containing the winUrls.
     */
    public com.google.protobuf.ProtocolStringList
        getWinUrlsList() {
      return winUrls_;
    }
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @return The count of winUrls.
     */
    public int getWinUrlsCount() {
      return winUrls_.size();
    }
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @param index The index of the element to return.
     * @return The winUrls at the given index.
     */
    public java.lang.String getWinUrls(int index) {
      return winUrls_.get(index);
    }
    /**
     * <pre>
     * 竞价成功上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string winUrls = 13;</code>
     * @param index The index of the value to return.
     * @return The bytes of the winUrls at the given index.
     */
    public com.google.protobuf.ByteString
        getWinUrlsBytes(int index) {
      return winUrls_.getByteString(index);
    }

    public static final int LOSEURLS_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList loseUrls_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @return A list containing the loseUrls.
     */
    public com.google.protobuf.ProtocolStringList
        getLoseUrlsList() {
      return loseUrls_;
    }
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @return The count of loseUrls.
     */
    public int getLoseUrlsCount() {
      return loseUrls_.size();
    }
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @param index The index of the element to return.
     * @return The loseUrls at the given index.
     */
    public java.lang.String getLoseUrls(int index) {
      return loseUrls_.get(index);
    }
    /**
     * <pre>
     * 竞价失败上报地址，可能有多条(竞价模式)
     * </pre>
     *
     * <code>repeated string loseUrls = 14;</code>
     * @param index The index of the value to return.
     * @return The bytes of the loseUrls at the given index.
     */
    public com.google.protobuf.ByteString
        getLoseUrlsBytes(int index) {
      return loseUrls_.getByteString(index);
    }

    public static final int CTYPE_FIELD_NUMBER = 15;
    private int cType_ = 0;
    /**
     * <pre>
     * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
     * </pre>
     *
     * <code>optional sint32 cType = 15;</code>
     * @return Whether the cType field is set.
     */
    @java.lang.Override
    public boolean hasCType() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
     * </pre>
     *
     * <code>optional sint32 cType = 15;</code>
     * @return The cType.
     */
    @java.lang.Override
    public int getCType() {
      return cType_;
    }

    public static final int CITYPE_FIELD_NUMBER = 16;
    private int ciType_ = 0;
    /**
     * <pre>
     * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
     * </pre>
     *
     * <code>optional sint32 ciType = 16;</code>
     * @return Whether the ciType field is set.
     */
    @java.lang.Override
    public boolean hasCiType() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
     * </pre>
     *
     * <code>optional sint32 ciType = 16;</code>
     * @return The ciType.
     */
    @java.lang.Override
    public int getCiType() {
      return ciType_;
    }

    public static final int APP_FIELD_NUMBER = 17;
    private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App app_;
    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     * @return Whether the app field is set.
     */
    @java.lang.Override
    public boolean hasApp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     * @return The app.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getApp() {
      return app_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance() : app_;
    }
    /**
     * <pre>
     * APP应用下载信息
     * </pre>
     *
     * <code>optional .BidResponse.Bid.App app = 17;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder getAppOrBuilder() {
      return app_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance() : app_;
    }

    public static final int VIDEO_FIELD_NUMBER = 18;
    private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video video_;
    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     * @return Whether the video field is set.
     */
    @java.lang.Override
    public boolean hasVideo() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     * @return The video.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getVideo() {
      return video_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance() : video_;
    }
    /**
     * <pre>
     * 视频广告素材
     * </pre>
     *
     * <code>optional .BidResponse.Bid.Video video = 18;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder getVideoOrBuilder() {
      return video_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance() : video_;
    }

    public static final int TRACKERS_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> trackers_;
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    @java.lang.Override
    public java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> getTrackersList() {
      return trackers_;
    }
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    @java.lang.Override
    public java.util.List<? extends cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder> 
        getTrackersOrBuilderList() {
      return trackers_;
    }
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    @java.lang.Override
    public int getTrackersCount() {
      return trackers_.size();
    }
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getTrackers(int index) {
      return trackers_.get(index);
    }
    /**
     * <pre>
     * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
     * </pre>
     *
     * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder getTrackersOrBuilder(
        int index) {
      return trackers_.get(index);
    }

    public static final int UNIVERSALLINK_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object universalLink_ = "";
    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return Whether the universalLink field is set.
     */
    @java.lang.Override
    public boolean hasUniversalLink() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return The universalLink.
     */
    @java.lang.Override
    public java.lang.String getUniversalLink() {
      java.lang.Object ref = universalLink_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        universalLink_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * app 端 iOS DP调起链接，优先级高于deeplink；
     * </pre>
     *
     * <code>optional string universalLink = 20;</code>
     * @return The bytes for universalLink.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniversalLinkBytes() {
      java.lang.Object ref = universalLink_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        universalLink_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLICKAREAREPORTURLS_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList clickAreaReportUrls_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @return A list containing the clickAreaReportUrls.
     */
    public com.google.protobuf.ProtocolStringList
        getClickAreaReportUrlsList() {
      return clickAreaReportUrls_;
    }
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @return The count of clickAreaReportUrls.
     */
    public int getClickAreaReportUrlsCount() {
      return clickAreaReportUrls_.size();
    }
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @param index The index of the element to return.
     * @return The clickAreaReportUrls at the given index.
     */
    public java.lang.String getClickAreaReportUrls(int index) {
      return clickAreaReportUrls_.get(index);
    }
    /**
     * <pre>
     * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
     * </pre>
     *
     * <code>repeated string clickAreaReportUrls = 21;</code>
     * @param index The index of the value to return.
     * @return The bytes of the clickAreaReportUrls at the given index.
     */
    public com.google.protobuf.ByteString
        getClickAreaReportUrlsBytes(int index) {
      return clickAreaReportUrls_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, bidId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(tagId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, tagId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, title_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, desc_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(iconUrl_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, iconUrl_);
      }
      for (int i = 0; i < imgUrls_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, imgUrls_.getRaw(i));
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeSInt32(7, w_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeSInt32(8, h_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(landingUrl_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, landingUrl_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, deeplink_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, downloadUrl_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeSInt32(12, bidFloor_);
      }
      for (int i = 0; i < winUrls_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, winUrls_.getRaw(i));
      }
      for (int i = 0; i < loseUrls_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 14, loseUrls_.getRaw(i));
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeSInt32(15, cType_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeSInt32(16, ciType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(17, getApp());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeMessage(18, getVideo());
      }
      for (int i = 0; i < trackers_.size(); i++) {
        output.writeMessage(19, trackers_.get(i));
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, universalLink_);
      }
      for (int i = 0; i < clickAreaReportUrls_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, clickAreaReportUrls_.getRaw(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, bidId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(tagId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, tagId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, title_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, desc_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(iconUrl_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, iconUrl_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < imgUrls_.size(); i++) {
          dataSize += computeStringSizeNoTag(imgUrls_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getImgUrlsList().size();
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(7, w_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(8, h_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(landingUrl_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, landingUrl_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, deeplink_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, downloadUrl_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(12, bidFloor_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < winUrls_.size(); i++) {
          dataSize += computeStringSizeNoTag(winUrls_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getWinUrlsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < loseUrls_.size(); i++) {
          dataSize += computeStringSizeNoTag(loseUrls_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getLoseUrlsList().size();
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(15, cType_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt32Size(16, ciType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, getApp());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, getVideo());
      }
      for (int i = 0; i < trackers_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, trackers_.get(i));
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, universalLink_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < clickAreaReportUrls_.size(); i++) {
          dataSize += computeStringSizeNoTag(clickAreaReportUrls_.getRaw(i));
        }
        size += dataSize;
        size += 2 * getClickAreaReportUrlsList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid)) {
        return super.equals(obj);
      }
      cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid other = (cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid) obj;

      if (hasBidId() != other.hasBidId()) return false;
      if (hasBidId()) {
        if (!getBidId()
            .equals(other.getBidId())) return false;
      }
      if (!getTagId()
          .equals(other.getTagId())) return false;
      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (!getTitle()
            .equals(other.getTitle())) return false;
      }
      if (hasDesc() != other.hasDesc()) return false;
      if (hasDesc()) {
        if (!getDesc()
            .equals(other.getDesc())) return false;
      }
      if (!getIconUrl()
          .equals(other.getIconUrl())) return false;
      if (!getImgUrlsList()
          .equals(other.getImgUrlsList())) return false;
      if (hasW() != other.hasW()) return false;
      if (hasW()) {
        if (getW()
            != other.getW()) return false;
      }
      if (hasH() != other.hasH()) return false;
      if (hasH()) {
        if (getH()
            != other.getH()) return false;
      }
      if (!getLandingUrl()
          .equals(other.getLandingUrl())) return false;
      if (hasDeeplink() != other.hasDeeplink()) return false;
      if (hasDeeplink()) {
        if (!getDeeplink()
            .equals(other.getDeeplink())) return false;
      }
      if (hasDownloadUrl() != other.hasDownloadUrl()) return false;
      if (hasDownloadUrl()) {
        if (!getDownloadUrl()
            .equals(other.getDownloadUrl())) return false;
      }
      if (hasBidFloor() != other.hasBidFloor()) return false;
      if (hasBidFloor()) {
        if (getBidFloor()
            != other.getBidFloor()) return false;
      }
      if (!getWinUrlsList()
          .equals(other.getWinUrlsList())) return false;
      if (!getLoseUrlsList()
          .equals(other.getLoseUrlsList())) return false;
      if (hasCType() != other.hasCType()) return false;
      if (hasCType()) {
        if (getCType()
            != other.getCType()) return false;
      }
      if (hasCiType() != other.hasCiType()) return false;
      if (hasCiType()) {
        if (getCiType()
            != other.getCiType()) return false;
      }
      if (hasApp() != other.hasApp()) return false;
      if (hasApp()) {
        if (!getApp()
            .equals(other.getApp())) return false;
      }
      if (hasVideo() != other.hasVideo()) return false;
      if (hasVideo()) {
        if (!getVideo()
            .equals(other.getVideo())) return false;
      }
      if (!getTrackersList()
          .equals(other.getTrackersList())) return false;
      if (hasUniversalLink() != other.hasUniversalLink()) return false;
      if (hasUniversalLink()) {
        if (!getUniversalLink()
            .equals(other.getUniversalLink())) return false;
      }
      if (!getClickAreaReportUrlsList()
          .equals(other.getClickAreaReportUrlsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBidId()) {
        hash = (37 * hash) + BIDID_FIELD_NUMBER;
        hash = (53 * hash) + getBidId().hashCode();
      }
      hash = (37 * hash) + TAGID_FIELD_NUMBER;
      hash = (53 * hash) + getTagId().hashCode();
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle().hashCode();
      }
      if (hasDesc()) {
        hash = (37 * hash) + DESC_FIELD_NUMBER;
        hash = (53 * hash) + getDesc().hashCode();
      }
      hash = (37 * hash) + ICONURL_FIELD_NUMBER;
      hash = (53 * hash) + getIconUrl().hashCode();
      if (getImgUrlsCount() > 0) {
        hash = (37 * hash) + IMGURLS_FIELD_NUMBER;
        hash = (53 * hash) + getImgUrlsList().hashCode();
      }
      if (hasW()) {
        hash = (37 * hash) + W_FIELD_NUMBER;
        hash = (53 * hash) + getW();
      }
      if (hasH()) {
        hash = (37 * hash) + H_FIELD_NUMBER;
        hash = (53 * hash) + getH();
      }
      hash = (37 * hash) + LANDINGURL_FIELD_NUMBER;
      hash = (53 * hash) + getLandingUrl().hashCode();
      if (hasDeeplink()) {
        hash = (37 * hash) + DEEPLINK_FIELD_NUMBER;
        hash = (53 * hash) + getDeeplink().hashCode();
      }
      if (hasDownloadUrl()) {
        hash = (37 * hash) + DOWNLOADURL_FIELD_NUMBER;
        hash = (53 * hash) + getDownloadUrl().hashCode();
      }
      if (hasBidFloor()) {
        hash = (37 * hash) + BIDFLOOR_FIELD_NUMBER;
        hash = (53 * hash) + getBidFloor();
      }
      if (getWinUrlsCount() > 0) {
        hash = (37 * hash) + WINURLS_FIELD_NUMBER;
        hash = (53 * hash) + getWinUrlsList().hashCode();
      }
      if (getLoseUrlsCount() > 0) {
        hash = (37 * hash) + LOSEURLS_FIELD_NUMBER;
        hash = (53 * hash) + getLoseUrlsList().hashCode();
      }
      if (hasCType()) {
        hash = (37 * hash) + CTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCType();
      }
      if (hasCiType()) {
        hash = (37 * hash) + CITYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCiType();
      }
      if (hasApp()) {
        hash = (37 * hash) + APP_FIELD_NUMBER;
        hash = (53 * hash) + getApp().hashCode();
      }
      if (hasVideo()) {
        hash = (37 * hash) + VIDEO_FIELD_NUMBER;
        hash = (53 * hash) + getVideo().hashCode();
      }
      if (getTrackersCount() > 0) {
        hash = (37 * hash) + TRACKERS_FIELD_NUMBER;
        hash = (53 * hash) + getTrackersList().hashCode();
      }
      if (hasUniversalLink()) {
        hash = (37 * hash) + UNIVERSALLINK_FIELD_NUMBER;
        hash = (53 * hash) + getUniversalLink().hashCode();
      }
      if (getClickAreaReportUrlsCount() > 0) {
        hash = (37 * hash) + CLICKAREAREPORTURLS_FIELD_NUMBER;
        hash = (53 * hash) + getClickAreaReportUrlsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code BidResponse.Bid}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BidResponse.Bid)
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder.class);
      }

      // Construct using cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getAppFieldBuilder();
          getVideoFieldBuilder();
          getTrackersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bidId_ = "";
        tagId_ = "";
        title_ = "";
        desc_ = "";
        iconUrl_ = "";
        imgUrls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        w_ = 0;
        h_ = 0;
        landingUrl_ = "";
        deeplink_ = "";
        downloadUrl_ = "";
        bidFloor_ = 0;
        winUrls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        loseUrls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        cType_ = 0;
        ciType_ = 0;
        app_ = null;
        if (appBuilder_ != null) {
          appBuilder_.dispose();
          appBuilder_ = null;
        }
        video_ = null;
        if (videoBuilder_ != null) {
          videoBuilder_.dispose();
          videoBuilder_ = null;
        }
        if (trackersBuilder_ == null) {
          trackers_ = java.util.Collections.emptyList();
        } else {
          trackers_ = null;
          trackersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00040000);
        universalLink_ = "";
        clickAreaReportUrls_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_Bid_descriptor;
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getDefaultInstanceForType() {
        return cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance();
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid build() {
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid buildPartial() {
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid result = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid result) {
        if (trackersBuilder_ == null) {
          if (((bitField0_ & 0x00040000) != 0)) {
            trackers_ = java.util.Collections.unmodifiableList(trackers_);
            bitField0_ = (bitField0_ & ~0x00040000);
          }
          result.trackers_ = trackers_;
        } else {
          result.trackers_ = trackersBuilder_.build();
        }
      }

      private void buildPartial0(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.bidId_ = bidId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.tagId_ = tagId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.title_ = title_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.desc_ = desc_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.iconUrl_ = iconUrl_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          imgUrls_.makeImmutable();
          result.imgUrls_ = imgUrls_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.w_ = w_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.h_ = h_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.landingUrl_ = landingUrl_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.deeplink_ = deeplink_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.downloadUrl_ = downloadUrl_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.bidFloor_ = bidFloor_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          winUrls_.makeImmutable();
          result.winUrls_ = winUrls_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          loseUrls_.makeImmutable();
          result.loseUrls_ = loseUrls_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.cType_ = cType_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.ciType_ = ciType_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.app_ = appBuilder_ == null
              ? app_
              : appBuilder_.build();
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.video_ = videoBuilder_ == null
              ? video_
              : videoBuilder_.build();
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.universalLink_ = universalLink_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          clickAreaReportUrls_.makeImmutable();
          result.clickAreaReportUrls_ = clickAreaReportUrls_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid) {
          return mergeFrom((cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid other) {
        if (other == cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance()) return this;
        if (other.hasBidId()) {
          bidId_ = other.bidId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getTagId().isEmpty()) {
          tagId_ = other.tagId_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasTitle()) {
          title_ = other.title_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasDesc()) {
          desc_ = other.desc_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getIconUrl().isEmpty()) {
          iconUrl_ = other.iconUrl_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.imgUrls_.isEmpty()) {
          if (imgUrls_.isEmpty()) {
            imgUrls_ = other.imgUrls_;
            bitField0_ |= 0x00000020;
          } else {
            ensureImgUrlsIsMutable();
            imgUrls_.addAll(other.imgUrls_);
          }
          onChanged();
        }
        if (other.hasW()) {
          setW(other.getW());
        }
        if (other.hasH()) {
          setH(other.getH());
        }
        if (!other.getLandingUrl().isEmpty()) {
          landingUrl_ = other.landingUrl_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasDeeplink()) {
          deeplink_ = other.deeplink_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasDownloadUrl()) {
          downloadUrl_ = other.downloadUrl_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasBidFloor()) {
          setBidFloor(other.getBidFloor());
        }
        if (!other.winUrls_.isEmpty()) {
          if (winUrls_.isEmpty()) {
            winUrls_ = other.winUrls_;
            bitField0_ |= 0x00001000;
          } else {
            ensureWinUrlsIsMutable();
            winUrls_.addAll(other.winUrls_);
          }
          onChanged();
        }
        if (!other.loseUrls_.isEmpty()) {
          if (loseUrls_.isEmpty()) {
            loseUrls_ = other.loseUrls_;
            bitField0_ |= 0x00002000;
          } else {
            ensureLoseUrlsIsMutable();
            loseUrls_.addAll(other.loseUrls_);
          }
          onChanged();
        }
        if (other.hasCType()) {
          setCType(other.getCType());
        }
        if (other.hasCiType()) {
          setCiType(other.getCiType());
        }
        if (other.hasApp()) {
          mergeApp(other.getApp());
        }
        if (other.hasVideo()) {
          mergeVideo(other.getVideo());
        }
        if (trackersBuilder_ == null) {
          if (!other.trackers_.isEmpty()) {
            if (trackers_.isEmpty()) {
              trackers_ = other.trackers_;
              bitField0_ = (bitField0_ & ~0x00040000);
            } else {
              ensureTrackersIsMutable();
              trackers_.addAll(other.trackers_);
            }
            onChanged();
          }
        } else {
          if (!other.trackers_.isEmpty()) {
            if (trackersBuilder_.isEmpty()) {
              trackersBuilder_.dispose();
              trackersBuilder_ = null;
              trackers_ = other.trackers_;
              bitField0_ = (bitField0_ & ~0x00040000);
              trackersBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getTrackersFieldBuilder() : null;
            } else {
              trackersBuilder_.addAllMessages(other.trackers_);
            }
          }
        }
        if (other.hasUniversalLink()) {
          universalLink_ = other.universalLink_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (!other.clickAreaReportUrls_.isEmpty()) {
          if (clickAreaReportUrls_.isEmpty()) {
            clickAreaReportUrls_ = other.clickAreaReportUrls_;
            bitField0_ |= 0x00100000;
          } else {
            ensureClickAreaReportUrlsIsMutable();
            clickAreaReportUrls_.addAll(other.clickAreaReportUrls_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                bidId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                tagId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                title_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                desc_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                iconUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureImgUrlsIsMutable();
                imgUrls_.add(s);
                break;
              } // case 50
              case 56: {
                w_ = input.readSInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                h_ = input.readSInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 74: {
                landingUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                deeplink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                downloadUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 96: {
                bidFloor_ = input.readSInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 106: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureWinUrlsIsMutable();
                winUrls_.add(s);
                break;
              } // case 106
              case 114: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureLoseUrlsIsMutable();
                loseUrls_.add(s);
                break;
              } // case 114
              case 120: {
                cType_ = input.readSInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 128: {
                ciType_ = input.readSInt32();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 138: {
                input.readMessage(
                    getAppFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                input.readMessage(
                    getVideoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker m =
                    input.readMessage(
                        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.parser(),
                        extensionRegistry);
                if (trackersBuilder_ == null) {
                  ensureTrackersIsMutable();
                  trackers_.add(m);
                } else {
                  trackersBuilder_.addMessage(m);
                }
                break;
              } // case 154
              case 162: {
                universalLink_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureClickAreaReportUrlsIsMutable();
                clickAreaReportUrls_.add(s);
                break;
              } // case 170
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object bidId_ = "";
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @return Whether the bidId field is set.
       */
      public boolean hasBidId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @return The bidId.
       */
      public java.lang.String getBidId() {
        java.lang.Object ref = bidId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bidId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @return The bytes for bidId.
       */
      public com.google.protobuf.ByteString
          getBidIdBytes() {
        java.lang.Object ref = bidId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bidId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @param value The bidId to set.
       * @return This builder for chaining.
       */
      public Builder setBidId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        bidId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBidId() {
        bidId_ = getDefaultInstance().getBidId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次竞价唯一ID(竞价模式)
       * </pre>
       *
       * <code>optional string bidId = 1;</code>
       * @param value The bytes for bidId to set.
       * @return This builder for chaining.
       */
      public Builder setBidIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        bidId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object tagId_ = "";
      /**
       * <pre>
       * 广告位ID
       * </pre>
       *
       * <code>string tagId = 2;</code>
       * @return The tagId.
       */
      public java.lang.String getTagId() {
        java.lang.Object ref = tagId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          tagId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告位ID
       * </pre>
       *
       * <code>string tagId = 2;</code>
       * @return The bytes for tagId.
       */
      public com.google.protobuf.ByteString
          getTagIdBytes() {
        java.lang.Object ref = tagId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tagId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告位ID
       * </pre>
       *
       * <code>string tagId = 2;</code>
       * @param value The tagId to set.
       * @return This builder for chaining.
       */
      public Builder setTagId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        tagId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告位ID
       * </pre>
       *
       * <code>string tagId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTagId() {
        tagId_ = getDefaultInstance().getTagId();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告位ID
       * </pre>
       *
       * <code>string tagId = 2;</code>
       * @param value The bytes for tagId to set.
       * @return This builder for chaining.
       */
      public Builder setTagIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        tagId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @return Whether the title field is set.
       */
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        title_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        title_ = getDefaultInstance().getTitle();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告标题
       * </pre>
       *
       * <code>optional string title = 3;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        title_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object desc_ = "";
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @return Whether the desc field is set.
       */
      public boolean hasDesc() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @return The desc.
       */
      public java.lang.String getDesc() {
        java.lang.Object ref = desc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          desc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @return The bytes for desc.
       */
      public com.google.protobuf.ByteString
          getDescBytes() {
        java.lang.Object ref = desc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          desc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @param value The desc to set.
       * @return This builder for chaining.
       */
      public Builder setDesc(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        desc_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDesc() {
        desc_ = getDefaultInstance().getDesc();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告描述
       * </pre>
       *
       * <code>optional string desc = 4;</code>
       * @param value The bytes for desc to set.
       * @return This builder for chaining.
       */
      public Builder setDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        desc_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object iconUrl_ = "";
      /**
       * <pre>
       * 广告图标icon链接
       * </pre>
       *
       * <code>string iconUrl = 5;</code>
       * @return The iconUrl.
       */
      public java.lang.String getIconUrl() {
        java.lang.Object ref = iconUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          iconUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广告图标icon链接
       * </pre>
       *
       * <code>string iconUrl = 5;</code>
       * @return The bytes for iconUrl.
       */
      public com.google.protobuf.ByteString
          getIconUrlBytes() {
        java.lang.Object ref = iconUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iconUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广告图标icon链接
       * </pre>
       *
       * <code>string iconUrl = 5;</code>
       * @param value The iconUrl to set.
       * @return This builder for chaining.
       */
      public Builder setIconUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iconUrl_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图标icon链接
       * </pre>
       *
       * <code>string iconUrl = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIconUrl() {
        iconUrl_ = getDefaultInstance().getIconUrl();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图标icon链接
       * </pre>
       *
       * <code>string iconUrl = 5;</code>
       * @param value The bytes for iconUrl to set.
       * @return This builder for chaining.
       */
      public Builder setIconUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        iconUrl_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList imgUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureImgUrlsIsMutable() {
        if (!imgUrls_.isModifiable()) {
          imgUrls_ = new com.google.protobuf.LazyStringArrayList(imgUrls_);
        }
        bitField0_ |= 0x00000020;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @return A list containing the imgUrls.
       */
      public com.google.protobuf.ProtocolStringList
          getImgUrlsList() {
        imgUrls_.makeImmutable();
        return imgUrls_;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @return The count of imgUrls.
       */
      public int getImgUrlsCount() {
        return imgUrls_.size();
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param index The index of the element to return.
       * @return The imgUrls at the given index.
       */
      public java.lang.String getImgUrls(int index) {
        return imgUrls_.get(index);
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the imgUrls at the given index.
       */
      public com.google.protobuf.ByteString
          getImgUrlsBytes(int index) {
        return imgUrls_.getByteString(index);
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param index The index to set the value at.
       * @param value The imgUrls to set.
       * @return This builder for chaining.
       */
      public Builder setImgUrls(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImgUrlsIsMutable();
        imgUrls_.set(index, value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param value The imgUrls to add.
       * @return This builder for chaining.
       */
      public Builder addImgUrls(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImgUrlsIsMutable();
        imgUrls_.add(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param values The imgUrls to add.
       * @return This builder for chaining.
       */
      public Builder addAllImgUrls(
          java.lang.Iterable<java.lang.String> values) {
        ensureImgUrlsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, imgUrls_);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearImgUrls() {
        imgUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告图片链接，单个广告可能多个图片地址
       * </pre>
       *
       * <code>repeated string imgUrls = 6;</code>
       * @param value The bytes of the imgUrls to add.
       * @return This builder for chaining.
       */
      public Builder addImgUrlsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureImgUrlsIsMutable();
        imgUrls_.add(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private int w_ ;
      /**
       * <pre>
       * 广告宽度，可能为空
       * </pre>
       *
       * <code>optional sint32 w = 7;</code>
       * @return Whether the w field is set.
       */
      @java.lang.Override
      public boolean hasW() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 广告宽度，可能为空
       * </pre>
       *
       * <code>optional sint32 w = 7;</code>
       * @return The w.
       */
      @java.lang.Override
      public int getW() {
        return w_;
      }
      /**
       * <pre>
       * 广告宽度，可能为空
       * </pre>
       *
       * <code>optional sint32 w = 7;</code>
       * @param value The w to set.
       * @return This builder for chaining.
       */
      public Builder setW(int value) {

        w_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告宽度，可能为空
       * </pre>
       *
       * <code>optional sint32 w = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearW() {
        bitField0_ = (bitField0_ & ~0x00000040);
        w_ = 0;
        onChanged();
        return this;
      }

      private int h_ ;
      /**
       * <pre>
       * 广告高度，可能为空
       * </pre>
       *
       * <code>optional sint32 h = 8;</code>
       * @return Whether the h field is set.
       */
      @java.lang.Override
      public boolean hasH() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 广告高度，可能为空
       * </pre>
       *
       * <code>optional sint32 h = 8;</code>
       * @return The h.
       */
      @java.lang.Override
      public int getH() {
        return h_;
      }
      /**
       * <pre>
       * 广告高度，可能为空
       * </pre>
       *
       * <code>optional sint32 h = 8;</code>
       * @param value The h to set.
       * @return This builder for chaining.
       */
      public Builder setH(int value) {

        h_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告高度，可能为空
       * </pre>
       *
       * <code>optional sint32 h = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearH() {
        bitField0_ = (bitField0_ & ~0x00000080);
        h_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object landingUrl_ = "";
      /**
       * <pre>
       * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
       * </pre>
       *
       * <code>string landingUrl = 9;</code>
       * @return The landingUrl.
       */
      public java.lang.String getLandingUrl() {
        java.lang.Object ref = landingUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          landingUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
       * </pre>
       *
       * <code>string landingUrl = 9;</code>
       * @return The bytes for landingUrl.
       */
      public com.google.protobuf.ByteString
          getLandingUrlBytes() {
        java.lang.Object ref = landingUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          landingUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
       * </pre>
       *
       * <code>string landingUrl = 9;</code>
       * @param value The landingUrl to set.
       * @return This builder for chaining.
       */
      public Builder setLandingUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        landingUrl_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
       * </pre>
       *
       * <code>string landingUrl = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearLandingUrl() {
        landingUrl_ = getDefaultInstance().getLandingUrl();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 落地页链接，若 ciType=3 时，该字段值可能为下载地址，与 downloadUrl 相同；
       * </pre>
       *
       * <code>string landingUrl = 9;</code>
       * @param value The bytes for landingUrl to set.
       * @return This builder for chaining.
       */
      public Builder setLandingUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        landingUrl_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object deeplink_ = "";
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @return Whether the deeplink field is set.
       */
      public boolean hasDeeplink() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @return The deeplink.
       */
      public java.lang.String getDeeplink() {
        java.lang.Object ref = deeplink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deeplink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @return The bytes for deeplink.
       */
      public com.google.protobuf.ByteString
          getDeeplinkBytes() {
        java.lang.Object ref = deeplink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deeplink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @param value The deeplink to set.
       * @return This builder for chaining.
       */
      public Builder setDeeplink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deeplink_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeeplink() {
        deeplink_ = getDefaultInstance().getDeeplink();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * deeplink链接，该参数有值时，请优先处理，若无值，则处理 landingUrl；
       * </pre>
       *
       * <code>optional string deeplink = 10;</code>
       * @param value The bytes for deeplink to set.
       * @return This builder for chaining.
       */
      public Builder setDeeplinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deeplink_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object downloadUrl_ = "";
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @return Whether the downloadUrl field is set.
       */
      public boolean hasDownloadUrl() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @return The downloadUrl.
       */
      public java.lang.String getDownloadUrl() {
        java.lang.Object ref = downloadUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          downloadUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @return The bytes for downloadUrl.
       */
      public com.google.protobuf.ByteString
          getDownloadUrlBytes() {
        java.lang.Object ref = downloadUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          downloadUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @param value The downloadUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDownloadUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        downloadUrl_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownloadUrl() {
        downloadUrl_ = getDefaultInstance().getDownloadUrl();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APP应用下载链接
       * </pre>
       *
       * <code>optional string downloadUrl = 11;</code>
       * @param value The bytes for downloadUrl to set.
       * @return This builder for chaining.
       */
      public Builder setDownloadUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        downloadUrl_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private int bidFloor_ ;
      /**
       * <pre>
       * 竞价广告出价，单位: 分/cpm(竞价模式)
       * </pre>
       *
       * <code>optional sint32 bidFloor = 12;</code>
       * @return Whether the bidFloor field is set.
       */
      @java.lang.Override
      public boolean hasBidFloor() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 竞价广告出价，单位: 分/cpm(竞价模式)
       * </pre>
       *
       * <code>optional sint32 bidFloor = 12;</code>
       * @return The bidFloor.
       */
      @java.lang.Override
      public int getBidFloor() {
        return bidFloor_;
      }
      /**
       * <pre>
       * 竞价广告出价，单位: 分/cpm(竞价模式)
       * </pre>
       *
       * <code>optional sint32 bidFloor = 12;</code>
       * @param value The bidFloor to set.
       * @return This builder for chaining.
       */
      public Builder setBidFloor(int value) {

        bidFloor_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价广告出价，单位: 分/cpm(竞价模式)
       * </pre>
       *
       * <code>optional sint32 bidFloor = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearBidFloor() {
        bitField0_ = (bitField0_ & ~0x00000800);
        bidFloor_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList winUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureWinUrlsIsMutable() {
        if (!winUrls_.isModifiable()) {
          winUrls_ = new com.google.protobuf.LazyStringArrayList(winUrls_);
        }
        bitField0_ |= 0x00001000;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @return A list containing the winUrls.
       */
      public com.google.protobuf.ProtocolStringList
          getWinUrlsList() {
        winUrls_.makeImmutable();
        return winUrls_;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @return The count of winUrls.
       */
      public int getWinUrlsCount() {
        return winUrls_.size();
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param index The index of the element to return.
       * @return The winUrls at the given index.
       */
      public java.lang.String getWinUrls(int index) {
        return winUrls_.get(index);
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param index The index of the value to return.
       * @return The bytes of the winUrls at the given index.
       */
      public com.google.protobuf.ByteString
          getWinUrlsBytes(int index) {
        return winUrls_.getByteString(index);
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param index The index to set the value at.
       * @param value The winUrls to set.
       * @return This builder for chaining.
       */
      public Builder setWinUrls(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureWinUrlsIsMutable();
        winUrls_.set(index, value);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param value The winUrls to add.
       * @return This builder for chaining.
       */
      public Builder addWinUrls(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureWinUrlsIsMutable();
        winUrls_.add(value);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param values The winUrls to add.
       * @return This builder for chaining.
       */
      public Builder addAllWinUrls(
          java.lang.Iterable<java.lang.String> values) {
        ensureWinUrlsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, winUrls_);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinUrls() {
        winUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00001000);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价成功上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string winUrls = 13;</code>
       * @param value The bytes of the winUrls to add.
       * @return This builder for chaining.
       */
      public Builder addWinUrlsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureWinUrlsIsMutable();
        winUrls_.add(value);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList loseUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureLoseUrlsIsMutable() {
        if (!loseUrls_.isModifiable()) {
          loseUrls_ = new com.google.protobuf.LazyStringArrayList(loseUrls_);
        }
        bitField0_ |= 0x00002000;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @return A list containing the loseUrls.
       */
      public com.google.protobuf.ProtocolStringList
          getLoseUrlsList() {
        loseUrls_.makeImmutable();
        return loseUrls_;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @return The count of loseUrls.
       */
      public int getLoseUrlsCount() {
        return loseUrls_.size();
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param index The index of the element to return.
       * @return The loseUrls at the given index.
       */
      public java.lang.String getLoseUrls(int index) {
        return loseUrls_.get(index);
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param index The index of the value to return.
       * @return The bytes of the loseUrls at the given index.
       */
      public com.google.protobuf.ByteString
          getLoseUrlsBytes(int index) {
        return loseUrls_.getByteString(index);
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param index The index to set the value at.
       * @param value The loseUrls to set.
       * @return This builder for chaining.
       */
      public Builder setLoseUrls(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureLoseUrlsIsMutable();
        loseUrls_.set(index, value);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param value The loseUrls to add.
       * @return This builder for chaining.
       */
      public Builder addLoseUrls(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureLoseUrlsIsMutable();
        loseUrls_.add(value);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param values The loseUrls to add.
       * @return This builder for chaining.
       */
      public Builder addAllLoseUrls(
          java.lang.Iterable<java.lang.String> values) {
        ensureLoseUrlsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, loseUrls_);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoseUrls() {
        loseUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00002000);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价失败上报地址，可能有多条(竞价模式)
       * </pre>
       *
       * <code>repeated string loseUrls = 14;</code>
       * @param value The bytes of the loseUrls to add.
       * @return This builder for chaining.
       */
      public Builder addLoseUrlsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureLoseUrlsIsMutable();
        loseUrls_.add(value);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      private int cType_ ;
      /**
       * <pre>
       * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
       * </pre>
       *
       * <code>optional sint32 cType = 15;</code>
       * @return Whether the cType field is set.
       */
      @java.lang.Override
      public boolean hasCType() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
       * </pre>
       *
       * <code>optional sint32 cType = 15;</code>
       * @return The cType.
       */
      @java.lang.Override
      public int getCType() {
        return cType_;
      }
      /**
       * <pre>
       * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
       * </pre>
       *
       * <code>optional sint32 cType = 15;</code>
       * @param value The cType to set.
       * @return This builder for chaining.
       */
      public Builder setCType(int value) {

        cType_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告的类型，1：文字，2：静态图片，3：图文，4：视频，5：激励视频，6：gif；
       * </pre>
       *
       * <code>optional sint32 cType = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearCType() {
        bitField0_ = (bitField0_ & ~0x00004000);
        cType_ = 0;
        onChanged();
        return this;
      }

      private int ciType_ ;
      /**
       * <pre>
       * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
       * </pre>
       *
       * <code>optional sint32 ciType = 16;</code>
       * @return Whether the ciType field is set.
       */
      @java.lang.Override
      public boolean hasCiType() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
       * </pre>
       *
       * <code>optional sint32 ciType = 16;</code>
       * @return The ciType.
       */
      @java.lang.Override
      public int getCiType() {
        return ciType_;
      }
      /**
       * <pre>
       * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
       * </pre>
       *
       * <code>optional sint32 ciType = 16;</code>
       * @param value The ciType to set.
       * @return This builder for chaining.
       */
      public Builder setCiType(int value) {

        ciType_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告的交互类型，1：浏览器打开，2：应用内打开，3:直接下载应用，4：访问后下载；
       * </pre>
       *
       * <code>optional sint32 ciType = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearCiType() {
        bitField0_ = (bitField0_ & ~0x00008000);
        ciType_ = 0;
        onChanged();
        return this;
      }

      private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App app_;
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder> appBuilder_;
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       * @return Whether the app field is set.
       */
      public boolean hasApp() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       * @return The app.
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App getApp() {
        if (appBuilder_ == null) {
          return app_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance() : app_;
        } else {
          return appBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public Builder setApp(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App value) {
        if (appBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          app_ = value;
        } else {
          appBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public Builder setApp(
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder builderForValue) {
        if (appBuilder_ == null) {
          app_ = builderForValue.build();
        } else {
          appBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public Builder mergeApp(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App value) {
        if (appBuilder_ == null) {
          if (((bitField0_ & 0x00010000) != 0) &&
            app_ != null &&
            app_ != cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance()) {
            getAppBuilder().mergeFrom(value);
          } else {
            app_ = value;
          }
        } else {
          appBuilder_.mergeFrom(value);
        }
        if (app_ != null) {
          bitField0_ |= 0x00010000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public Builder clearApp() {
        bitField0_ = (bitField0_ & ~0x00010000);
        app_ = null;
        if (appBuilder_ != null) {
          appBuilder_.dispose();
          appBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder getAppBuilder() {
        bitField0_ |= 0x00010000;
        onChanged();
        return getAppFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder getAppOrBuilder() {
        if (appBuilder_ != null) {
          return appBuilder_.getMessageOrBuilder();
        } else {
          return app_ == null ?
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.getDefaultInstance() : app_;
        }
      }
      /**
       * <pre>
       * APP应用下载信息
       * </pre>
       *
       * <code>optional .BidResponse.Bid.App app = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder> 
          getAppFieldBuilder() {
        if (appBuilder_ == null) {
          appBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.App.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.AppOrBuilder>(
                  getApp(),
                  getParentForChildren(),
                  isClean());
          app_ = null;
        }
        return appBuilder_;
      }

      private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video video_;
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder> videoBuilder_;
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       * @return Whether the video field is set.
       */
      public boolean hasVideo() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       * @return The video.
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video getVideo() {
        if (videoBuilder_ == null) {
          return video_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance() : video_;
        } else {
          return videoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public Builder setVideo(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video value) {
        if (videoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          video_ = value;
        } else {
          videoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public Builder setVideo(
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder builderForValue) {
        if (videoBuilder_ == null) {
          video_ = builderForValue.build();
        } else {
          videoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public Builder mergeVideo(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video value) {
        if (videoBuilder_ == null) {
          if (((bitField0_ & 0x00020000) != 0) &&
            video_ != null &&
            video_ != cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance()) {
            getVideoBuilder().mergeFrom(value);
          } else {
            video_ = value;
          }
        } else {
          videoBuilder_.mergeFrom(value);
        }
        if (video_ != null) {
          bitField0_ |= 0x00020000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public Builder clearVideo() {
        bitField0_ = (bitField0_ & ~0x00020000);
        video_ = null;
        if (videoBuilder_ != null) {
          videoBuilder_.dispose();
          videoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder getVideoBuilder() {
        bitField0_ |= 0x00020000;
        onChanged();
        return getVideoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder getVideoOrBuilder() {
        if (videoBuilder_ != null) {
          return videoBuilder_.getMessageOrBuilder();
        } else {
          return video_ == null ?
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.getDefaultInstance() : video_;
        }
      }
      /**
       * <pre>
       * 视频广告素材
       * </pre>
       *
       * <code>optional .BidResponse.Bid.Video video = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder> 
          getVideoFieldBuilder() {
        if (videoBuilder_ == null) {
          videoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Video.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.VideoOrBuilder>(
                  getVideo(),
                  getParentForChildren(),
                  isClean());
          video_ = null;
        }
        return videoBuilder_;
      }

      private java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> trackers_ =
        java.util.Collections.emptyList();
      private void ensureTrackersIsMutable() {
        if (!((bitField0_ & 0x00040000) != 0)) {
          trackers_ = new java.util.ArrayList<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker>(trackers_);
          bitField0_ |= 0x00040000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder> trackersBuilder_;

      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> getTrackersList() {
        if (trackersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(trackers_);
        } else {
          return trackersBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public int getTrackersCount() {
        if (trackersBuilder_ == null) {
          return trackers_.size();
        } else {
          return trackersBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker getTrackers(int index) {
        if (trackersBuilder_ == null) {
          return trackers_.get(index);
        } else {
          return trackersBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder setTrackers(
          int index, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker value) {
        if (trackersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackersIsMutable();
          trackers_.set(index, value);
          onChanged();
        } else {
          trackersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder setTrackers(
          int index, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder builderForValue) {
        if (trackersBuilder_ == null) {
          ensureTrackersIsMutable();
          trackers_.set(index, builderForValue.build());
          onChanged();
        } else {
          trackersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder addTrackers(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker value) {
        if (trackersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackersIsMutable();
          trackers_.add(value);
          onChanged();
        } else {
          trackersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder addTrackers(
          int index, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker value) {
        if (trackersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTrackersIsMutable();
          trackers_.add(index, value);
          onChanged();
        } else {
          trackersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder addTrackers(
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder builderForValue) {
        if (trackersBuilder_ == null) {
          ensureTrackersIsMutable();
          trackers_.add(builderForValue.build());
          onChanged();
        } else {
          trackersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder addTrackers(
          int index, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder builderForValue) {
        if (trackersBuilder_ == null) {
          ensureTrackersIsMutable();
          trackers_.add(index, builderForValue.build());
          onChanged();
        } else {
          trackersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder addAllTrackers(
          java.lang.Iterable<? extends cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker> values) {
        if (trackersBuilder_ == null) {
          ensureTrackersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, trackers_);
          onChanged();
        } else {
          trackersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder clearTrackers() {
        if (trackersBuilder_ == null) {
          trackers_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00040000);
          onChanged();
        } else {
          trackersBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public Builder removeTrackers(int index) {
        if (trackersBuilder_ == null) {
          ensureTrackersIsMutable();
          trackers_.remove(index);
          onChanged();
        } else {
          trackersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder getTrackersBuilder(
          int index) {
        return getTrackersFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder getTrackersOrBuilder(
          int index) {
        if (trackersBuilder_ == null) {
          return trackers_.get(index);  } else {
          return trackersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public java.util.List<? extends cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder> 
           getTrackersOrBuilderList() {
        if (trackersBuilder_ != null) {
          return trackersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(trackers_);
        }
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder addTrackersBuilder() {
        return getTrackersFieldBuilder().addBuilder(
            cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.getDefaultInstance());
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder addTrackersBuilder(
          int index) {
        return getTrackersFieldBuilder().addBuilder(
            index, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.getDefaultInstance());
      }
      /**
       * <pre>
       * Tracker 对象列表，用于上报广告执行情况，必须全部依次上报
       * </pre>
       *
       * <code>repeated .BidResponse.Bid.Tracker trackers = 19;</code>
       */
      public java.util.List<cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder> 
           getTrackersBuilderList() {
        return getTrackersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder> 
          getTrackersFieldBuilder() {
        if (trackersBuilder_ == null) {
          trackersBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Tracker.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.TrackerOrBuilder>(
                  trackers_,
                  ((bitField0_ & 0x00040000) != 0),
                  getParentForChildren(),
                  isClean());
          trackers_ = null;
        }
        return trackersBuilder_;
      }

      private java.lang.Object universalLink_ = "";
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @return Whether the universalLink field is set.
       */
      public boolean hasUniversalLink() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @return The universalLink.
       */
      public java.lang.String getUniversalLink() {
        java.lang.Object ref = universalLink_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          universalLink_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @return The bytes for universalLink.
       */
      public com.google.protobuf.ByteString
          getUniversalLinkBytes() {
        java.lang.Object ref = universalLink_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          universalLink_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @param value The universalLink to set.
       * @return This builder for chaining.
       */
      public Builder setUniversalLink(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        universalLink_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearUniversalLink() {
        universalLink_ = getDefaultInstance().getUniversalLink();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * app 端 iOS DP调起链接，优先级高于deeplink；
       * </pre>
       *
       * <code>optional string universalLink = 20;</code>
       * @param value The bytes for universalLink to set.
       * @return This builder for chaining.
       */
      public Builder setUniversalLinkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        universalLink_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList clickAreaReportUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureClickAreaReportUrlsIsMutable() {
        if (!clickAreaReportUrls_.isModifiable()) {
          clickAreaReportUrls_ = new com.google.protobuf.LazyStringArrayList(clickAreaReportUrls_);
        }
        bitField0_ |= 0x00100000;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @return A list containing the clickAreaReportUrls.
       */
      public com.google.protobuf.ProtocolStringList
          getClickAreaReportUrlsList() {
        clickAreaReportUrls_.makeImmutable();
        return clickAreaReportUrls_;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @return The count of clickAreaReportUrls.
       */
      public int getClickAreaReportUrlsCount() {
        return clickAreaReportUrls_.size();
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param index The index of the element to return.
       * @return The clickAreaReportUrls at the given index.
       */
      public java.lang.String getClickAreaReportUrls(int index) {
        return clickAreaReportUrls_.get(index);
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param index The index of the value to return.
       * @return The bytes of the clickAreaReportUrls at the given index.
       */
      public com.google.protobuf.ByteString
          getClickAreaReportUrlsBytes(int index) {
        return clickAreaReportUrls_.getByteString(index);
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param index The index to set the value at.
       * @param value The clickAreaReportUrls to set.
       * @return This builder for chaining.
       */
      public Builder setClickAreaReportUrls(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureClickAreaReportUrlsIsMutable();
        clickAreaReportUrls_.set(index, value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param value The clickAreaReportUrls to add.
       * @return This builder for chaining.
       */
      public Builder addClickAreaReportUrls(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureClickAreaReportUrlsIsMutable();
        clickAreaReportUrls_.add(value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param values The clickAreaReportUrls to add.
       * @return This builder for chaining.
       */
      public Builder addAllClickAreaReportUrls(
          java.lang.Iterable<java.lang.String> values) {
        ensureClickAreaReportUrlsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clickAreaReportUrls_);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearClickAreaReportUrls() {
        clickAreaReportUrls_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00100000);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 汇川点击坐标打点post上报，可能含有多个连接，详情见文档末尾描述；
       * </pre>
       *
       * <code>repeated string clickAreaReportUrls = 21;</code>
       * @param value The bytes of the clickAreaReportUrls to add.
       * @return This builder for chaining.
       */
      public Builder addClickAreaReportUrlsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureClickAreaReportUrlsIsMutable();
        clickAreaReportUrls_.add(value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:BidResponse.Bid)
    }

    // @@protoc_insertion_point(class_scope:BidResponse.Bid)
    private static final cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid();
    }

    public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Bid>
        PARSER = new com.google.protobuf.AbstractParser<Bid>() {
      @java.lang.Override
      public Bid parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Bid> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Bid> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <pre>
   * 媒体请求时的唯一ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 媒体请求时的唯一ID
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 2;
  private int code_ = 0;
  /**
   * <pre>
   * 响应码，200：有广告填充，404：无广告填充
   * </pre>
   *
   * <code>sint32 code = 2;</code>
   * @return The code.
   */
  @java.lang.Override
  public int getCode() {
    return code_;
  }

  public static final int MSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object msg_ = "";
  /**
   * <pre>
   * 响应说明
   * </pre>
   *
   * <code>string msg = 3;</code>
   * @return The msg.
   */
  @java.lang.Override
  public java.lang.String getMsg() {
    java.lang.Object ref = msg_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      msg_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 响应说明
   * </pre>
   *
   * <code>string msg = 3;</code>
   * @return The bytes for msg.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMsgBytes() {
    java.lang.Object ref = msg_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      msg_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BID_FIELD_NUMBER = 4;
  private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid bid_;
  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   * @return Whether the bid field is set.
   */
  @java.lang.Override
  public boolean hasBid() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   * @return The bid.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getBid() {
    return bid_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance() : bid_;
  }
  /**
   * <pre>
   * 广告内容信息，无广告返回时此对象为空
   * </pre>
   *
   * <code>optional .BidResponse.Bid bid = 4;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder getBidOrBuilder() {
    return bid_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance() : bid_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (code_ != 0) {
      output.writeSInt32(2, code_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, msg_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(4, getBid());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (code_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeSInt32Size(2, code_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(msg_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, msg_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getBid());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.tianzao.dto.BidResponse other = (cn.taken.ad.logic.adv.tianzao.dto.BidResponse) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (getCode()
        != other.getCode()) return false;
    if (!getMsg()
        .equals(other.getMsg())) return false;
    if (hasBid() != other.hasBid()) return false;
    if (hasBid()) {
      if (!getBid()
          .equals(other.getBid())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode();
    hash = (37 * hash) + MSG_FIELD_NUMBER;
    hash = (53 * hash) + getMsg().hashCode();
    if (hasBid()) {
      hash = (37 * hash) + BID_FIELD_NUMBER;
      hash = (53 * hash) + getBid().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.tianzao.dto.BidResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 响应体
   * </pre>
   *
   * Protobuf type {@code BidResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:BidResponse)
      cn.taken.ad.logic.adv.tianzao.dto.BidResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.tianzao.dto.BidResponse.class, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.tianzao.dto.BidResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getBidFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      code_ = 0;
      msg_ = "";
      bid_ = null;
      if (bidBuilder_ != null) {
        bidBuilder_.dispose();
        bidBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.tianzao.dto.TianZaoBidRequest.internal_static_BidResponse_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.tianzao.dto.BidResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse build() {
      cn.taken.ad.logic.adv.tianzao.dto.BidResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse buildPartial() {
      cn.taken.ad.logic.adv.tianzao.dto.BidResponse result = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.tianzao.dto.BidResponse result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.code_ = code_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.msg_ = msg_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.bid_ = bidBuilder_ == null
            ? bid_
            : bidBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.tianzao.dto.BidResponse) {
        return mergeFrom((cn.taken.ad.logic.adv.tianzao.dto.BidResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.tianzao.dto.BidResponse other) {
      if (other == cn.taken.ad.logic.adv.tianzao.dto.BidResponse.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getCode() != 0) {
        setCode(other.getCode());
      }
      if (!other.getMsg().isEmpty()) {
        msg_ = other.msg_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasBid()) {
        mergeBid(other.getBid());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              code_ = input.readSInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              msg_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  getBidFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     * 媒体请求时的唯一ID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 媒体请求时的唯一ID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 媒体请求时的唯一ID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 媒体请求时的唯一ID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 媒体请求时的唯一ID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int code_ ;
    /**
     * <pre>
     * 响应码，200：有广告填充，404：无广告填充
     * </pre>
     *
     * <code>sint32 code = 2;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }
    /**
     * <pre>
     * 响应码，200：有广告填充，404：无广告填充
     * </pre>
     *
     * <code>sint32 code = 2;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(int value) {

      code_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 响应码，200：有广告填充，404：无广告填充
     * </pre>
     *
     * <code>sint32 code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      bitField0_ = (bitField0_ & ~0x00000002);
      code_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object msg_ = "";
    /**
     * <pre>
     * 响应说明
     * </pre>
     *
     * <code>string msg = 3;</code>
     * @return The msg.
     */
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        msg_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 响应说明
     * </pre>
     *
     * <code>string msg = 3;</code>
     * @return The bytes for msg.
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 响应说明
     * </pre>
     *
     * <code>string msg = 3;</code>
     * @param value The msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsg(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      msg_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 响应说明
     * </pre>
     *
     * <code>string msg = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMsg() {
      msg_ = getDefaultInstance().getMsg();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 响应说明
     * </pre>
     *
     * <code>string msg = 3;</code>
     * @param value The bytes for msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsgBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      msg_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid bid_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder> bidBuilder_;
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     * @return Whether the bid field is set.
     */
    public boolean hasBid() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     * @return The bid.
     */
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid getBid() {
      if (bidBuilder_ == null) {
        return bid_ == null ? cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance() : bid_;
      } else {
        return bidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public Builder setBid(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid value) {
      if (bidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        bid_ = value;
      } else {
        bidBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public Builder setBid(
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder builderForValue) {
      if (bidBuilder_ == null) {
        bid_ = builderForValue.build();
      } else {
        bidBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public Builder mergeBid(cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid value) {
      if (bidBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          bid_ != null &&
          bid_ != cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance()) {
          getBidBuilder().mergeFrom(value);
        } else {
          bid_ = value;
        }
      } else {
        bidBuilder_.mergeFrom(value);
      }
      if (bid_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public Builder clearBid() {
      bitField0_ = (bitField0_ & ~0x00000008);
      bid_ = null;
      if (bidBuilder_ != null) {
        bidBuilder_.dispose();
        bidBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder getBidBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getBidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    public cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder getBidOrBuilder() {
      if (bidBuilder_ != null) {
        return bidBuilder_.getMessageOrBuilder();
      } else {
        return bid_ == null ?
            cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.getDefaultInstance() : bid_;
      }
    }
    /**
     * <pre>
     * 广告内容信息，无广告返回时此对象为空
     * </pre>
     *
     * <code>optional .BidResponse.Bid bid = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder> 
        getBidFieldBuilder() {
      if (bidBuilder_ == null) {
        bidBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.Bid.Builder, cn.taken.ad.logic.adv.tianzao.dto.BidResponse.BidOrBuilder>(
                getBid(),
                getParentForChildren(),
                isClean());
        bid_ = null;
      }
      return bidBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:BidResponse)
  }

  // @@protoc_insertion_point(class_scope:BidResponse)
  private static final cn.taken.ad.logic.adv.tianzao.dto.BidResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.tianzao.dto.BidResponse();
  }

  public static cn.taken.ad.logic.adv.tianzao.dto.BidResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BidResponse>
      PARSER = new com.google.protobuf.AbstractParser<BidResponse>() {
    @java.lang.Override
    public BidResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BidResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BidResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.tianzao.dto.BidResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

