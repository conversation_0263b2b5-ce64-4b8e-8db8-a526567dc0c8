package cn.taken.ad.logic.adv.yiqi.dto;

public class YiQiRequestDeal {
    /**
     * 计价方式。取值
     * 1：cpm
     * 2：cpc
     */
    private Integer chargeType;
    /**
     * RTB 价格（元/cpm、元/cpc）保留三位小数
     * 对应 RTB 计价方式的价格
     * cpm：表示的 1000 次展示的价格，单位元。
     * cpc：表示的 1 次点击的价格，单位元。
     */
    private Double price;

    public YiQiRequestDeal() {
    }

    public YiQiRequestDeal(Integer chargeType, Double price) {
        this.chargeType = chargeType;
        this.price = price;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }
}
