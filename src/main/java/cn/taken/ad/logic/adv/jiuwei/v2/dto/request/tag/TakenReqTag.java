// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RequestTag.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.request.tag;

public final class TakenReqTag {
  private TakenReqTag() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TakenReqTag.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_req_TakenRequestTag_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_req_TakenRequestTag_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020RequestTag.proto\022\003req\"\212\001\n\017TakenRequest" +
      "Tag\022\r\n\005tagId\030\001 \001(\t\022\020\n\010deeplink\030\002 \001(\005\022\r\n\005" +
      "width\030\003 \001(\005\022\016\n\006height\030\004 \001(\005\022\r\n\005price\030\005 \001" +
      "(\005\022\023\n\013minDuration\030\006 \001(\005\022\023\n\013maxDuration\030\007" +
      " \001(\005BC\n2cn.taken.ad.logic.prossor.taken." +
      "v2.dto.request.tagB\013TakenReqTagP\001b\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_req_TakenRequestTag_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_req_TakenRequestTag_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_req_TakenRequestTag_descriptor,
        new String[] { "TagId", "Deeplink", "Width", "Height", "Price", "MinDuration", "MaxDuration", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
