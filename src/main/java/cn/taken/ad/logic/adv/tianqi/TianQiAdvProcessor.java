package cn.taken.ad.logic.adv.tianqi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.ActionType;
import cn.taken.ad.constant.business.CarrierType;
import cn.taken.ad.constant.business.ConnectionType;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.MaterialType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.jindai.dto.BidClickUrl;
import cn.taken.ad.logic.adv.tianqi.dto.TianQiBidRequest;
import cn.taken.ad.logic.adv.tianqi.dto.TianQiBidResponse;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestCaidDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestUserDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseMiniProgramDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component("TIANQI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class TianQiAdvProcessor implements AdvProcessor {
    private static final Logger log = LoggerFactory.getLogger(TianQiAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto request, RtbAdvDto advDto) throws Throwable {
        TianQiBidRequest.Builder rtb = TianQiBidRequest.newBuilder();
        rtb.setId(request.getReqId());
        rtb.addImp(warpTag(request, advDto));
        rtb.setApp(warpApp(request.getApp(), advDto));
        rtb.setDevice(warpDevice(request.getDevice(), request.getNetwork(), request.getGeo()));
        rtb.setUser(warpUser(request.getUser()));
        // 0 - 优先购买（不参与竞价）
        //1 - 最高价格成交（参与竞价）2 - 以次高价格成交（参与竞价）
        rtb.setAt(advDto.getSettlementType() == 1 ? 1 : 0);
        rtb.setTmax(advDto.getTimeout());
        advDto.setReqObj(rtb);
//        log.info("request:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(rtb));

        byte[] bytes = rtb.build().toByteArray();
        // 请求地址
        HttpResult httpResult = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{
                new BasicHeader("Content-Type", "application/x-protobuf;charset=UTF-8"),
                new BasicHeader("Accept-Encoding", "gzip")
        }, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        // 解析响应
        return parseResponse(advDto, request, httpResult, httpClient);
    }

    private RtbResponseDto parseResponse(RtbAdvDto advDto, RtbRequestDto request, HttpResult httpResult, FastHttpClient httpClient) throws Exception {
        byte[] data = httpResult.getData();
        if (null == data || data.length == 0) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        TianQiBidResponse takenResp = TianQiBidResponse.parseFrom(data);
//        log.info("response:{}", JsonFormat.printer().omittingInsignificantWhitespace().print(takenResp));
        advDto.setRespObj(takenResp);
        if (takenResp.getSeatbidList().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        // 广告信息
        List<TianQiBidResponse.TianQiSeatbid> takenTags = takenResp.getSeatbidList();
        List<TagResponseDto> tags = new ArrayList<>();
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), LogicState.SUCCESS.getDesc(), takenResp.getId(), tags);
        responseDto.setRespId(takenResp.getId());
        takenTags.forEach(seatbid -> {
                    seatbid.getBidList().forEach(item -> {
                        float advPrice = item.getPrice();
                        TagResponseDto dto = new TagResponseDto();
                        dto.setTagInfoId(item.getId());
                        dto.setPrice((double) advPrice);
                        dto.setCreativeId(item.getCrid());
                        switch (item.getMaterialtype()) {
                            case 1:
                            case 2:
                                dto.setMaterialType(MaterialType.IMAGE_TEXT);
                                break;
                            case 3:
                                dto.setMaterialType(MaterialType.TEXT);
                                break;
                            case 4:
                                dto.setMaterialType(MaterialType.HTML);
                                dto.setHtmlContent(StringUtils.isEmpty(item.getAdm()) ? "" : item.getAdm());
                                break;
                            case 5:
                            case 6:
                            case 7:
                                dto.setMaterialType(MaterialType.VIDEO);
                                break;

                        }
                        dto.setTitle(item.getTitle());
                        dto.setDesc(item.getDesc());
                        dto.setMaterialHeight(item.getH());
                        dto.setMaterialWidth(item.getW());

                        if (StringUtils.isNotEmpty(item.getDeeplinkurl())) {
                            dto.setClickUrl(item.getDeeplinkurl());
                        }
                        String clickId = null;

                        if (item.getInteractiontype() > 0) {
                            //1 - 跳转目标链接
                            //2 - 应用下载 3 - deeplink 4 - 广点通

                            if (item.getInteractiontype() == 1) {
                                dto.setActionType(ActionType.SYSTEM_BROWSER_H5);
                                dto.setClickUrl(item.getTargeturl());
                            } else if (item.getInteractiontype() == 3) {
                                dto.setActionType(ActionType.DEEPLINK);
                                dto.setClickUrl(item.getDeeplinkurl());
                            } else if (item.getInteractiontype() == 2) {
                                dto.setActionType(ActionType.DOWNLOAD);
                                if (StringUtils.isNotEmpty(item.getTargeturl())) {
                                    dto.setClickUrl(item.getTargeturl());
                                } else if (StringUtils.isNotEmpty(item.getUniversallink())) {
                                    dto.setClickUrl(item.getUniversallink());
                                }
                            } else if (item.getInteractiontype() == 4) {
                                //广点通地址处理
                                try {
                                    HttpResult hr = httpClient.get(item.getTargeturl(), null, null, -1);
                                    if (hr.isSuccess()) {
                                        BidClickUrl bc = hr.getDataObjectByJson(BidClickUrl.class);
                                        clickId = bc.getClickid();
                                        dto.setClickUrl(bc.getDstlink());
                                    }
                                } catch (Exception e) {
                                    log.error("error:", e);
                                }
                            }
                        }

                        if (StringUtils.isNotEmpty(item.getDeeplinkurl())) {
                            dto.setDeepLinkUrl(item.getDeeplinkurl());
                        }

                        //appinfo
                        if (StringUtils.isNotEmpty(item.getAppname())) {
                            ResponseAppDto appDto = new ResponseAppDto();
                            appDto.setAppName(item.getAppname());
                            if (StringUtils.isNotEmpty(item.getIcon())) {
                                appDto.setAppIconUrl(item.getIcon());
                            }
                            if (StringUtils.isNotEmpty(item.getAppbundle())) {
                                appDto.setPackageName(item.getAppbundle());
                            }
                            if (item.getAppsize() > 0) {
                                appDto.setAppSize(item.getAppsize() * 1024L);
                            }
                            dto.setAppInfo(appDto);
                        }
                        if (StringUtils.isNotEmpty(item.getAppletid())) {
                            ResponseMiniProgramDto responseMiniProgramDto = new ResponseMiniProgramDto();
                            responseMiniProgramDto.setId(item.getAppletid());
                            if (StringUtils.isNotEmpty(item.getAppletpath())) {
                                responseMiniProgramDto.setPath(item.getAppletpath());
                            }
                            dto.setMiniProgram(responseMiniProgramDto);
                        }
                        if (item.hasVideo()) {
                            //video
                            ResponseVideoDto videoDto = new ResponseVideoDto();
                            TianQiBidResponse.TianQiSeatbid.Bid.Video video = item.getVideo();
                            if (StringUtils.isNotEmpty(video.getUrl())) {
                                videoDto.setVideoUrl(video.getUrl());
                            }
                            if (video.getDuration() > 0) {
                                videoDto.setDuration(video.getDuration());
                            }
                            if (video.getLength() > 0) {
                                videoDto.setVideoSize(video.getLength() * 1024L);
                            }
                            if (video.getW() > 0) {
                                videoDto.setVideoWidth(video.getW());
                                videoDto.setCoverWidth(video.getW());
                            }
                            if (video.getH() > 0) {
                                videoDto.setVideoHeight(video.getH());
                                videoDto.setCoverHeight(video.getH());
                            }
                            if (StringUtils.isNotEmpty(video.getCoverurl())) {
                                videoDto.setCoverImgUrls(new ArrayList<>(Collections.singletonList(video.getUrl())));
                            }
                            if (video.getSkipmintime() > 0) {
                                videoDto.setSkipSeconds(video.getSkipmintime());
                            }
                            if (video.getPreloadtime() > 0) {
                                videoDto.setSkipSeconds(video.getPreloadtime());
                            }

                            if (video.getSkip() >= 0) {
                                videoDto.setClickAble(video.getSkip() != 0);
                            }
                            if (StringUtils.isNotEmpty(video.getEndbuttontext())) {
                                videoDto.setEndButtonText(video.getEndbuttontext());
                            }
                            if (StringUtils.isNotEmpty(video.getEndcardurl())) {
                                videoDto.setEndImgUrls(Collections.singletonList(video.getEndcardurl()));
                            }
                            if (StringUtils.isNotEmpty(video.getEndcardhtml())) {
                                videoDto.setEndHtml(video.getEndcardhtml());
                            }
                            if (StringUtils.isNotEmpty(video.getEndtitle())) {
                                videoDto.setEndTitle(video.getEndtitle());
                            }
                            if (StringUtils.isNotEmpty(video.getEnddesc())) {
                                videoDto.setEndDesc(video.getEnddesc());
                            }
                            dto.setVideoInfo(videoDto);
                        }
                        if (!item.getImagesList().isEmpty()) {
                            //image
                            dto.setImgUrls(item.getImagesList());
                        }
                        //tracks
                        List<ResponseTrackDto> tracks = new ArrayList<>();
                        dto.setTracks(tracks);
                        item.getTrackersList().forEach(tracker -> {
                            switch (tracker.getType()) {
                                case 19:
                                    //jingsheng
                                    dto.setWinNoticeUrls(tracker.getUrlsList());
                                    break;
                                case 20:
                                    //jingbai
                                    dto.setFailNoticeUrls(tracker.getUrlsList());
                                    break;
                                case 1:
                                    tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), tracker.getUrlsList()));
                                    break;
                                case 2:
                                    tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), tracker.getUrlsList()));
                                    break;
                                case 3:
                                    tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), tracker.getUrlsList()));
                                    break;
                                case 4:
                                    tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), tracker.getUrlsList()));
                                    break;
                                case 5:
                                    tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), tracker.getUrlsList()));
                                    break;
                                case 6:
                                    tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), tracker.getUrlsList()));
                                    break;
                                case 7:
                                    tracks.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), tracker.getUrlsList()));
                                    break;
                                case 8:
                                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), tracker.getUrlsList()));
                                    break;
                                case 9:
                                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), tracker.getUrlsList()));
                                    break;
                                case 10:
                                    tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), tracker.getUrlsList()));
                                    break;
                                case 11:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), tracker.getUrlsList()));
                                    break;
                                case 12:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), tracker.getUrlsList()));
                                    break;
                                case 13:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), tracker.getUrlsList()));
                                    break;
                                case 14:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), tracker.getUrlsList()));
                                    break;
                                case 15:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), tracker.getUrlsList()));
                                    break;
                                case 16:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), tracker.getUrlsList()));
                                    break;
                                case 17:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), tracker.getUrlsList()));
                                    break;
                                case 18:
                                    tracks.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), tracker.getUrlsList()));
                                    break;

                            }
                        });
                        dto.setTracks(tracks);
                        // 宏替换 统一替换成平台的宏(事件)
                        String finalClickId = clickId;
                        dto.getTracks().forEach(track -> {
                            List<String> urls = track.getTrackUrls();
                            if (StringUtils.isNotEmpty(finalClickId)) {
                                urls = replaceMacro("__CLICKID__", urls, finalClickId);
                            }
                            urls = replaceMacro("__PRICE__", urls, String.valueOf(advPrice));
                            urls = replaceMacro("__REQ_WIDTH__", urls, MacroType.REQ_WIDTH.getCode());
                            urls = replaceMacro("__REQ_HEIGHT__", urls, MacroType.REQ_HEIGHT.getCode());
                            urls = replaceMacro("__WIDTH__", urls, MacroType.WIDTH.getCode());
                            urls = replaceMacro("__HEIGHT__", urls, MacroType.HEIGHT.getCode());
                            urls = replaceMacro("__DOWN_X__", urls, MacroType.DOWN_X.getCode());
                            urls = replaceMacro("__DOWN_Y__", urls, MacroType.DOWN_Y.getCode());
                            urls = replaceMacro("__UP_X__", urls, MacroType.UP_X.getCode());
                            urls = replaceMacro("__UP_Y__", urls, MacroType.UP_Y.getCode());
                            urls = replaceMacro("__ABS_DOWN_X__", urls, MacroType.ABS_DOWN_X.getCode());
                            urls = replaceMacro("__ABS_DOWN_Y__", urls, MacroType.ABS_DOWN_Y.getCode());
                            urls = replaceMacro("__ABS_UP_X__", urls, MacroType.ABS_UP_X.getCode());
                            urls = replaceMacro("__ABS_UP_Y__", urls, MacroType.ABS_UP_Y.getCode());
                            urls = replaceMacro("__DP_WIDTH__", urls, MacroType.DP_WIDTH.getCode());
                            urls = replaceMacro("__DP_HEIGHT__", urls, MacroType.DP_HEIGHT.getCode());
                            urls = replaceMacro("__DP_DOWN_X__", urls, MacroType.DP_DOWN_X.getCode());
                            urls = replaceMacro("__DP_DOWN_Y__", urls, MacroType.DP_DOWN_Y.getCode());
                            urls = replaceMacro("__DP_UP_X__", urls, MacroType.DP_UP_X.getCode());
                            urls = replaceMacro("__DP_UP_Y__", urls, MacroType.DP_UP_Y.getCode());
                            urls = replaceMacro("__SLD__", urls, MacroType.SLD.getCode());
                            urls = replaceMacro("__EVENT_TIME__", urls, MacroType.TIME_SECONDS.getCode());
                            urls = replaceMacro("__CLICK_TIME_START__", urls, MacroType.START_TIME_SECONDS.getCode());
                            urls = replaceMacro("__CLICK_TIME_END__", urls, MacroType.END_TIME_SECONDS.getCode());
                            urls = replaceMacro("__CLICK_ID__", urls, MacroType.CLICK_ID.getCode());
                            urls = replaceMacro("__DURATION__", urls, MacroType.VIDEO_TIME.getCode());
                            urls = replaceMacro("__PLAY_BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());
                            urls = replaceMacro("__PLAY_END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());
                            urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
                            urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());
                            urls = replaceMacro("__PLAY_SCENE__", urls, MacroType.VIDEO_SCENE.getCode());
                            urls = replaceMacro("__PLAY_TYPE__", urls, MacroType.VIDEO_TYPE.getCode());
                            urls = replaceMacro("__PLAY_STATUS__", urls, MacroType.VIDEO_STATUS.getCode());
                            urls = replaceMacro("__TARGET_APP_INSTALL__", urls, MacroType.TARGET_APP_INSTALL.getCode());
                            track.setTrackUrls(urls);
                        });
                        tags.add(dto);
                    });
                }
        );
        return responseDto;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        if (!param.isBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        boolean success = false;
        for (String url : param.getUrls()) {
            HttpResult result = httpClient.get(url, null, null, 5000);
            if (!result.isSuccess()) {
                log.error("notice fail httpCode:{},Url:{}", result.getStatusLine(), url, result.getThrowable());
            }
            if (!success) {
                success = result.isSuccess();
            }
        }
        return success ? SuperResult.rightResult() : SuperResult.badResult();
    }

    private TianQiBidRequest.TianQiApp warpApp(RequestAppDto dto, RtbAdvDto rtbDto) {
        TianQiBidRequest.TianQiApp.Builder app = TianQiBidRequest.TianQiApp.newBuilder();
        app.setId(rtbDto.getAppCode() != null ? rtbDto.getAppCode() : "");
        app.setName(dto.getAppName() != null ? dto.getAppName() : "");
        app.setBundle(dto.getBundle() != null ? dto.getBundle() : "");
        app.setVer(dto.getAppVersion() != null ? dto.getAppVersion() : "");
        app.setDomain(dto.getAppDomainUrl() != null ? dto.getAppDomainUrl() : "");
        app.setStoreurl(dto.getAppstoreUrl() != null ? dto.getAppstoreUrl() : "");
//        app.setKeywords(dto.get)
        return app.build();
    }

    private TianQiBidRequest.TianQiDevice warpDevice(RequestDeviceDto deviceDto, RequestNetworkDto network, RequestGeoDto geo) {
        TianQiBidRequest.TianQiDevice.Builder builder = TianQiBidRequest.TianQiDevice.newBuilder();
        builder.setIp(network.getIp() != null ? network.getIp() : "");
        if (StringUtils.isNotEmpty(network.getIpv6())) {
            builder.setIpv6(network.getIpv6());
        }
        builder.setUa(deviceDto.getUserAgent() != null ? deviceDto.getUserAgent() : "");
//        if (deviceDto.getDnt() != null) {
//            builder.setDnt(deviceDto.getDnt());
//        }
        if (geo != null) {
            builder.setGeo(warpGeo(geo));
        }
        builder.setCarrier(network.getCarrierType() != null ? network.getCarrierType().getType() : 0);
        //网络运营商，1：中国移动，2：中国联通，3：中国电信
        if (null != network.getCarrierType()) {
            if (network.getCarrierType() == CarrierType.CM) {
                builder.setMccmnc("46000");
            } else if (network.getCarrierType() == CarrierType.CU) {
                builder.setMccmnc("46001");
            } else if (network.getCarrierType() == CarrierType.CT) {
                builder.setMccmnc("46003");
            } else {
                builder.setMccmnc("46002");
            }
        }

        // 网络类型：0：未知，1：Ethernet，2：WIFI，3：Unknown Generation，4：2G，5：3G，6：4G，7：5G
        if (null != network.getConnectType()) {
            if (network.getConnectType() == ConnectionType.WIFI) {
                builder.setConnectiontype(2);
            } else if (network.getConnectType() == ConnectionType.NETWORK_2G) {
                builder.setConnectiontype(4);
            } else if (network.getConnectType() == ConnectionType.NETWORK_3G) {
                builder.setConnectiontype(5);
            } else if (network.getConnectType() == ConnectionType.NETWORK_4G) {
                builder.setConnectiontype(6);
            } else if (network.getConnectType() == ConnectionType.NETWORK_5G) {
                builder.setConnectiontype(7);
            } else if (network.getConnectType() == ConnectionType.ETHERNET) {
                builder.setConnectiontype(1);
            } else {
                builder.setConnectiontype(0);
            }
        }
        //    PC(1, "电脑"),
        //    PAD(2, "平板"),
        //    PHONE(3, "手机"),
        //    TV(4, "电视"),
        //    SCREEN(5, "户外屏幕"),
        //    UNKNOWN(999, "其他"),
        switch (deviceDto.getDeviceType().getType()) {
            case 1:
                builder.setDevicetype(4);
                break;
            case 2:
                builder.setDevicetype(2);
                break;
            case 3:
                builder.setDevicetype(1);
                break;
            case 4:
                builder.setDevicetype(3);
                break;
            default:
                builder.setDevicetype(1);

        }
        builder.setMake(deviceDto.getVendor() != null ? deviceDto.getVendor() : "");
        builder.setBrand(deviceDto.getBrand() != null ? deviceDto.getBrand() : "");
        builder.setModel(deviceDto.getModel() != null ? deviceDto.getModel() : "");
        builder.setOrientation(deviceDto.getOrientation() != null ? deviceDto.getOrientation().getType() : 0);

        //设备OS类型，1：iOS，2：Android，3：HarmonyOS
        switch (deviceDto.getOsType().getType()) {
            case 1:
                builder.setOs(2);
                break;
            case 2:
                builder.setOs(1);
                break;
            case 3:
                builder.setOs(4);
                break;
            default:
                builder.setOs(2);

        }

        builder.setOsv(deviceDto.getOsVersion() != null ? deviceDto.getOsVersion() : "");
        if (deviceDto.getApiLevel() != null) {
            builder.setOsl(deviceDto.getApiLevel());
        }
        builder.setW(deviceDto.getWidth() != null ? deviceDto.getWidth() : 0);
        builder.setH(deviceDto.getHeight() != null ? deviceDto.getHeight() : 0);
        builder.setPpi(deviceDto.getPpi() != null ? deviceDto.getPpi() : 0);
//        if (deviceDto.getDpi() != null) {
//            builder.setDpi(deviceDto.getDpi());
//        }
//        if(deviceDto.getPpi()!=null){
//            builder.setDensity(deviceDto.getPpi());
//        }
//        if (StringUtils.isNotEmpty(deviceDto.getHmsAgVersion())) {
//            builder.setHwv(deviceDto.getHwv());
//        }
        if (StringUtils.isNotEmpty(deviceDto.getLanguage())) {
            builder.setLanguage(deviceDto.getLanguage());
        }

        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            builder.setIdfa(deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            builder.setIdfamd5(deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfv())) {
            builder.setIdfv(deviceDto.getIdfv());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOpenUdId())) {
            builder.setOpenudid(deviceDto.getOpenUdId());
        }

        if (deviceDto.getCaids() != null && !deviceDto.getCaids().isEmpty()) {
            RequestCaidDto caidDto = deviceDto.getCaids().get(0);
            if (null != caidDto) {
                if (StringUtils.isNotBlank(caidDto.getCaid())){
                    builder.setCaid(caidDto.getCaid());
                    if (StringUtils.isNotBlank(caidDto.getVersion())){
                        builder.setCaidversion(caidDto.getVersion());
                    }
                }
            }
            deviceDto.getCaids().forEach(caid -> {
                TianQiBidRequest.TianQiCaid.Builder ca = TianQiBidRequest.TianQiCaid.newBuilder();
                if (StringUtils.isNotBlank(caid.getCaid())){
                    ca.setCaid(caid.getCaid());
                    if (StringUtils.isNotBlank(caid.getVersion())){
                        ca.setVersion(caid.getVersion());
                    }
                    builder.addCaidlist(ca);
                }
            });
        }
        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            builder.setImei(deviceDto.getImei());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            builder.setImeimd5(deviceDto.getImeiMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            builder.setAndroidid(deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            builder.setAndroididmd5(deviceDto.getAndroidIdMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            builder.setOaid(deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaidMd5())) {
            builder.setOaidmd5(deviceDto.getOaidMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getPaid())) {
            builder.setPaid(deviceDto.getPaid());
        }
        builder.setMac(network.getMac() != null ? network.getMac() : "");
        if (StringUtils.isNotEmpty(network.getMacMd5())) {
            builder.setMacmd5(network.getMacMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImsi())) {
            builder.setImsi(deviceDto.getImsi());
        }
//        if (StringUtils.isNotEmpty(deviceDto.getSsid())) {
//            builder.setSsid(deviceDto.getSsid());
//        }
//        if (StringUtils.isNotEmpty(deviceDto.getWifiMac())) {
//            builder.setWifimac(deviceDto.getWifiMac());
//        }
        if (StringUtils.isNotEmpty(deviceDto.getRomVersion())) {
            builder.setRomver(deviceDto.getRomVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysCompileTime())) {
            builder.setSyscompilingtime(deviceDto.getSysCompileTime());
        }

        if (StringUtils.isNotEmpty(deviceDto.getAppStoreVersion())) {
            builder.setAppstoreversion(deviceDto.getAppStoreVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getBootMark())) {
            builder.setBootmark(deviceDto.getBootMark());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUpdateMark())) {
            builder.setUpdatemark(deviceDto.getUpdateMark());
        }
        if (StringUtils.isNotEmpty(deviceDto.getTimeZone())) {
            builder.setTimezone(deviceDto.getTimeZone());
        }
        if (StringUtils.isNotEmpty(deviceDto.getCountry())) {
            builder.setCountry(deviceDto.getCountry());
        }
        if (StringUtils.isNotEmpty(deviceDto.getModelCode())) {
            builder.setModelcode(deviceDto.getModelCode());
        }
        if (StringUtils.isNotEmpty(deviceDto.getDeviceNameMd5())) {
            builder.setDevicenamemd5(deviceDto.getDeviceNameMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareMachine())) {
            builder.setHardwaremachine(deviceDto.getHardwareMachine());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHardwareModel())) {
            builder.setHardwaremodel(deviceDto.getHardwareModel());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysInitTime())) {
            builder.setInittime(deviceDto.getSysInitTime());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysStartTime())) {
            builder.setStartuptime(deviceDto.getSysStartTime());
        }
        if (StringUtils.isNotEmpty(deviceDto.getSysUpdateTime())) {
            builder.setUpgradetime(deviceDto.getSysUpdateTime());
        }
        if (deviceDto.getDeviceMemory() != null && deviceDto.getDeviceMemory() > 0L) {
            builder.setPhysicalmemory(deviceDto.getDeviceMemory().toString());
        }
        if (deviceDto.getDeviceHardDisk() != null && deviceDto.getDeviceHardDisk() > 0L) {
            builder.setHarddisk(deviceDto.getDeviceHardDisk().toString());
        }
        if (deviceDto.getCpuNum() != null && deviceDto.getCpuNum() > 0) {
            builder.setCpunum(deviceDto.getCpuNum());
        }
        if (deviceDto.getCpuFreq() != null && deviceDto.getCpuFreq() > 0D) {
            builder.setCpufreq(deviceDto.getCpuFreq().floatValue());
        }
        if (deviceDto.getIdfaPolicy() != null) {
            builder.setIdfapolicy(deviceDto.getIdfaPolicy());
        }
        if (deviceDto.getBatteryStatus() != null) {
            builder.setBatterystatus(deviceDto.getBatteryStatus());
        }
        if (deviceDto.getBatteryPower() != null) {
            builder.setBatterypower(deviceDto.getBatteryPower());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsVersion())) {
            builder.setHmsversion(deviceDto.getHmsVersion());
        }
        if (StringUtils.isNotEmpty(deviceDto.getHmsAgVersion())) {
            builder.setAgversion(deviceDto.getHmsAgVersion());
        }
        if (deviceDto.getInstalledAppInfo() != null && !deviceDto.getInstalledAppInfo().isEmpty()) {
            deviceDto.getInstalledAppInfo().forEach(app -> {
                if(StringUtils.isNotBlank(app.getAppName())) {
                    builder.addApplist(app.getAppName());
                }
            });
        }
        return builder.build();
    }

    private TianQiBidRequest.TianQiImp warpTag(RtbRequestDto requestDto, RtbAdvDto dto) {
        TianQiBidRequest.TianQiImp.Builder tag = TianQiBidRequest.TianQiImp.newBuilder();
        RequestTagDto tagDto = requestDto.getTag();
        tag.setId(requestDto.getReqId());
        tag.setTagid(dto.getTagCode() != null ? dto.getTagCode() : "");
        if (null != tagDto.getWidth()) {
            tag.setW(tagDto.getWidth());
        }
        if (null != tagDto.getHeight()) {
            tag.setH(tagDto.getHeight());
        }
        tag.setVideo(warpVideo(requestDto, dto));
        if (null != dto.getPrice()) {
            tag.setBidfloor(dto.getPrice().intValue());
        }
        return tag.build();
    }


    private TianQiBidRequest.TianQiImp.TianQiVideo warpVideo(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        TianQiBidRequest.TianQiImp.TianQiVideo.Builder tianQiVideo = TianQiBidRequest.TianQiImp.TianQiVideo.newBuilder();
        RequestTagDto tagDto = rtbDto.getTag();
        if (null != tagDto.getHeight()) {
            tianQiVideo.setH(tagDto.getHeight());
        }
        if (null != tagDto.getWidth()) {
            tianQiVideo.setW(tagDto.getWidth());
        }
        if (null != tagDto.getMinDuration()) {
            tianQiVideo.setMinduration(tagDto.getMinDuration());
        }
        if (null != tagDto.getMaxDuration()) {
            tianQiVideo.setMaxduration(tagDto.getMaxDuration());
        }
        List<String> list = new ArrayList<>();
        list.add("video/mp4");
        list.add("video/x-flv");
        list.add("video/3gpp");
        list.add("video/x-msvideo");
        list.add("video/x-ms-wmv");
        list.add("video/quicktime");
        tianQiVideo.addAllMimes(list);
        return tianQiVideo.build();
    }

    private TianQiBidRequest.TianQiGeo warpGeo(RequestGeoDto dto) {
        TianQiBidRequest.TianQiGeo.Builder geo = TianQiBidRequest.TianQiGeo.newBuilder();
        if (null != dto.getLatitude()) {
            geo.setLat(dto.getLatitude().floatValue());
        }
        if (null != dto.getLongitude()) {
            geo.setLon(dto.getLongitude().floatValue());
        }
        return geo.build();
    }


    private TianQiBidRequest.TianQiUser warpUser(RequestUserDto dto) {
        TianQiBidRequest.TianQiUser.Builder user = TianQiBidRequest.TianQiUser.newBuilder();
        if (dto.getUserId() != null) {
            user.setId(dto.getUserId());
            user.setBuyeruid(dto.getUserId());
        }
        if (null != dto.getAge()) {
            user.setYob(calculateBirthYear(dto.getAge()));
        }
        if (dto.getGender() != null) {
            user.setGender(dto.getGender());
        }
        if (dto.getInterest() != null) {
            List<String> ins = new ArrayList<String>(Arrays.asList(dto.getInterest()));
            user.setKeywords(StringUtils.join(ins, ","));
        }
        return user.build();
    }

    public static int calculateBirthYear(int age) {
        int currentYear = LocalDate.now().getYear();
        return currentYear - age;
    }
}
