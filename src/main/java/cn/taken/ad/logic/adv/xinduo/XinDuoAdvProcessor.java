package cn.taken.ad.logic.adv.xinduo;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.number.BigDecimalUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.xinduo.dto.AdRequest;
import cn.taken.ad.logic.adv.xinduo.dto.AdResponse;
import cn.taken.ad.logic.adv.xinduo.dto.Caid;
import cn.taken.ad.logic.adv.xinduo.dto.VideoBase;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.utils.ParamParser;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component("XINDUO" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class XinDuoAdvProcessor implements AdvProcessor {

    public static final String API_VERSION = "apiVersion";
    public static final String AES_KEY = "secretKey";

    private static final Logger log = LoggerFactory.getLogger(XinDuoAdvProcessor.class);

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        StringBuffer deviceIdBuf = new StringBuffer();
        AdRequest.Builder builder = builderRequest(rtbDto, advDto,deviceIdBuf);
        AdRequest request = builder.build();
        advDto.setReqObj(request);
        byte[] reqBytes = request.toByteArray();
        // 请求地址
        HttpResult httpResult = httpClient.postBytes(advDto.getRtburl(), reqBytes, new Header[]{new BasicHeader("Content-Type", "application/protobuf"), new BasicHeader("Content-Encoding", "gzip"), new BasicHeader("Accept-Encoding", "gzip")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        // 解析响应
        return parseResponse(rtbDto, httpResult, deviceIdBuf.toString(), advDto,httpClient);
    }


    private AdRequest.Builder builderRequest(RtbRequestDto rtbDto, RtbAdvDto advDto, StringBuffer deviceIdBuf) {
        AdRequest.Builder request = AdRequest.newBuilder();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        RequestAppDto appDto = rtbDto.getApp();
        RequestGeoDto geoDto = rtbDto.getGeo();
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());
        Map<String, String> appParam = ParamParser.parseParamByJson(advDto.getAppPnyParam());
        String appKey = appParam.get(AES_KEY);
        if (StringUtils.isEmpty(appKey)) {
            appKey = param.getOrDefault(AES_KEY, "");
        }
        request.setProtocol(param.get(API_VERSION));
        request.setAppId(advDto.getAppCode());
        request.setSlotId(advDto.getTagCode());
        request.setIsDeepLink(1);

        OsType osType = deviceDto.getOsType();
        boolean isIos = false;
        boolean isAndroid = false;
        boolean isHongMeng = false;
        if (null == osType) {
            request.setOs(3);
        } else {
            switch (osType) {
                case ANDROID:
                    request.setOs(0);
                    isAndroid = true;
                    break;
                case IOS:
                    request.setOs(1);
                    isIos = true;
                    break;
                case WINDOWS_PHONE:
                    request.setOs(2);
                    break;
                case WINDOWS_PC:
                    request.setOs(4);
                    break;
                case HARMONY:
                    request.setOs(5);
                    isHongMeng = true;
                    break;
                default:
                    request.setOs(3);
                    break;
            }
        }
        // 暂无 pos
        request.setOsVersionName(StringUtils.isNotBlank(deviceDto.getOsVersion()) ? deviceDto.getOsVersion() : "");
        // 暂无 osVersionCode
        request.setBrand(StringUtils.isNotBlank(deviceDto.getBrand()) ? deviceDto.getBrand() : "");
        request.setModel(StringUtils.isNotBlank(deviceDto.getModel()) ? deviceDto.getModel() : "");
        if (StringUtils.isNotBlank(deviceDto.getVendor())) {
            request.setMake(deviceDto.getVendor());
        }
        if (StringUtils.isNotBlank(deviceDto.getOaid())) {
            request.setOaid(deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImsi())) {
            request.setImsi(deviceDto.getImsi());
        } else {

            CarrierType carrierType = networkDto.getCarrierType();
            if (carrierType != CarrierType.UNKNOWN && carrierType != CarrierType.OTHER) {
                switch (carrierType) {
                    case CM:
                        request.setImsi("46000");
                        break;
                    case CU:
                        request.setImsi("46001");
                        break;
                    case CT:
                        request.setImsi("46003");
                        break;
                }
            }
        }
        if (isAndroid) {
            if (StringUtils.isNotEmpty(deviceDto.getImei())) {
                request.setUuid(deviceDto.getImei());
                deviceIdBuf.append(deviceDto.getImei());
            } else {
                request.setUuid("");
            }
            if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
                request.setUuidMd5(deviceDto.getImeiMd5());
            } else {
                request.setUuidMd5("");
            }
            if (deviceIdBuf.length() == 0) {
                if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
                    deviceIdBuf.append(deviceDto.getOaid());
                } else if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
                    deviceIdBuf.append(deviceDto.getAndroidId());
                }
            }
        } else if (isIos) {
            if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
                request.setUuid(deviceDto.getIdfa());
                deviceIdBuf.append(deviceDto.getIdfa());
            } else {
                request.setUuid("");
            }
            if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
                request.setUuidMd5(deviceDto.getIdfaMd5());
            }
        }

        if (deviceIdBuf.length() == 0 && StringUtils.isNotEmpty(networkDto.getMac())) {
            deviceIdBuf.append(networkDto.getMac());
        }
        if (StringUtils.isNotBlank(deviceDto.getAndroidId())){
            request.setAndroidId(deviceDto.getAndroidId());
        }
        if (StringUtils.isNotBlank(deviceDto.getAndroidIdMd5())) {
            request.setAndroidIdMd5(deviceDto.getAndroidIdMd5());
        }
        // 暂无 iccid
        ConnectionType connectionType = networkDto.getConnectType();
        switch (connectionType) {
            case NETWORK_2G:
                request.setNetwork("2g");
                break;
            case NETWORK_3G:
                request.setNetwork("3g");
                break;
            case NETWORK_4G:
                request.setNetwork("4g");
                break;
            case NETWORK_5G:
                request.setNetwork("5g");
                break;
            case WIFI:
                request.setNetwork("wifi");
                break;
            default:
                request.setNetwork("未知");
                break;
        }
        if (StringUtils.isNotBlank(networkDto.getMac())){
            request.setMac(networkDto.getMac());
        }
        if (StringUtils.isNotBlank(networkDto.getMacMd5())) {
            request.setMacMd5(networkDto.getMacMd5());
        }
        if (StringUtils.isNotBlank(deviceDto.getIdfv())) {
            request.setIdfv(deviceDto.getIdfv());
        }
        if (StringUtils.isNotBlank(deviceDto.getOpenUdId())) {
            request.setOpenUDID(deviceDto.getOpenUdId());
        }

        if (null != deviceDto.getCaids() && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto caid : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(caid.getCaid())) {
                    Caid.Builder cd = Caid.newBuilder();
                    cd.setCaid(caid.getCaid());
                    cd.setVersion(StringUtils.isNotEmpty(caid.getVersion()) ? caid.getVersion() : "");
                    if (caid.getVendor() != null){
                        switch (caid.getVendor().getType()) {
                            case 1:
                                cd.setVendor(0);
                                break;
                            case 2:
                                cd.setVendor(1);
                                break;
                            case 3:
                                cd.setVendor(2);
                                break;
                        }
                    }
                    if (null != caid.getTimestamp()){
                        cd.setGenerateTime(caid.getTimestamp().toString());
                    }
                    request.addCaids(cd);
                }
            }
        }
        request.setSysComplingTime(StringUtils.isNotEmpty(deviceDto.getSysCompileTime()) ? deviceDto.getSysCompileTime() : "");
        if (StringUtils.isNotBlank(deviceDto.getSysElapseTime())) {
            request.setElapseTime(deviceDto.getSysElapseTime());
        }
        request.setDeviceFileTime(StringUtils.isNotEmpty(deviceDto.getSysInitTime()) ? deviceDto.getSysInitTime() : "");

        if (null != deviceDto.getSysUpdateTime()) {
            request.setSysUpdateTime(deviceDto.getSysUpdateTime());
        } else {
            request.setSysUpdateTime("");
        }
        if (null != deviceDto.getSysStartTime()) {
            request.setDeviceStartTime(deviceDto.getSysStartTime());
        } else {
            request.setDeviceStartTime("");
        }
        request.setBootMark(StringUtils.isNotEmpty(deviceDto.getBootMark()) ? deviceDto.getBootMark() : "");
        request.setUpdateMark(StringUtils.isNotEmpty(deviceDto.getUpdateMark()) ? deviceDto.getUpdateMark() : "");
        if (StringUtils.isNotEmpty(deviceDto.getDeviceName())) {
            request.setDeviceName(deviceDto.getDeviceName());
        }
        if (StringUtils.isNotBlank(deviceDto.getDeviceNameMd5())) {
            request.setDeviceNameMd5(deviceDto.getDeviceNameMd5());
        }
        if (deviceDto.getDeviceMemory() != null) {
            request.setDeviceMemory(String.valueOf(deviceDto.getDeviceMemory()));
        }
        if (deviceDto.getDeviceHardDisk() != null) {
            request.setDeviceHardDisk(String.valueOf(deviceDto.getDeviceHardDisk()));
        }
        if (StringUtils.isNotBlank(deviceDto.getHardwareMachine())) {
            request.setHardwareMachine(deviceDto.getHardwareMachine());
        }
        if (StringUtils.isNotBlank(deviceDto.getHardwareModel())) {
            request.setHardwareModel(deviceDto.getHardwareModel());
        }
        if (StringUtils.isNotBlank(deviceDto.getTimeZone())) {
            request.setTimeZone(deviceDto.getTimeZone());
        }
        if (StringUtils.isNotBlank(deviceDto.getLocalName())) {
            request.setLocalName(deviceDto.getLocalName());
        }
        if (StringUtils.isNotBlank(deviceDto.getLanguage())) {
            request.setLanguage(deviceDto.getLanguage());
        }
        if (StringUtils.isNotBlank(deviceDto.getCountry())) {
            request.setCountry(deviceDto.getCountry());
        }
        if (deviceDto.getCpuNum() != null) {
            request.setCpuNum(deviceDto.getCpuNum());
        }
        if (null != deviceDto.getCpuFreq()) {
            request.setCpuFreq(deviceDto.getCpuFreq().floatValue());
        }
        if (null == deviceDto.getIdfaPolicy()) {
            request.setIdfaPolicy(0);
        } else if (1 == deviceDto.getIdfaPolicy()) {
            request.setIdfaPolicy(0);
        } else if (2 == deviceDto.getIdfaPolicy()) {
            request.setIdfaPolicy(1);
        } else if (3 == deviceDto.getIdfaPolicy()) {
            request.setIdfaPolicy(2);
        } else if (4 == deviceDto.getIdfaPolicy()) {
            request.setIdfaPolicy(3);
        }
        if (null == deviceDto.getBatteryStatus()) {
            request.setBatteryStatus(0);
        } else {
            request.setBatteryStatus(deviceDto.getBatteryStatus());
        }
        if (null != deviceDto.getBatteryPower()) {
            request.setBatteryPower(deviceDto.getBatteryPower());
        }
        if (StringUtils.isNotBlank(deviceDto.getSerialNO())) {
            request.setSerialNO(deviceDto.getSerialNO());
        }
        if (null != deviceDto.getWidth()){
            request.setScreenWidth(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()){
            request.setScreenHeight(deviceDto.getHeight());
        }
        if (null != deviceDto.getScreenInch()) {
            request.setScreenInch(deviceDto.getScreenInch().floatValue());
        }
        if (null != deviceDto.getScreenDensity()) {
            request.setScreenDensity(deviceDto.getScreenDensity().floatValue());
        }
        if (null != deviceDto.getPpi()){
            request.setPpi(deviceDto.getPpi());
        }
        request.setAppName(StringUtils.isNotBlank(appDto.getAppName()) ? appDto.getAppName() : "");
        request.setPackageName(StringUtils.isNotBlank(appDto.getBundle()) ? appDto.getBundle() : "");
        request.setAppVersionName(StringUtils.isNotBlank(appDto.getAppVersion()) ? appDto.getAppVersion() : "");
        if (StringUtils.isNotBlank(appDto.getAppVersionCode())){
            request.setAppVersionCode(Integer.parseInt(appDto.getAppVersionCode()));
        }
        // 暂无 appVersionCode 应用程序版本号
        request.setOrientation(0);
        if (null != deviceDto.getOrientation()) {
            if (OrientationType.VERTICAL == deviceDto.getOrientation()) {
                request.setOrientation(1);
            } else if (OrientationType.HORIZONTAL == deviceDto.getOrientation()) {
                request.setOrientation(2);
            }
        }
        request.setUa(StringUtils.isNotBlank(deviceDto.getUserAgent()) ? deviceDto.getUserAgent() : "");
        request.setIp(StringUtils.isNotBlank(networkDto.getIp()) ? networkDto.getIp() : "");
        if (StringUtils.isNotBlank(networkDto.getIpv6())) {
            request.setIpv6(networkDto.getIpv6());
        }
        request.setTimestamp(System.currentTimeMillis() + "");
        request.setSign(sign(request, appKey));
        CoordinateType coordinateType = geoDto.getCoordinateType();
        if (null != coordinateType) {
            switch (coordinateType) {
                case GLOBAL:
                    request.setCoordinateType(1);
                case STATE:
                    request.setCoordinateType(2);
                    break;
                case BAIDU:
                    request.setCoordinateType(3);
                    break;
                default:
                    request.setCoordinateType(0);
                    break;
            }
        }
        // 暂无 laccu 设备定位精准度 0：定位精准，可以获取到小数点4位及以上 1：定位不准确
        if (null != geoDto.getLongitude()) {
            request.setLongitude(geoDto.getLongitude().toString());
        }
        if (null != geoDto.getLatitude()) {
            request.setLatitude(geoDto.getLatitude().toString());
        }
        List<RequestInstalledAppDto> installedAppDtos = rtbDto.getDevice().getInstalledAppInfo();
        if (null != installedAppDtos) {
            installedAppDtos.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getPackageName())) {
                    request.addAppList(item.getPackageName());
                }
            });

        }
        request.setSsid(StringUtils.isNotEmpty(networkDto.getSsid()) ? networkDto.getSsid() : "");
        request.setWifiMac(StringUtils.isNotEmpty(networkDto.getWifiMac()) ? networkDto.getWifiMac() : "");
        request.setRomVersion(StringUtils.isNotEmpty(deviceDto.getRomVersion()) ? deviceDto.getRomVersion() : "");
        if (StringUtils.isNotBlank(deviceDto.getSysUiVersion())) {
            request.setSysUiVersion(deviceDto.getSysUiVersion());
        }
        if (StringUtils.isNotBlank(deviceDto.getAppStoreVersion())) {
            request.setAppstoreVersion(deviceDto.getAppStoreVersion());
        }
        if (StringUtils.isNotBlank(deviceDto.getHmsVersion())) {
            request.setHmsCore(deviceDto.getHmsVersion());
        }
        request.setCookie(StringUtils.isNotEmpty(deviceDto.getCookie()) ? deviceDto.getCookie() : "");
        request.setReferer(StringUtils.isNotEmpty(deviceDto.getReferer()) ? deviceDto.getReferer() : "");
        DeviceType deviceType = deviceDto.getDeviceType();
        if (null == deviceType) {
            request.setDeviceType(99);
        } else {
            switch (deviceType) {
                case PC:
                    request.setDeviceType(1);
                    break;
                case PHONE:
                    if (isIos) {
                        request.setDeviceType(34);
                    } else if (isAndroid) {
                        request.setDeviceType(32);
                    } else if (isHongMeng) {
                        request.setDeviceType(42);
                    }
                    break;
                case PAD:
                    if (isIos) {
                        request.setDeviceType(33);
                    } else if (isAndroid) {
                        request.setDeviceType(31);
                    } else if (isHongMeng) {
                        request.setDeviceType(41);
                    }
                    break;
                //case TV:
            }
        }
        // c2s 是否客户端接入该接口文档，不填默认0 0：否 1：是
        request.setC2S(0);
        if (null != rtbDto.getTag().getPrice()) {
            request.setChargeType(2);
            // 分 转 元
            BigDecimal price = BigDecimalUtils.div(BigDecimal.valueOf(rtbDto.getTag().getPrice()), new BigDecimal(100), 3);
            request.setPrice(price.doubleValue());
        }
        request.setPaid(StringUtils.isNotEmpty(deviceDto.getPaid()) ? deviceDto.getPaid() : "");
        request.setAaid(StringUtils.isNotEmpty(deviceDto.getAaid()) ? deviceDto.getAaid() : "");
        return request;
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, HttpResult httpResult, String deviceId, RtbAdvDto advDto,FastHttpClient httpClient) throws Exception {
        byte[] data = httpResult.getData();
        AdResponse response = AdResponse.parseFrom(data);
        advDto.setRespObj(response);
        if (0 != response.getCode()) {
            if (4012 == response.getCode() || 4016 == response.getCode()) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response.getCode() + "");
            }
        }
        if (response.getAdsList().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }

        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "0");
        response.getAdsList().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            tagResponseDto.setTitle(tag.getTitle());
            tagResponseDto.setDesc(tag.getDesc());
            tagResponseDto.setLogoUrl(tag.getAdLogoUrl());
            tagResponseDto.setIconUrl(tag.getIconUrl());
            if (!tag.getImgUrlsList().isEmpty()) {
                tagResponseDto.setImgUrls(new ArrayList<>(tag.getImgUrlsList()));
            }
            tagResponseDto.setClickUrl(tag.getClickUrl());
            // marketUrl 厂商应用商店下载页链接，仅 Android 端返回。参考 《APP 媒体处理逻辑》第 4 点
            tagResponseDto.setMarketUrl(tag.getMarketUrl());
            // universallink iOS调起链接。参考《APP媒体处理逻辑》第4点
            tagResponseDto.setUniversalLink(tag.getUniversallink());
            tagResponseDto.setDeepLinkUrl(tag.getDeeplink());
            ResponseAppDto appDto = new ResponseAppDto();
            appDto.setAppName(tag.getAppName());
            appDto.setPackageName(tag.getPackageName());
            appDto.setRating(tag.getRating());
            appDto.setRatingCount(tag.getRatingCount());
            if (StringUtils.isNotEmpty(tag.getAppSize())) {
                appDto.setAppSize(Long.parseLong(tag.getAppSize()));
            }
            appDto.setAppVersion(tag.getAppVersion());
            appDto.setAppDeveloper(tag.getAppDeveloper());
            appDto.setAppPrivacyUrl(tag.getAppPrivacyUrl());
            if (StringUtils.isNotEmpty(tag.getAppPermContent())) {
                if (tag.getAppPermContent().startsWith("http")) {
                    appDto.setAppPermissionInfoUrl(tag.getAppPermContent());
                } else {
                    appDto.setAppPermContent(tag.getAppPermContent());
                }
            }
            tagResponseDto.setAppInfo(appDto);
            tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            // 暂无 clickPosition 0：不用特殊处理 1：需要特殊处理类广告(标识广点通专用落地页，处理方式请参考《需要二次访问的广告处理业务说明》
            String clickId = null;
            if (tag.getClickPosition() == 1) {
                HttpResult result = httpClient.get(tag.getClickUrl(), null, null, advDto.getTimeout());
                if (result.isSuccess()) {
                    JsonObject obj = JsonHelper.fromJson(JsonElement.class, result.getDataStringUTF8()).getAsJsonObject();
                    if (obj.get("ret").getAsInt() == 0) {
                        JsonObject dd = obj.get("data").getAsJsonObject();
                        tagResponseDto.setClickUrl(dd.get("dstlink").getAsString());
                        clickId = dd.get("clickid").getAsString();
                    }
                }
            }
            if (1 == tag.getActionType()) {
                tagResponseDto.setActionType(ActionType.DOWNLOAD);
            } else if (2 == tag.getActionType()) {
                tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            } else if (3 == tag.getActionType()) {
                tagResponseDto.setActionType(ActionType.SYSTEM_BROWSER_H5);
            }
            switch (tag.getMaterialType()) {
                case 1:
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                    break;
                case 2:
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                    break;
                case 3:
                    tagResponseDto.setMaterialType(MaterialType.TEXT);
                    break;
                case 4:
                    tagResponseDto.setMaterialType(MaterialType.HTML);
                    break;
            }
            tagResponseDto.setHtmlContent(tag.getHtmlBody());
            // 竟胜
            List<String> winNoticeUrl = new ArrayList<>();
            // 事件列表
            List<ResponseTrackDto> tracks = new ArrayList<>();
            if (!tag.getTracksList().isEmpty()) {
                tag.getTracksList().forEach(track -> {
                    if (track.getUrlsList().isEmpty()) {
                        return;
                    }
                    List<String> trackUrls = new ArrayList<>(track.getUrlsList());
                    String methodType = track.getMethod();
                    String contentType = track.getContentType();
                    String content = track.getContent();
                    switch (track.getType()) {
                        case 1:
                            tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 2:
                            tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 3:
                            tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 4:
                            tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 5:
                            tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 6:
                            tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 7:
                            tracks.add(new ResponseTrackDto(EventType.INSTALLED_OPEN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 8:
                            tracks.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 15:
                            winNoticeUrl.addAll(trackUrls);
                            break;
                        case 16:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_FAIL.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 17:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 18:
                            tracks.add(new ResponseTrackDto(EventType.DEEPLINK_START.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 31:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 32:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 33:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 34:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 35:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 36:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SKIP.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 37:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_FULL_SCREEN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 38:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_EXITS_FULL_SCREEN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 39:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_SUCCESS.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 40:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_LOADED_FAIL.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 41:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_MUTE.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 42:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_UNMUTE.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 43:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 44:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 45:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_ERROR.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 46:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_REPLAY.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 47:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_UP.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 48:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_SLIDE_DOWN.getType(), trackUrls, methodType, contentType, content));
                            break;
                        case 49:
                            tracks.add(new ResponseTrackDto(EventType.VIDEO_PLAY_EDN_DISPLAYED.getType(), trackUrls, methodType, contentType, content));
                            break;
                    }
                });
            }
            tagResponseDto.setTracks(tracks);
            tagResponseDto.setWinNoticeUrls(winNoticeUrl);
            // 暂无 video_type 视频广告类型 ID，默认为 0
            // 视频
            ResponseVideoDto responseVideoDto = new ResponseVideoDto();
            VideoBase video = tag.getVideoBase();
            if (null != video) {
                responseVideoDto.setDuration((int) video.getDuration());
                responseVideoDto.setVideoSize((long) video.getVideoSize());
                responseVideoDto.setVideoUrl(video.getVideoUrl());
                responseVideoDto.setVideoWidth(video.getVideoWidth());
                responseVideoDto.setVideoHeight(video.getVideoHeight());
                if (!video.getCoverImgUrlList().isEmpty()) {
                    responseVideoDto.setCoverImgUrls(new ArrayList<>(video.getCoverImgUrlList()));
                }
                responseVideoDto.setButtonText(video.getButtonText());
                if (!video.getEndImgUrlList().isEmpty()) {
                    responseVideoDto.setEndImgUrls(new ArrayList<>(video.getEndImgUrlList()));
                }
                responseVideoDto.setEndHtml(video.getEndHtml());
                responseVideoDto.setAutoLanding(Boolean.TRUE.equals(video.getAutoLanding()));
                responseVideoDto.setPrefetch(Boolean.TRUE.equals(video.getPrefetch()));
                responseVideoDto.setClickAble(Boolean.TRUE.equals(video.getClickAble()));
                responseVideoDto.setSkipSeconds(video.getSkipSeconds());
                responseVideoDto.setEndButtonText(video.getEndButtonText());
                responseVideoDto.setEndIconUrl(video.getEndIconUrl());
                responseVideoDto.setEndTitle(video.getEndTitle());
                responseVideoDto.setEndDesc(video.getEndDesc());
            }
            tagResponseDto.setVideoInfo(responseVideoDto);
            // 转为分
            tagResponseDto.setPrice(BigDecimalUtils.mul(BigDecimal.valueOf(tag.getPrice()), new BigDecimal(100)).doubleValue());
            if (!tag.getClickAreaReportUrlList().isEmpty()) {
                tagResponseDto.setClickAreaReportUrls(new ArrayList<>(tag.getClickAreaReportUrlList()));
            }
            // 宏替换
            RequestDeviceDto deviceDto = rtbDto.getDevice();
            RequestNetworkDto networkDto = rtbDto.getNetwork();
            String finalClickId = clickId;
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = replaceAllMacro(track.getTrackUrls(), deviceDto, networkDto, deviceId, finalClickId);
                track.setTrackUrls(urls);
            });
            // 点击链接有可能存在宏 这里替换
            if (StringUtils.isNotEmpty(tagResponseDto.getClickUrl())) {
                String clickUrl = replaceAllMacro(new ArrayList<>(Collections.singletonList(tagResponseDto.getClickUrl())), deviceDto, networkDto, deviceId,clickId).get(0);
                tagResponseDto.setClickUrl(clickUrl);
            }
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }

    private List<String> replaceAllMacro(List<String> urls, RequestDeviceDto deviceDto, RequestNetworkDto networkDto, String deviceId, String clickId) {
        // __WIDTH__ __HEIGHT__ 与广告主一致 不需要替换
        urls = replaceMacro("_XDKDX_", urls, MacroType.DOWN_X.getCode());
        urls = replaceMacro("_XDKDY_", urls, MacroType.DOWN_Y.getCode());
        urls = replaceMacro("_XDKUX_", urls, MacroType.UP_X.getCode());
        urls = replaceMacro("_XDKUY_", urls, MacroType.UP_Y.getCode());
        urls = replaceMacro("_XDKDX-ABS_", urls, MacroType.ABS_DOWN_X.getCode());
        urls = replaceMacro("_XDKDY-ABS_", urls, MacroType.ABS_DOWN_Y.getCode());
        urls = replaceMacro("_XDKUX-ABS_", urls, MacroType.ABS_UP_X.getCode());
        urls = replaceMacro("_XDKUY-ABS_", urls, MacroType.ABS_UP_Y.getCode());
        urls = replaceMacro("_WIDTH_", urls, MacroType.WIDTH.getCode());
        urls = replaceMacro("_HEIGHT_", urls, MacroType.HEIGHT.getCode());
        urls = replaceMacro("_EVENT_TIME_START_", urls, MacroType.START_TIME.getCode());
        urls = replaceMacro("_EVENT_TIME_END_", urls, MacroType.END_TIME.getCode());
        urls = replaceMacro("_EVENT_TIME_SECOND_", urls, MacroType.START_TIME_SECONDS.getCode());
        urls = replaceMacro("_EVENT_TIME_SEC_END_", urls, MacroType.END_TIME_SECONDS.getCode());
        urls = replaceMacro("__BEHAVIOR__", urls, MacroType.VIDEO_BEHAVIOR.getCode());
        urls = replaceMacro("__BEGIN_TIME__", urls, MacroType.VIDEO_BEGIN_TIME.getCode());
        urls = replaceMacro("__END_TIME__", urls, MacroType.VIDEO_END_TIME.getCode());
        urls = replaceMacro("__PLAY_FIRST_FRAME__", urls, MacroType.VIDEO_PLAY_FIRST_FRAME.getCode());
        urls = replaceMacro("__PLAY_LAST_FRAME__", urls, MacroType.VIDEO_PLAY_LAST_FRAME.getCode());
        urls = replaceMacro("__SCENE__", urls, MacroType.VIDEO_SCENE.getCode());
        urls = replaceMacro("__PLAY_TYPE__", urls, MacroType.VIDEO_TYPE.getCode());
        urls = replaceMacro("__STATUS__", urls, MacroType.VIDEO_STATUS.getCode());
        urls = replaceMacro("__PLAY_SECOND__", urls, MacroType.VIDEO_PROGRESS_SEC.getCode());
        urls = replaceMacro("__PLAY_MILLSECOND__", urls, MacroType.VIDEO_PROGRESS.getCode());
        // 暂无 __VIDEO_DURATION__ 视频总时长
        // 暂无 __CLICK_ID__ 二次下载点击宏替换
        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            urls = replaceMacro("__IDFA__", urls, deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            urls = replaceMacro("__IMEI__", urls, deviceDto.getImei());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            urls = replaceMacro("__IMEIMD5__", urls, deviceDto.getImeiMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            urls = replaceMacro("__IDFAMD5__", urls, deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(networkDto.getIp())) {
            urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
            urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            // 不为空时替换
            urls = replaceMacro("__OAID__", urls, deviceDto.getOaid());
        }
        String mac = networkDto.getMac();
        if (StringUtils.isNotEmpty(mac)) {
            urls = replaceMacro("__MAC__", urls, mac.replace(":", "").replace("-", ""));
            urls = replaceMacro("__MAC1__", urls, mac.replace("-", ":"));
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            urls = replaceMacro("__ANDROIDID__", urls, deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            urls = replaceMacro("__ANDROIDIDMD5__", urls, deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAaid())) {
            urls = replaceMacro("__ALL_AAID__", urls, deviceDto.getAaid());
        }
        String caid = "";
        if (deviceDto.getCaids() != null && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto deviceDtoCaid : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(deviceDtoCaid.getCaid())) {
                    caid = deviceDtoCaid.getCaid();
                    break;
                }
            }
        }
        if (StringUtils.isNotEmpty(caid)) {
            urls = replaceMacro("__CAID__", urls, caid);
        }

        urls = replaceMacro("__DPWIDTH__", urls, MacroType.DP_WIDTH.getCode());
        urls = replaceMacro("__DPHEIGHT__", urls, MacroType.DP_HEIGHT.getCode());
        // 以下宏 与平台一致 不需要替换
        // __DP_DOWN_X__ __DP_DOWN_Y__ __DP_UP_X__  __DP_UP_Y__
        // __SLD__ __X_MAX_ACC__ __Y_MAX_ACC__ __Z_MAX_ACC__ __TURN_X__ __TURN_Y__ __TURN_Z__ __TURN_TIME__
        // __BUTTON_LUX__ __BUTTON_LUY__ __BUTTON_RDX__ __BUTTON_RDY__ __DISPLAY_LUX__ __DISPLAY_LUY__ __DISPLAY_RDX__ __DISPLAY_RDY__
        // 暂无 __MUID__  媒体用户标识
        // 暂无 __REQUESTID__ 请求ID，唯一标识

        // 设备ID 操作系统为Android时，IMEI、OAID、AndroidID、MAC按 从左⾄右优先级顺序进⾏替换； 操作系统为iOS时，IDFA、MAC按从左⾄右优先级顺序进⾏替换
        if (StringUtils.isNotEmpty(deviceId)) {
            urls = replaceMacro("__DEVICE_ID__", urls, deviceId);
        }
        urls = replaceMacro("__LON__", urls, MacroType.LON.getCode());
        urls = replaceMacro("__LAT__", urls, MacroType.LAT.getCode());
        // 暂无 __CLICKAREA__ 点击区域 1：广告素材 2：按钮
        // 暂无 __DPLINK__ 是否deeplink唤醒 1：打开deeplink 2：打开落地页
        // 暂无 __DP_RESULT__ deeplink调起结果 0：成功 1：失败
        if (clickId != null) {
            urls = replaceMacro("__CLICK_ID__", urls, clickId);
        }
        return urls;
    }

    private String sign(AdRequest.Builder request, String advAppKey) {
        String str = request.getProtocol() + "&" + request.getAppId() + "&" + request.getSlotId() + "&" + request.getUuid() + "&" + request.getTimestamp() + "&" + advAppKey;
        return Md5.md5(str).toLowerCase();
    }


    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 只有竟胜
        if (!reqDto.getBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        boolean hasRight = false;
        for (String url : reqDto.getUrls()) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }
}
