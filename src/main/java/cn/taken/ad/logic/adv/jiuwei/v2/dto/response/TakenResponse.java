// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: Response.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.jiuwei.v2.dto.response;

import cn.taken.ad.logic.adv.jiuwei.v2.dto.response.tag.TakenRespTag;

public final class TakenResponse {
  private TakenResponse() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      TakenResponse.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_resp_TakenResponseInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_resp_TakenResponseInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\016Response.proto\022\004resp\032\021ResponseTag.prot" +
      "o\"W\n\021TakenResponseInfo\022\014\n\004code\030\001 \001(\005\022\016\n\006" +
      "respId\030\002 \001(\t\022$\n\004data\030\003 \003(\0132\026.resp.TakenR" +
      "esponseTagBB\n/cn.taken.ad.logic.prossor." +
      "taken.v2.dto.responseB\rTakenResponseP\001b\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          TakenRespTag.getDescriptor(),
        });
    internal_static_resp_TakenResponseInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_resp_TakenResponseInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_resp_TakenResponseInfo_descriptor,
        new String[] { "Code", "RespId", "Data", });
    descriptor.resolveAllFeaturesImmutable();
    TakenRespTag.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
