// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: yaya_mob4.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.yaya.dto;

/**
 * Protobuf type {@code ReqImp}
 */
public final class ReqImp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:ReqImp)
    ReqImpOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ReqImp.class.getName());
  }
  // Use ReqImp.newBuilder() to construct.
  private ReqImp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ReqImp() {
    id_ = "";
    tagid_ = "";
    bidfloorcur_ = "";
    isSupportHttps_ = 0;
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqImp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqImp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.taken.ad.logic.adv.yaya.dto.ReqImp.class, cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BANNER_FIELD_NUMBER = 2;
  private cn.taken.ad.logic.adv.yaya.dto.ReqBanner banner_;
  /**
   * <code>.ReqBanner banner = 2;</code>
   * @return Whether the banner field is set.
   */
  @java.lang.Override
  public boolean hasBanner() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.ReqBanner banner = 2;</code>
   * @return The banner.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqBanner getBanner() {
    return banner_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance() : banner_;
  }
  /**
   * <code>.ReqBanner banner = 2;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder getBannerOrBuilder() {
    return banner_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance() : banner_;
  }

  public static final int VIDEO_FIELD_NUMBER = 3;
  private cn.taken.ad.logic.adv.yaya.dto.ReqVideo video_;
  /**
   * <code>.ReqVideo video = 3;</code>
   * @return Whether the video field is set.
   */
  @java.lang.Override
  public boolean hasVideo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>.ReqVideo video = 3;</code>
   * @return The video.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqVideo getVideo() {
    return video_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqVideo.getDefaultInstance() : video_;
  }
  /**
   * <code>.ReqVideo video = 3;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqVideoOrBuilder getVideoOrBuilder() {
    return video_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqVideo.getDefaultInstance() : video_;
  }

  public static final int TAGID_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object tagid_ = "";
  /**
   * <code>string tagid = 4;</code>
   * @return The tagid.
   */
  @java.lang.Override
  public java.lang.String getTagid() {
    java.lang.Object ref = tagid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tagid_ = s;
      return s;
    }
  }
  /**
   * <code>string tagid = 4;</code>
   * @return The bytes for tagid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTagidBytes() {
    java.lang.Object ref = tagid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tagid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BIDFLOOR_FIELD_NUMBER = 5;
  private int bidfloor_ = 0;
  /**
   * <code>int32 bidfloor = 5;</code>
   * @return The bidfloor.
   */
  @java.lang.Override
  public int getBidfloor() {
    return bidfloor_;
  }

  public static final int BIDFLOORCUR_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bidfloorcur_ = "";
  /**
   * <code>string bidfloorcur = 6;</code>
   * @return The bidfloorcur.
   */
  @java.lang.Override
  public java.lang.String getBidfloorcur() {
    java.lang.Object ref = bidfloorcur_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      bidfloorcur_ = s;
      return s;
    }
  }
  /**
   * <code>string bidfloorcur = 6;</code>
   * @return The bytes for bidfloorcur.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBidfloorcurBytes() {
    java.lang.Object ref = bidfloorcur_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bidfloorcur_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISSUPPORTHTTPS_FIELD_NUMBER = 7;
  private int isSupportHttps_ = 0;
  /**
   * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
   * @return The enum numeric value on the wire for isSupportHttps.
   */
  @java.lang.Override public int getIsSupportHttpsValue() {
    return isSupportHttps_;
  }
  /**
   * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
   * @return The isSupportHttps.
   */
  @java.lang.Override public cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType getIsSupportHttps() {
    cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType result = cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.forNumber(isSupportHttps_);
    return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.UNRECOGNIZED : result;
  }

  public static final int ISNATIVEAD_FIELD_NUMBER = 8;
  private int isNativeAd_ = 0;
  /**
   * <code>int32 isNativeAd = 8;</code>
   * @return The isNativeAd.
   */
  @java.lang.Override
  public int getIsNativeAd() {
    return isNativeAd_;
  }

  public static final int ISDEEPLINK_FIELD_NUMBER = 9;
  private int isDeeplink_ = 0;
  /**
   * <code>int32 isDeeplink = 9;</code>
   * @return The isDeeplink.
   */
  @java.lang.Override
  public int getIsDeeplink() {
    return isDeeplink_;
  }

  public static final int NATIVEAD_FIELD_NUMBER = 10;
  private cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd nativeAd_;
  /**
   * <code>.ReqNativeAd nativeAd = 10;</code>
   * @return Whether the nativeAd field is set.
   */
  @java.lang.Override
  public boolean hasNativeAd() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>.ReqNativeAd nativeAd = 10;</code>
   * @return The nativeAd.
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd getNativeAd() {
    return nativeAd_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.getDefaultInstance() : nativeAd_;
  }
  /**
   * <code>.ReqNativeAd nativeAd = 10;</code>
   */
  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqNativeAdOrBuilder getNativeAdOrBuilder() {
    return nativeAd_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.getDefaultInstance() : nativeAd_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(2, getBanner());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getVideo());
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(tagid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, tagid_);
    }
    if (bidfloor_ != 0) {
      output.writeInt32(5, bidfloor_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bidfloorcur_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, bidfloorcur_);
    }
    if (isSupportHttps_ != cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.MobReqSupportHttpsType_Default.getNumber()) {
      output.writeEnum(7, isSupportHttps_);
    }
    if (isNativeAd_ != 0) {
      output.writeInt32(8, isNativeAd_);
    }
    if (isDeeplink_ != 0) {
      output.writeInt32(9, isDeeplink_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(10, getNativeAd());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getBanner());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getVideo());
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(tagid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, tagid_);
    }
    if (bidfloor_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, bidfloor_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bidfloorcur_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, bidfloorcur_);
    }
    if (isSupportHttps_ != cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.MobReqSupportHttpsType_Default.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, isSupportHttps_);
    }
    if (isNativeAd_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, isNativeAd_);
    }
    if (isDeeplink_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, isDeeplink_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getNativeAd());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.taken.ad.logic.adv.yaya.dto.ReqImp)) {
      return super.equals(obj);
    }
    cn.taken.ad.logic.adv.yaya.dto.ReqImp other = (cn.taken.ad.logic.adv.yaya.dto.ReqImp) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (hasBanner() != other.hasBanner()) return false;
    if (hasBanner()) {
      if (!getBanner()
          .equals(other.getBanner())) return false;
    }
    if (hasVideo() != other.hasVideo()) return false;
    if (hasVideo()) {
      if (!getVideo()
          .equals(other.getVideo())) return false;
    }
    if (!getTagid()
        .equals(other.getTagid())) return false;
    if (getBidfloor()
        != other.getBidfloor()) return false;
    if (!getBidfloorcur()
        .equals(other.getBidfloorcur())) return false;
    if (isSupportHttps_ != other.isSupportHttps_) return false;
    if (getIsNativeAd()
        != other.getIsNativeAd()) return false;
    if (getIsDeeplink()
        != other.getIsDeeplink()) return false;
    if (hasNativeAd() != other.hasNativeAd()) return false;
    if (hasNativeAd()) {
      if (!getNativeAd()
          .equals(other.getNativeAd())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    if (hasBanner()) {
      hash = (37 * hash) + BANNER_FIELD_NUMBER;
      hash = (53 * hash) + getBanner().hashCode();
    }
    if (hasVideo()) {
      hash = (37 * hash) + VIDEO_FIELD_NUMBER;
      hash = (53 * hash) + getVideo().hashCode();
    }
    hash = (37 * hash) + TAGID_FIELD_NUMBER;
    hash = (53 * hash) + getTagid().hashCode();
    hash = (37 * hash) + BIDFLOOR_FIELD_NUMBER;
    hash = (53 * hash) + getBidfloor();
    hash = (37 * hash) + BIDFLOORCUR_FIELD_NUMBER;
    hash = (53 * hash) + getBidfloorcur().hashCode();
    hash = (37 * hash) + ISSUPPORTHTTPS_FIELD_NUMBER;
    hash = (53 * hash) + isSupportHttps_;
    hash = (37 * hash) + ISNATIVEAD_FIELD_NUMBER;
    hash = (53 * hash) + getIsNativeAd();
    hash = (37 * hash) + ISDEEPLINK_FIELD_NUMBER;
    hash = (53 * hash) + getIsDeeplink();
    if (hasNativeAd()) {
      hash = (37 * hash) + NATIVEAD_FIELD_NUMBER;
      hash = (53 * hash) + getNativeAd().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.taken.ad.logic.adv.yaya.dto.ReqImp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code ReqImp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:ReqImp)
      cn.taken.ad.logic.adv.yaya.dto.ReqImpOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqImp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqImp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.taken.ad.logic.adv.yaya.dto.ReqImp.class, cn.taken.ad.logic.adv.yaya.dto.ReqImp.Builder.class);
    }

    // Construct using cn.taken.ad.logic.adv.yaya.dto.ReqImp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getBannerFieldBuilder();
        getVideoFieldBuilder();
        getNativeAdFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      banner_ = null;
      if (bannerBuilder_ != null) {
        bannerBuilder_.dispose();
        bannerBuilder_ = null;
      }
      video_ = null;
      if (videoBuilder_ != null) {
        videoBuilder_.dispose();
        videoBuilder_ = null;
      }
      tagid_ = "";
      bidfloor_ = 0;
      bidfloorcur_ = "";
      isSupportHttps_ = 0;
      isNativeAd_ = 0;
      isDeeplink_ = 0;
      nativeAd_ = null;
      if (nativeAdBuilder_ != null) {
        nativeAdBuilder_.dispose();
        nativeAdBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.taken.ad.logic.adv.yaya.dto.YaYaAdvPeqRespDto.internal_static_ReqImp_descriptor;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqImp getDefaultInstanceForType() {
      return cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance();
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqImp build() {
      cn.taken.ad.logic.adv.yaya.dto.ReqImp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.ReqImp buildPartial() {
      cn.taken.ad.logic.adv.yaya.dto.ReqImp result = new cn.taken.ad.logic.adv.yaya.dto.ReqImp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(cn.taken.ad.logic.adv.yaya.dto.ReqImp result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.banner_ = bannerBuilder_ == null
            ? banner_
            : bannerBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.video_ = videoBuilder_ == null
            ? video_
            : videoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.tagid_ = tagid_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.bidfloor_ = bidfloor_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.bidfloorcur_ = bidfloorcur_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.isSupportHttps_ = isSupportHttps_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.isNativeAd_ = isNativeAd_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.isDeeplink_ = isDeeplink_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.nativeAd_ = nativeAdBuilder_ == null
            ? nativeAd_
            : nativeAdBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.taken.ad.logic.adv.yaya.dto.ReqImp) {
        return mergeFrom((cn.taken.ad.logic.adv.yaya.dto.ReqImp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.taken.ad.logic.adv.yaya.dto.ReqImp other) {
      if (other == cn.taken.ad.logic.adv.yaya.dto.ReqImp.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasBanner()) {
        mergeBanner(other.getBanner());
      }
      if (other.hasVideo()) {
        mergeVideo(other.getVideo());
      }
      if (!other.getTagid().isEmpty()) {
        tagid_ = other.tagid_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.getBidfloor() != 0) {
        setBidfloor(other.getBidfloor());
      }
      if (!other.getBidfloorcur().isEmpty()) {
        bidfloorcur_ = other.bidfloorcur_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.isSupportHttps_ != 0) {
        setIsSupportHttpsValue(other.getIsSupportHttpsValue());
      }
      if (other.getIsNativeAd() != 0) {
        setIsNativeAd(other.getIsNativeAd());
      }
      if (other.getIsDeeplink() != 0) {
        setIsDeeplink(other.getIsDeeplink());
      }
      if (other.hasNativeAd()) {
        mergeNativeAd(other.getNativeAd());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getBannerFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  getVideoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              tagid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              bidfloor_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              bidfloorcur_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              isSupportHttps_ = input.readEnum();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              isNativeAd_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              isDeeplink_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              input.readMessage(
                  getNativeAdFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqBanner banner_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqBanner, cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder> bannerBuilder_;
    /**
     * <code>.ReqBanner banner = 2;</code>
     * @return Whether the banner field is set.
     */
    public boolean hasBanner() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     * @return The banner.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqBanner getBanner() {
      if (bannerBuilder_ == null) {
        return banner_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance() : banner_;
      } else {
        return bannerBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public Builder setBanner(cn.taken.ad.logic.adv.yaya.dto.ReqBanner value) {
      if (bannerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        banner_ = value;
      } else {
        bannerBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public Builder setBanner(
        cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder builderForValue) {
      if (bannerBuilder_ == null) {
        banner_ = builderForValue.build();
      } else {
        bannerBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public Builder mergeBanner(cn.taken.ad.logic.adv.yaya.dto.ReqBanner value) {
      if (bannerBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          banner_ != null &&
          banner_ != cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance()) {
          getBannerBuilder().mergeFrom(value);
        } else {
          banner_ = value;
        }
      } else {
        bannerBuilder_.mergeFrom(value);
      }
      if (banner_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public Builder clearBanner() {
      bitField0_ = (bitField0_ & ~0x00000002);
      banner_ = null;
      if (bannerBuilder_ != null) {
        bannerBuilder_.dispose();
        bannerBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder getBannerBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getBannerFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder getBannerOrBuilder() {
      if (bannerBuilder_ != null) {
        return bannerBuilder_.getMessageOrBuilder();
      } else {
        return banner_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqBanner.getDefaultInstance() : banner_;
      }
    }
    /**
     * <code>.ReqBanner banner = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqBanner, cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder> 
        getBannerFieldBuilder() {
      if (bannerBuilder_ == null) {
        bannerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqBanner, cn.taken.ad.logic.adv.yaya.dto.ReqBanner.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqBannerOrBuilder>(
                getBanner(),
                getParentForChildren(),
                isClean());
        banner_ = null;
      }
      return bannerBuilder_;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqVideo video_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqVideo, cn.taken.ad.logic.adv.yaya.dto.ReqVideo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqVideoOrBuilder> videoBuilder_;
    /**
     * <code>.ReqVideo video = 3;</code>
     * @return Whether the video field is set.
     */
    public boolean hasVideo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     * @return The video.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqVideo getVideo() {
      if (videoBuilder_ == null) {
        return video_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqVideo.getDefaultInstance() : video_;
      } else {
        return videoBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public Builder setVideo(cn.taken.ad.logic.adv.yaya.dto.ReqVideo value) {
      if (videoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        video_ = value;
      } else {
        videoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public Builder setVideo(
        cn.taken.ad.logic.adv.yaya.dto.ReqVideo.Builder builderForValue) {
      if (videoBuilder_ == null) {
        video_ = builderForValue.build();
      } else {
        videoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public Builder mergeVideo(cn.taken.ad.logic.adv.yaya.dto.ReqVideo value) {
      if (videoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          video_ != null &&
          video_ != cn.taken.ad.logic.adv.yaya.dto.ReqVideo.getDefaultInstance()) {
          getVideoBuilder().mergeFrom(value);
        } else {
          video_ = value;
        }
      } else {
        videoBuilder_.mergeFrom(value);
      }
      if (video_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public Builder clearVideo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      video_ = null;
      if (videoBuilder_ != null) {
        videoBuilder_.dispose();
        videoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqVideo.Builder getVideoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return getVideoFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqVideoOrBuilder getVideoOrBuilder() {
      if (videoBuilder_ != null) {
        return videoBuilder_.getMessageOrBuilder();
      } else {
        return video_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqVideo.getDefaultInstance() : video_;
      }
    }
    /**
     * <code>.ReqVideo video = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqVideo, cn.taken.ad.logic.adv.yaya.dto.ReqVideo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqVideoOrBuilder> 
        getVideoFieldBuilder() {
      if (videoBuilder_ == null) {
        videoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqVideo, cn.taken.ad.logic.adv.yaya.dto.ReqVideo.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqVideoOrBuilder>(
                getVideo(),
                getParentForChildren(),
                isClean());
        video_ = null;
      }
      return videoBuilder_;
    }

    private java.lang.Object tagid_ = "";
    /**
     * <code>string tagid = 4;</code>
     * @return The tagid.
     */
    public java.lang.String getTagid() {
      java.lang.Object ref = tagid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tagid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string tagid = 4;</code>
     * @return The bytes for tagid.
     */
    public com.google.protobuf.ByteString
        getTagidBytes() {
      java.lang.Object ref = tagid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tagid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string tagid = 4;</code>
     * @param value The tagid to set.
     * @return This builder for chaining.
     */
    public Builder setTagid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      tagid_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string tagid = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTagid() {
      tagid_ = getDefaultInstance().getTagid();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string tagid = 4;</code>
     * @param value The bytes for tagid to set.
     * @return This builder for chaining.
     */
    public Builder setTagidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      tagid_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int bidfloor_ ;
    /**
     * <code>int32 bidfloor = 5;</code>
     * @return The bidfloor.
     */
    @java.lang.Override
    public int getBidfloor() {
      return bidfloor_;
    }
    /**
     * <code>int32 bidfloor = 5;</code>
     * @param value The bidfloor to set.
     * @return This builder for chaining.
     */
    public Builder setBidfloor(int value) {

      bidfloor_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>int32 bidfloor = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBidfloor() {
      bitField0_ = (bitField0_ & ~0x00000010);
      bidfloor_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object bidfloorcur_ = "";
    /**
     * <code>string bidfloorcur = 6;</code>
     * @return The bidfloorcur.
     */
    public java.lang.String getBidfloorcur() {
      java.lang.Object ref = bidfloorcur_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bidfloorcur_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string bidfloorcur = 6;</code>
     * @return The bytes for bidfloorcur.
     */
    public com.google.protobuf.ByteString
        getBidfloorcurBytes() {
      java.lang.Object ref = bidfloorcur_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bidfloorcur_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string bidfloorcur = 6;</code>
     * @param value The bidfloorcur to set.
     * @return This builder for chaining.
     */
    public Builder setBidfloorcur(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bidfloorcur_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string bidfloorcur = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearBidfloorcur() {
      bidfloorcur_ = getDefaultInstance().getBidfloorcur();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string bidfloorcur = 6;</code>
     * @param value The bytes for bidfloorcur to set.
     * @return This builder for chaining.
     */
    public Builder setBidfloorcurBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      bidfloorcur_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int isSupportHttps_ = 0;
    /**
     * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
     * @return The enum numeric value on the wire for isSupportHttps.
     */
    @java.lang.Override public int getIsSupportHttpsValue() {
      return isSupportHttps_;
    }
    /**
     * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
     * @param value The enum numeric value on the wire for isSupportHttps to set.
     * @return This builder for chaining.
     */
    public Builder setIsSupportHttpsValue(int value) {
      isSupportHttps_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
     * @return The isSupportHttps.
     */
    @java.lang.Override
    public cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType getIsSupportHttps() {
      cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType result = cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.forNumber(isSupportHttps_);
      return result == null ? cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType.UNRECOGNIZED : result;
    }
    /**
     * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
     * @param value The isSupportHttps to set.
     * @return This builder for chaining.
     */
    public Builder setIsSupportHttps(cn.taken.ad.logic.adv.yaya.dto.MobReqSupportHttpsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000040;
      isSupportHttps_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.MobReqSupportHttpsType isSupportHttps = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsSupportHttps() {
      bitField0_ = (bitField0_ & ~0x00000040);
      isSupportHttps_ = 0;
      onChanged();
      return this;
    }

    private int isNativeAd_ ;
    /**
     * <code>int32 isNativeAd = 8;</code>
     * @return The isNativeAd.
     */
    @java.lang.Override
    public int getIsNativeAd() {
      return isNativeAd_;
    }
    /**
     * <code>int32 isNativeAd = 8;</code>
     * @param value The isNativeAd to set.
     * @return This builder for chaining.
     */
    public Builder setIsNativeAd(int value) {

      isNativeAd_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>int32 isNativeAd = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsNativeAd() {
      bitField0_ = (bitField0_ & ~0x00000080);
      isNativeAd_ = 0;
      onChanged();
      return this;
    }

    private int isDeeplink_ ;
    /**
     * <code>int32 isDeeplink = 9;</code>
     * @return The isDeeplink.
     */
    @java.lang.Override
    public int getIsDeeplink() {
      return isDeeplink_;
    }
    /**
     * <code>int32 isDeeplink = 9;</code>
     * @param value The isDeeplink to set.
     * @return This builder for chaining.
     */
    public Builder setIsDeeplink(int value) {

      isDeeplink_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>int32 isDeeplink = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsDeeplink() {
      bitField0_ = (bitField0_ & ~0x00000100);
      isDeeplink_ = 0;
      onChanged();
      return this;
    }

    private cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd nativeAd_;
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAdOrBuilder> nativeAdBuilder_;
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     * @return Whether the nativeAd field is set.
     */
    public boolean hasNativeAd() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     * @return The nativeAd.
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd getNativeAd() {
      if (nativeAdBuilder_ == null) {
        return nativeAd_ == null ? cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.getDefaultInstance() : nativeAd_;
      } else {
        return nativeAdBuilder_.getMessage();
      }
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public Builder setNativeAd(cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd value) {
      if (nativeAdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nativeAd_ = value;
      } else {
        nativeAdBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public Builder setNativeAd(
        cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.Builder builderForValue) {
      if (nativeAdBuilder_ == null) {
        nativeAd_ = builderForValue.build();
      } else {
        nativeAdBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public Builder mergeNativeAd(cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd value) {
      if (nativeAdBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0) &&
          nativeAd_ != null &&
          nativeAd_ != cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.getDefaultInstance()) {
          getNativeAdBuilder().mergeFrom(value);
        } else {
          nativeAd_ = value;
        }
      } else {
        nativeAdBuilder_.mergeFrom(value);
      }
      if (nativeAd_ != null) {
        bitField0_ |= 0x00000200;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public Builder clearNativeAd() {
      bitField0_ = (bitField0_ & ~0x00000200);
      nativeAd_ = null;
      if (nativeAdBuilder_ != null) {
        nativeAdBuilder_.dispose();
        nativeAdBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.Builder getNativeAdBuilder() {
      bitField0_ |= 0x00000200;
      onChanged();
      return getNativeAdFieldBuilder().getBuilder();
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    public cn.taken.ad.logic.adv.yaya.dto.ReqNativeAdOrBuilder getNativeAdOrBuilder() {
      if (nativeAdBuilder_ != null) {
        return nativeAdBuilder_.getMessageOrBuilder();
      } else {
        return nativeAd_ == null ?
            cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.getDefaultInstance() : nativeAd_;
      }
    }
    /**
     * <code>.ReqNativeAd nativeAd = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAdOrBuilder> 
        getNativeAdFieldBuilder() {
      if (nativeAdBuilder_ == null) {
        nativeAdBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAd.Builder, cn.taken.ad.logic.adv.yaya.dto.ReqNativeAdOrBuilder>(
                getNativeAd(),
                getParentForChildren(),
                isClean());
        nativeAd_ = null;
      }
      return nativeAdBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:ReqImp)
  }

  // @@protoc_insertion_point(class_scope:ReqImp)
  private static final cn.taken.ad.logic.adv.yaya.dto.ReqImp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.taken.ad.logic.adv.yaya.dto.ReqImp();
  }

  public static cn.taken.ad.logic.adv.yaya.dto.ReqImp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqImp>
      PARSER = new com.google.protobuf.AbstractParser<ReqImp>() {
    @java.lang.Override
    public ReqImp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ReqImp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqImp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.taken.ad.logic.adv.yaya.dto.ReqImp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

