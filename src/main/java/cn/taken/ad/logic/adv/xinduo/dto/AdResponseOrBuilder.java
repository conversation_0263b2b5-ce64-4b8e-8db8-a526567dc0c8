// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xinduo_9.0.1.proto
// Protobuf Java Version: 4.28.3

package cn.taken.ad.logic.adv.xinduo.dto;

public interface AdResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:AdResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 状态码
   * </pre>
   *
   * <code>int32 code = 1;</code>
   * @return The code.
   */
  int getCode();

  /**
   * <pre>
   * 消息描述
   * </pre>
   *
   * <code>string msg = 2;</code>
   * @return The msg.
   */
  String getMsg();
  /**
   * <pre>
   * 消息描述
   * </pre>
   *
   * <code>string msg = 2;</code>
   * @return The bytes for msg.
   */
  com.google.protobuf.ByteString
      getMsgBytes();

  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  java.util.List<Ad>
      getAdsList();
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  Ad getAds(int index);
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  int getAdsCount();
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  java.util.List<? extends AdOrBuilder>
      getAdsOrBuilderList();
  /**
   * <pre>
   * 广告列表‌:ml-citation{ref="1,2" data="citationList"}
   * </pre>
   *
   * <code>repeated .Ad ads = 3;</code>
   */
  AdOrBuilder getAdsOrBuilder(
      int index);
}
