package cn.taken.ad.logic.adv.xinyi;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.xinyi.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 新义DSP
 */
@Component("XINYI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class XinYiAdvProcessor implements AdvProcessor {
    public static final String API_VERSION = "apiVersion";
    public static final String PRICE_KEY = "priceKey";
    private static final Logger log = LoggerFactory.getLogger(XinYiAdvProcessor.class);
    @Autowired
    private BaseRedisL2Cache baseRedisL2Cache;

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        XinYiRequest request = convertRequest(rtbDto, advDto);
        advDto.setReqObj(request);
        String json = JsonHelper.toJsonStringWithoutNull(request);
        // gzip 压缩json
        HttpResult httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json"), new BasicHeader("Accept-Encoding", "gzip"), new BasicHeader("Accept-Gzip", "true")}, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        int httpCode = httpResult.getStatusLine().getStatusCode();
        if (httpCode != 200) {
            if (httpCode == 204) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                advDto.setRespObj(resp);
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), httpCode + "");
            }
        }
        return parseResponse(rtbDto, resp, advDto);
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 只有竟胜
        if (!reqDto.getBiddingSuccess()) {
            return SuperResult.rightResult();
        }
        List<String> urls = reqDto.getUrls();
        if (reqDto.getBiddingSuccess() && null != reqDto.getPrice()) {
            // 宏替换
            // 竟胜 价格加密
            String price;
            try {
                String priceKey = ParamParser.parseParamByJson(reqDto.getAdvCustomParam()).get(PRICE_KEY);
                price = aesPrice(reqDto.getPrice(), priceKey);
                urls = replaceMacro("{XY_PRICE}", urls, price);
            } catch (Exception e) {
                log.error("Error Info:{}", JsonHelper.toJsonString(reqDto), e);
                return SuperResult.badResult("price encrypt fail");
            }
        }
        // 是否有请求成功的
        boolean hasRight = false;
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, String resp, RtbAdvDto advDto) throws Exception {
        XinYiResponse response;
        try {
            advDto.setRespObj(resp);
            response = JsonHelper.fromJson(XinYiResponse.class, resp);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Serializable resp fail");
        }
        if (null == response.getAds() || response.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        Map<String, String> params = ParamParser.parseParamByJson(advDto.getPnyParam());
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "", "");
        response.getAds().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            tagResponseDto.setMaterialHeight(tag.getHeight());
            tagResponseDto.setMaterialWidth(tag.getWidth());
            // ad_id 暂无
            tagResponseDto.setCreativeId(tag.getCreative_id());
            if (null != tag.getPrice()) {
                tagResponseDto.setPrice(tag.getPrice().doubleValue());
            }
            String price = aesPrice(tagResponseDto.getPrice(), params.get(PRICE_KEY));
            tagResponseDto.setTitle(tag.getTitle());
            tagResponseDto.setSubTitle(tag.getSubtitle());
            tagResponseDto.setDesc(tag.getDescription());
            // 暂无 advertiser_name广告主名称
            // 暂无 ratings 评价数
            // 暂无 button_text按钮文字
            // 暂无 likes 点赞次数
            // 暂无 downloads 下载次数
            if (null != tag.getImages()) {
                tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                List<String> imagesUrls = new ArrayList<>();
                tag.getImages().forEach(image -> {
                    if (StringUtils.isNotEmpty(image.getUrl())) {
                        imagesUrls.add(image.getUrl());
                    }
                });
                tagResponseDto.setImgUrls(imagesUrls);
            }
            if (null != tag.getIcon() && StringUtils.isNotEmpty(tag.getIcon().getUrl())) {
                tagResponseDto.setIconUrl(tag.getIcon().getUrl());
            }
            if (null != tag.getLogo() && StringUtils.isNotEmpty(tag.getLogo().getUrl())) {
                tagResponseDto.setLogoUrl(tag.getLogo().getUrl());
            }
            if (null != tag.getVideo()) {
                tagResponseDto.setMaterialType(MaterialType.VIDEO);
                ResponseVideoDto videoInfo = new ResponseVideoDto();
                videoInfo.setVideoUrl(tag.getVideo().getUrl());
                videoInfo.setDuration(tag.getVideo().getDuration());
                tagResponseDto.setVideoInfo(videoInfo);

                if (null != tag.getVideo_cover() && StringUtils.isNotEmpty(tag.getVideo_cover().getUrl())) {
                    videoInfo.setCoverImgUrls(new ArrayList<>(Collections.singletonList(tag.getVideo_cover().getUrl())));
                    videoInfo.setCoverWidth(tag.getVideo_cover().getWidth());
                    videoInfo.setCoverHeight(tag.getVideo_cover().getHeight());
                }
            }
            // 暂无 html_snippet html 代码片段, 在 webview 中加载
            // 暂无 html_url html 代码地址, 在 webview 中加载
            tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
            switch (tag.getAction()) {
                case 1:
                    tagResponseDto.setActionType(ActionType.WEB_VIEW_H5);
                    break;
                case 2:
                    tagResponseDto.setActionType(ActionType.SYSTEM_BROWSER_H5);
                    break;
                case 6:
                    tagResponseDto.setActionType(ActionType.DOWNLOAD);
                    break;
                case 7:
                    tagResponseDto.setActionType(ActionType.DEEPLINK);
                    break;
                case 8:
                    tagResponseDto.setActionType(ActionType.MINI_PROGRAM);
                    if (StringUtils.isNotBlank(tag.getMini_program_id()) && StringUtils.isNotEmpty(tag.getMini_program_path())) {
                        tagResponseDto.setMiniProgram(new ResponseMiniProgramDto(tag.getMini_program_id(), tag.getMini_program_path()));
                    }
                    break;
            }
            tagResponseDto.setClickUrl(tag.getTarget_url());
            ResponseAppDto appDto = new ResponseAppDto();
            appDto.setAppName(tag.getDownload_app_name());
            appDto.setPackageName(tag.getDownload_app_bundle());
            appDto.setAppVersion(tag.getDownload_app_version());
            if (null != tag.getDownload_app_size()) {
                appDto.setAppSize(tag.getDownload_app_size().longValue());
            }
            appDto.setAppInfo(tag.getDownload_app_desc());
            appDto.setAppPrivacyUrl(tag.getPrivacy_url());
            appDto.setAppPermissionInfoUrl(tag.getPermission_url());
            // 暂无 mini_program_id 小程序原始id
            // 暂无 mini_program_path 小程序页面路径
            tagResponseDto.setDeepLinkUrl(tag.getDeeplink_url());
            tagResponseDto.setAppInfo(appDto);

            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);
            if (null != tag.getImpression_trackers() && !tag.getImpression_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(tag.getImpression_trackers())));
            }
            if (null != tag.getClick_trackers() && !tag.getClick_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(tag.getClick_trackers())));
            }
            if (null != tag.getDownload_begin_trackers() && !tag.getDownload_begin_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), new ArrayList<>(tag.getDownload_begin_trackers())));
            }
            if (null != tag.getDownload_ended_trackers() && !tag.getDownload_ended_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), new ArrayList<>(tag.getDownload_ended_trackers())));
            }
            if (null != tag.getInstall_begin_trackers() && !tag.getInstall_begin_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_BEGIN.getType(), new ArrayList<>(tag.getInstall_begin_trackers())));
            }
            if (null != tag.getInstall_ended_trackers() && !tag.getInstall_ended_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), new ArrayList<>(tag.getInstall_ended_trackers())));
            }
            if (null != tag.getVideo_play_begin_trackers() && !tag.getVideo_play_begin_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), new ArrayList<>(tag.getVideo_play_begin_trackers())));
            }
            if (null != tag.getVideo_play_break_trackers() && !tag.getVideo_play_break_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), new ArrayList<>(tag.getVideo_play_break_trackers())));
            }
            if (null != tag.getVideo_play_ended_trackers() && !tag.getVideo_play_ended_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), new ArrayList<>(tag.getVideo_play_ended_trackers())));
            }
            // 暂无 deeplink_app_not_installed_trackers deeplink 唤醒时，发现应用没有安装
            // 暂无 deeplink_app_installed_trackers deeplink 唤醒时，发现应用有安装
            // 暂无 deeplink_app_invoke_failed_trackers deeplink 唤醒时，应用有安装但唤起失败
            if (null != tag.getDeeplink_app_invoke_success_trackers() && !tag.getDeeplink_app_invoke_success_trackers().isEmpty()) {
                tracks.add(new ResponseTrackDto(EventType.DEEPLINK_OPEN_SUCCESS.getType(), new ArrayList<>(tag.getDeeplink_app_invoke_success_trackers())));
            }
            // 竟胜链接
            if (StringUtils.isNotEmpty(tag.getWin_notice_tracker())) {
                tagResponseDto.setWinNoticeUrls(new ArrayList<>(Collections.singletonList(tag.getWin_notice_tracker())));
            }
            // 汇川预算 暂无 click_area_report_url
            if (StringUtils.isNotEmpty(tag.getClick_area_report_url())) {
                tagResponseDto.setClickAreaReportUrls(new ArrayList<>(Collections.singletonList(tag.getClick_area_report_url())));
            }
            // 宏替换
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = replaceAllMacro(track.getTrackUrls(), price);
                track.setTrackUrls(urls);
            });
            if (StringUtils.isNotBlank(tagResponseDto.getClickUrl())) {
                // click url 宏替换
                List<String> clickUrls = replaceAllMacro(new ArrayList<>(Collections.singletonList(tagResponseDto.getClickUrl())), price);
                tagResponseDto.setClickUrl(clickUrls.get(0));
            }
            // deeplink url 宏替换
            if (StringUtils.isNotEmpty(tagResponseDto.getDeepLinkUrl())) {
                List<String> deepUrls = replaceAllMacro(new ArrayList<>(Collections.singletonList(tagResponseDto.getDeepLinkUrl())), price);
                tagResponseDto.setDeepLinkUrl(deepUrls.get(0));
            }
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }

    private List<String> replaceAllMacro(List<String> urls, String aesPrice) {
        urls = replaceMacro("{XY_CLICK_DOWN_X}", urls, MacroType.DOWN_X.getCode());
        urls = replaceMacro("{XY_CLICK_DOWN_Y}", urls, MacroType.DOWN_Y.getCode());
        urls = replaceMacro("{XY_CLICK_UP_X}", urls, MacroType.UP_X.getCode());
        urls = replaceMacro("{XY_CLICK_UP_Y}", urls, MacroType.UP_Y.getCode());
        urls = replaceMacro("{XY_CLICK_DOWN_LX}", urls, MacroType.DP_DOWN_X.getCode());
        urls = replaceMacro("{XY_CLICK_DOWN_LY}", urls, MacroType.DP_DOWN_Y.getCode());
        urls = replaceMacro("{XY_CLICK_UP_LX}", urls, MacroType.DP_UP_X.getCode());
        urls = replaceMacro("{XY_CLICK_UP_LY}", urls, MacroType.DP_UP_Y.getCode());
        if (StringUtils.isNotEmpty(aesPrice)) {
            urls = replaceMacro("{XY_PRICE}", urls, aesPrice);
        }
        return urls;
    }

    /**
     * 参数转换为广告主需要的格式
     */
    private XinYiRequest convertRequest(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());
        XinYiRequest request = new XinYiRequest();
        request.setId(rtbDto.getReqId());
        request.setVersion(param.get(API_VERSION));
        //广告位
        request.setAds(convertRequestAds(rtbDto, advDto));
        //APP
        request.setApp(convertRequestApp(rtbDto, advDto));
        // device
        request.setDevice(convertRequestDevice(rtbDto));
        //user
        request.setUser(convertRequestUser(rtbDto));
        request.setNeed_https(false);
        return request;
    }

    private XinYiRequestDevice convertRequestDevice(RtbRequestDto rtbDto) {
        XinYiRequestDevice request = new XinYiRequestDevice();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        RequestGeoDto geoDto = rtbDto.getGeo();
        request.setIp(networkDto.getIp());
        request.setIpv6(networkDto.getIpv6());
        request.setUser_agent(deviceDto.getUserAgent());
        request.setMake(deviceDto.getBrand());
        request.setBrand(deviceDto.getBrand());
        request.setModel(deviceDto.getModel());

        OsType osType = deviceDto.getOsType();
        if (null != osType) {
            switch (osType) {
                case IOS:
                    request.setOs("ios");
                    break;
                case ANDROID:
                    request.setOs("android");
                    break;
            }
        }
        request.setOs_version(deviceDto.getOsVersion());
        ConnectionType connectionType = networkDto.getConnectType();
        switch (connectionType) {
            case WIFI:
                request.setConnection_type("wifi");
                break;
            case NETWORK_2G:
                request.setConnection_type("2g");
                break;
            case NETWORK_3G:
                request.setConnection_type("3g");
                break;
            case NETWORK_4G:
                request.setConnection_type("4g");
                break;
            case NETWORK_5G:
                request.setConnection_type("5g");
                break;
        }
        if (null != deviceDto.getOrientation()) {
            if (deviceDto.getOrientation().getType() == 1) {
                request.setOrientation("landscape");
            } else if (deviceDto.getOrientation().getType() == 2) {
                request.setOrientation("portrait");
            }
        }
        CarrierType carrierType = networkDto.getCarrierType();
        switch (carrierType) {
            case CM:
                request.setPlmn("46000");
                break;
            case CU:
                request.setPlmn("46001");
                break;
            case CT:
                request.setPlmn("46003");
                break;
        }
        request.setMac(networkDto.getMac());
        request.setMac_md5(networkDto.getMacMd5());
        request.setLanguage(deviceDto.getLanguage());
        if (null != deviceDto.getWidth()) {
            request.setScreen_width(deviceDto.getWidth());
        }
        if (null != deviceDto.getHeight()) {
            request.setScreen_height(deviceDto.getHeight());
        }
        // screen_dpi 暂无
        if (null != deviceDto.getScreenDensity()) {
            request.setScreen_pxratio(deviceDto.getScreenDensity().floatValue());
        }
        if (null != geoDto.getLongitude()) {
            request.setGeo_longitude(geoDto.getLongitude().floatValue());
        }
        if (null != geoDto.getLatitude()) {
            request.setGeo_latitude(geoDto.getLatitude().floatValue());
        }
        if (null != deviceDto.getInstalledAppInfo() && !deviceDto.getInstalledAppInfo().isEmpty()) {
            List<String> packageNames = new ArrayList<>();
            deviceDto.getInstalledAppInfo().forEach(item -> {
                if (StringUtils.isNotEmpty(item.getPackageName())) {
                    packageNames.add(item.getPackageName());
                }
            });
            if (!packageNames.isEmpty()) {
                request.setInstalled_apps(StringUtils.join(packageNames, ","));
            }
        }
        DeviceType deviceType = deviceDto.getDeviceType();
        if (null == deviceType) {
            request.setDevice_type(0);
        } else {
            switch (deviceType) {
                case PHONE:
                    request.setDevice_type(1);
                    break;
                case PAD:
                    request.setDevice_type(2);
                    break;
                case TV:
                    request.setDevice_type(3);
                    break;
                case PC:
                    request.setDevice_type(4);
                    break;
                default:
                    request.setDevice_type(0);
                    break;
            }
        }
        request.setSsid(networkDto.getSsid());
        request.setWifi_mac(networkDto.getWifiMac());
        request.setImei(deviceDto.getImei());
        request.setImei_md5(deviceDto.getImeiMd5());
        request.setAndroid_id(deviceDto.getAndroidId());
        request.setAndroid_id_md5(deviceDto.getAndroidIdMd5());
        request.setImsi(deviceDto.getImsi());
        request.setAndroid_advertising_id(deviceDto.getAaid());
        request.setOaid(deviceDto.getOaid());
        request.setOaid_md5(deviceDto.getOaidMd5());
        request.setRom_version(deviceDto.getRomVersion());
        Long cpmTime = TimeUtils.convertMilliSecond(deviceDto.getSysCompileTime());
        if (null != cpmTime) {
            request.setSys_compiling_time(cpmTime);
        } else if (null != deviceDto.getSysUpdateTime()) {
            Long next = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
            request.setSys_compiling_time(next);
        }
        request.setIdfa(deviceDto.getIdfa());
        request.setIdfa_md5(deviceDto.getIdfaMd5());
        request.setIdfv(deviceDto.getIdfv());
        List<RequestCaidDto> caids = new ArrayList<>();
        // 优先使用 caids 列表中的有效数据
        if (deviceDto.getCaids() != null) {
            caids = deviceDto.getCaids().stream()
                    .filter(caid -> StringUtils.isNotEmpty(caid.getCaid()))
                    .collect(Collectors.toList());
        }
        // 如果 caids 为空但存在单个 caId，则添加
        // 设置最多两个 caid 到 request
        if (!caids.isEmpty()) {
            request.setCaid(caids.get(0).getCaid());
            request.setCaid_version(caids.get(0).getVersion());
            if (caids.size() > 1) {
                request.setCaid2(caids.get(1).getCaid());
                request.setCaid2_version(caids.get(1).getVersion());
            }
        }

        request.setOpenudid(deviceDto.getOpenUdId());
        request.setBoot_mark(deviceDto.getBootMark());
        request.setUpdate_mark(deviceDto.getUpdateMark());

        if (StringUtils.isNotBlank(deviceDto.getSysStartTime())) {
            Long next = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
            if (next != null) {
                String time = String.valueOf(next / 1000);
                request.setDevice_startup_time(time);
            }
        }
        if (StringUtils.isNotBlank(deviceDto.getSysUpdateTime())) {
            Long next = TimeUtils.convertMilliSecond(deviceDto.getSysUpdateTime());
            request.setSystem_update_time(TimeUtils.formatUnixTimeWithPrecision(next, false));
        }
        request.setSystem_init_time(deviceDto.getSysInitTime());
        request.setPaid(deviceDto.getPaid());
        return request;
    }

    private XinYiRequestUser convertRequestUser(RtbRequestDto rtbDto) {
        XinYiRequestUser request = new XinYiRequestUser();
        RequestUserDto userDto = rtbDto.getUser();
        if (StringUtils.isNotEmpty(userDto.getGender())) {
            if ("M".equals(userDto.getGender())) {
                request.setGender("男");
            } else if ("F".equals(userDto.getGender())) {
                request.setGender("女");
            } else {
                request.setGender("未知");
            }
        }
        request.setAge(userDto.getAge());
        if (null != userDto.getInterest() && userDto.getInterest().length > 0) {
            request.setKeywords(userDto.getInterest());
        }
        return request;
    }


    private XinYiRequestApp convertRequestApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        XinYiRequestApp request = new XinYiRequestApp();
        RequestAppDto appDto = rtbDto.getApp();
        request.setName(appDto.getAppName());
        request.setVersion(appDto.getAppVersion());
        request.setBundle(appDto.getBundle());
        request.setDeeplink_mode(0);
        return request;
    }

    private List<XinYiRequestAd> convertRequestAds(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        XinYiRequestAd request = new XinYiRequestAd();
        request.setAd_unit_token(advDto.getTagCode());
        request.setWidth(rtbDto.getTag().getHeight());
        request.setHeight(rtbDto.getTag().getWidth());
        if (null != rtbDto.getTag().getPrice()) {
            request.setFloor_price(rtbDto.getTag().getPrice().floatValue());
        }
        //request.setSupport_js(false);
        return new ArrayList<>(Collections.singletonList(request));
    }

    private String aesPrice(Double price, String aesKey) {
        if (null == price) {
            return null;
        }
        if (StringUtils.isEmpty(aesKey)) {
            return null;
        }
        return Base64.getUrlEncoder().withoutPadding().encodeToString(Aes.encrypt(price.toString(), aesKey));
    }
}
