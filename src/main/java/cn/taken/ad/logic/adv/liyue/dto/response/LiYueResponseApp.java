package cn.taken.ad.logic.adv.liyue.dto.response;

import java.io.Serializable;

public class LiYueResponseApp implements Serializable {

    private static final long serialVersionUID = -8784371022371836836L;
    /**
     * 应⽤名称
     */
    private String app_name;
    /**
     * 应⽤版本号
     */
    private String app_version;
    /**
     * 应⽤开发者
     */
    private String developer;
    /**
     * 隐私协议地址
     */
    private String privacy_url;
    /**
     * 隐私权限地址
     */
    private String permission_url;
    /**
     * 隐私权限描述
     */
    private String permission_desc;
    /**
     * 功能介绍
     */
    private String app_intro;
    /**
     * 包名
     */
    private String package_name;
    /**
     * 图标
     */
    private String app_icon_url;
    /**
     * 应⽤⼤⼩, 单位: byte
     */
    private Integer size;
    /**
     * 下载地址
     */
    private String download_url;

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getDeveloper() {
        return developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }

    public String getPrivacy_url() {
        return privacy_url;
    }

    public void setPrivacy_url(String privacy_url) {
        this.privacy_url = privacy_url;
    }

    public String getPermission_url() {
        return permission_url;
    }

    public void setPermission_url(String permission_url) {
        this.permission_url = permission_url;
    }

    public String getPermission_desc() {
        return permission_desc;
    }

    public void setPermission_desc(String permission_desc) {
        this.permission_desc = permission_desc;
    }

    public String getApp_intro() {
        return app_intro;
    }

    public void setApp_intro(String app_intro) {
        this.app_intro = app_intro;
    }

    public String getPackage_name() {
        return package_name;
    }

    public void setPackage_name(String package_name) {
        this.package_name = package_name;
    }

    public String getApp_icon_url() {
        return app_icon_url;
    }

    public void setApp_icon_url(String app_icon_url) {
        this.app_icon_url = app_icon_url;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getDownload_url() {
        return download_url;
    }

    public void setDownload_url(String download_url) {
        this.download_url = download_url;
    }
}
