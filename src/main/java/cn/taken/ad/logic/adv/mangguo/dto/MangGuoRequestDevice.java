package cn.taken.ad.logic.adv.mangguo.dto;

import java.io.Serializable;

public class MangGuoRequestDevice implements Serializable {

    private static final long serialVersionUID = -3085919673891588662L;

    /**
     * Android 设备唯一标识码 IMEI	安卓必填，广告定向依赖ID（若获取不到需要oaid或者android_id）
     */
    private String imei;
    /**
     * Android 设备唯一标识码 IMEI的md5值	安卓必填，md5后的IMEI， 32位小写
     */
    private String imei_md5;
    /**
     * Android 设备唯一标识 OAID	安卓必填，因androidQ版本无法获取imei
     */
    private String oaid;
    /**
     * Android 设备ID	安卓必填
     */
    private String android_id;
    /**
     * Android 设备ID的md5值	安卓必填
     */
    private String android_id_md5;
    /**
     * iOS 设备唯一标识码	iOS 必填，广告定向依赖ID
     */
    private String idfa;
    /**
     * iOS 设备唯一标志码，idfa关闭时使用	iOS必填，广告定向依赖ID
     */
    private String openudid;
    /**
     * Pc端 cookie字段	m站或者Pc必填，用户唯一id
     */
    private String cookie;
    /**
     * Mac 地址	必填（OTT必填 明文）
     */
    private String mac;
    /**
     * 操作系统	必填，设备操作系统，android，ios, win7, win10等
     */
    private String os;
    /**
     * 操作系统版本	必填，操作系统版本号，手机端格式：例如Android_10, ios_15.5	OTT端格式：例如4.4.4
     */
    private String osver;
    /**
     * 设备品牌	移动设备必填，中文需要UTF-8 编码，如“HUAWEI”;（REDMI XIaomi HUAWEI HONOR 区分开）系统字段 ：Build.BRAND
     */
    private String brand;
    /**
     * 设备型号	移动设备必填，中文需要UTF-8 编码，如“D22AP”
     */
    private String model;
    /**
     * 屏幕分辨率宽度	必填，单位为像素
     */
    private Integer sw;
    /**
     * 屏幕分辨率高度	必填，单位为像素
     */
    private Integer sh;
    /**
     * 像素每英寸(新增)	必填，以像素每英寸表示的屏幕尺寸
     */
    private Integer ppi;
    /**
     * 访问者的IP地址	必填，客户端请求使用自身ip；服务端请求使用客户端ip
     */
    private String ip;
    /**
     * 访问者的代理浏览器类型	必填，广告请求中的ua 需使用系统webview 的ua，请勿自定义ua
     */
    private String ua;
    /**
     * 访问者请求的referer	PC端必填，用户设备HTTP请求头中的Referer字段
     */
    private String referer;
    /**
     * 网络连接类型	移动端必填，0-无网络, 1-WIFI，2-3G，3-4G，4-2G，5-5G
     */
    private Integer connection_type;
    /**
     * 移动运营商类型 移动端必填，-1-未知，0-中国移动（GSM），1-中国联通（GSM），2-中国移动（TD-S），3-中国电信
     * （CDMA），4-互联网电视，6-中国联通（WCDMA)
     */
    private Integer carrier_type;
    /**
     * 设备类型	必填，设备平台类型，1-PC,21-安卓H5平板，22-安卓H5手机，23-苹果H5平板，24-苹果H5手机，31-安卓 APP平板，32-安卓APP手机，33-苹果APP平板，34-苹果APP手机 ，100-OTT
     */
    private Integer device_type;
    /**
     * 横竖屏状态	必填，0-未知，1-竖屏，2-横屏
     */
    private Integer orientation;
    /**
     * GPS坐标经度	GCJ02国家测绘局坐标（火星坐标系）
     */
    private Float lg;
    /**
     * GPS坐标纬度	GCJ02国家测绘局坐标（火星坐标系）
     */
    private Float lt;
    /**
     * 客户端应用包名	移动端必填；pc或者wap站填写主站名
     */
    private String pkgname;
    /**
     * 客户端版本	移动端必填，请正确填写
     */
    private String app_version;
    /**
     * 无线网ssid名称	移动端必填，无线网ssid名称，如获取不到可传空（影响填充）；例如：wifi ssid MGSSID
     */
    private String ssid;
    /**
     * wifi路由器MAC地址	移动端必填，WIFI路由器MAC地址，如获取不到可传空(影响填充）；例如：wifi mac地址 20:a6:cd:7e:e3: 60
     */
    private String wifi_mac;
    /**
     * 手机ROM的版本	移动端必填，手机ROM版本，如获取不到可传空(影响填充）
     */
    private String rom_version;
    /**
     * 系统编译时间	移动端必填，系统编译时间（getLong("ro.build.date.utc") * 1000），如获取不到可传空(影响填充)；例如：系统编译时间豪秒数 1545362006000，
     */
    private String sys_compling_time;
    /**
     * 设备 machine	如“iPhone10,3”, 仅ios需要回传，安卓可不填写该字段，获取方式参考 附录8.1
     */
    private String hardware_machine;
    /**
     * 设备启动时间	设备启动时间，如"1596270702"，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String startup_time;
    /**
     * 系统版本更新时间	系统版本更新时间，如"1596632457.155983"，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String mb_time;
    /**
     * 国家	local地区，如“CN”，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String country_code;
    /**
     * 运营商名称	运营商名称，如“中国移动”，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String carrier_name;
    /**
     * 内存空间，字节	系统总内存空间，单位：字节，如“17179869184” ，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private Long mem_total;
    /**
     * 磁盘总空间，字节	磁盘总空间，单位：字节，如“250685575168” ，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录 8.1
     */
    private Long disk_total;
    /**
     * //时区	local时区，如"28800"，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String local_tz_name;
    /**
     * //设备model	如：D22AP，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String hardware_model;
    /**
     * //系统版本	如：14.0.1，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String os_version;
    /**
     * //语言	设备设置的语言：如"zh -Hans-CN" ， 仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String language;
    /**
     * //设备名称（小写MD5)	MD5(X的iphone )的值，如“ce9ba5a0128991a784d0187d86189b5d”，仅ios需要回传，安卓可不填写该字段 获取方式参考 附录8.1
     */
    private String phone_name;
    /**
     * //广告授权情况(可选字段)	广告标识授权情况，如“3”（代表authorized），仅ios需要回传，安卓可不填写该字段;
     */
    private Integer auth_status;
    /**
     * //cpu数目（可选字段）	CPU数目，如 4，仅ios需要回传，安卓可不填写该字段
     */
    private Integer cpu_num;
    /**
     * //广协caid字段	格式 :version_caid. 如： 20200901_f949f306494646edfee0f939698e1fb8
     */
    private String ios_caid;
    /**
     * //华为应用市场的版本号(只针对华为手机)	必填，具体客户端获取代码见(3)
     */
    private String hms;
    /**
     * //华为AG版本号(只针对华为手机)	必填，具体客户端获取代码见(3)
     */
    private String ag;
    /**
     * //手机硬件厂商(只针对华为手机)	如HUAWEI
     */
    private String maker;

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImei_md5() {
        return imei_md5;
    }

    public void setImei_md5(String imei_md5) {
        this.imei_md5 = imei_md5;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public String getAndroid_id_md5() {
        return android_id_md5;
    }

    public void setAndroid_id_md5(String android_id_md5) {
        this.android_id_md5 = android_id_md5;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getOpenudid() {
        return openudid;
    }

    public void setOpenudid(String openudid) {
        this.openudid = openudid;
    }

    public String getCookie() {
        return cookie;
    }

    public void setCookie(String cookie) {
        this.cookie = cookie;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOsver() {
        return osver;
    }

    public void setOsver(String osver) {
        this.osver = osver;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getSw() {
        return sw;
    }

    public void setSw(Integer sw) {
        this.sw = sw;
    }

    public Integer getSh() {
        return sh;
    }

    public void setSh(Integer sh) {
        this.sh = sh;
    }

    public Integer getPpi() {
        return ppi;
    }

    public void setPpi(Integer ppi) {
        this.ppi = ppi;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public Integer getConnection_type() {
        return connection_type;
    }

    public void setConnection_type(Integer connection_type) {
        this.connection_type = connection_type;
    }

    public Integer getCarrier_type() {
        return carrier_type;
    }

    public void setCarrier_type(Integer carrier_type) {
        this.carrier_type = carrier_type;
    }

    public Integer getDevice_type() {
        return device_type;
    }

    public void setDevice_type(Integer device_type) {
        this.device_type = device_type;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public Float getLg() {
        return lg;
    }

    public void setLg(Float lg) {
        this.lg = lg;
    }

    public Float getLt() {
        return lt;
    }

    public void setLt(Float lt) {
        this.lt = lt;
    }

    public String getPkgname() {
        return pkgname;
    }

    public void setPkgname(String pkgname) {
        this.pkgname = pkgname;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getWifi_mac() {
        return wifi_mac;
    }

    public void setWifi_mac(String wifi_mac) {
        this.wifi_mac = wifi_mac;
    }

    public String getRom_version() {
        return rom_version;
    }

    public void setRom_version(String rom_version) {
        this.rom_version = rom_version;
    }

    public String getSys_compling_time() {
        return sys_compling_time;
    }

    public void setSys_compling_time(String sys_compling_time) {
        this.sys_compling_time = sys_compling_time;
    }

    public String getHardware_machine() {
        return hardware_machine;
    }

    public void setHardware_machine(String hardware_machine) {
        this.hardware_machine = hardware_machine;
    }

    public String getStartup_time() {
        return startup_time;
    }

    public void setStartup_time(String startup_time) {
        this.startup_time = startup_time;
    }

    public String getMb_time() {
        return mb_time;
    }

    public void setMb_time(String mb_time) {
        this.mb_time = mb_time;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getCarrier_name() {
        return carrier_name;
    }

    public void setCarrier_name(String carrier_name) {
        this.carrier_name = carrier_name;
    }

    public Long getMem_total() {
        return mem_total;
    }

    public void setMem_total(Long mem_total) {
        this.mem_total = mem_total;
    }

    public Long getDisk_total() {
        return disk_total;
    }

    public void setDisk_total(Long disk_total) {
        this.disk_total = disk_total;
    }

    public String getLocal_tz_name() {
        return local_tz_name;
    }

    public void setLocal_tz_name(String local_tz_name) {
        this.local_tz_name = local_tz_name;
    }

    public String getHardware_model() {
        return hardware_model;
    }

    public void setHardware_model(String hardware_model) {
        this.hardware_model = hardware_model;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getPhone_name() {
        return phone_name;
    }

    public void setPhone_name(String phone_name) {
        this.phone_name = phone_name;
    }

    public Integer getAuth_status() {
        return auth_status;
    }

    public void setAuth_status(Integer auth_status) {
        this.auth_status = auth_status;
    }

    public Integer getCpu_num() {
        return cpu_num;
    }

    public void setCpu_num(Integer cpu_num) {
        this.cpu_num = cpu_num;
    }

    public String getIos_caid() {
        return ios_caid;
    }

    public void setIos_caid(String ios_caid) {
        this.ios_caid = ios_caid;
    }

    public String getHms() {
        return hms;
    }

    public void setHms(String hms) {
        this.hms = hms;
    }

    public String getAg() {
        return ag;
    }

    public void setAg(String ag) {
        this.ag = ag;
    }

    public String getMaker() {
        return maker;
    }

    public void setMaker(String maker) {
        this.maker = maker;
    }
}
