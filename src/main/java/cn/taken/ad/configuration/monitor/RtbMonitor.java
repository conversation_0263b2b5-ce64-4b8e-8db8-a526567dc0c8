package cn.taken.ad.configuration.monitor;

import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.core.dto.business.statistics.MediaAdvertiserReqMonitorDto;
import cn.taken.ad.core.dto.business.statistics.MediaReqMonitorDto;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class RtbMonitor {
    @Resource(name = "BaseRedisL2Cache")
    private BaseRedisL2Cache baseRedisL2Cache;

    private Map<Long, Map<String, MediaAdvertiserReqMonitorDto>> mediaAdvRequestMonitor = new ConcurrentHashMap<>();
    private Map<Long, Map<String, MediaReqMonitorDto>> mediaRequestMonitor = new ConcurrentHashMap<>();
    private Map<Long, Map<String, AtomicInteger>> mediaErrorCodeMonitor = new ConcurrentHashMap<>();
    private Map<Long, Map<String, AtomicInteger>> advErrorCodeMonitor = new ConcurrentHashMap<>();


    public void monitorAdvRequest(StrategyTagAdvertiser adv, MediaTag tag, int advReq, int advReqSuccess, int advReqFail, int advRespFail, int advReqTimeout, int participating, int use) {
        long second = System.currentTimeMillis() / 1000L;
        String key = adv.getStrategyId() + "-" + adv.getId() + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId() + "-" + adv.getAdvertiserId() + "-" + adv.getAdvertiserAppId() + "-" + adv.getAdvertiserTagId();
        MediaAdvertiserReqMonitorDto monitorDto = mediaAdvRequestMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(key, k -> new MediaAdvertiserReqMonitorDto(
                adv.getStrategyId(),
                adv.getId(),
                tag.getMediaId(),
                tag.getMediaAppId(),
                tag.getId(),
                adv.getAdvertiserId(),
                adv.getAdvertiserAppId(),
                adv.getAdvertiserTagId()
        ));
        monitorDto.getAdvertiserReqTotal().getAndAdd(advReq);
        monitorDto.getAdvertiserReqSuccessTotal().getAndAdd(advReqSuccess);
        monitorDto.getAdvertiserReqFailTotal().getAndAdd(advReqFail);
        monitorDto.getAdvertiserRespFailTotal().getAndAdd(advRespFail);
        monitorDto.getAdvertiserReqTimeoutTotal().getAndAdd(advReqTimeout);
        monitorDto.getAdvertiserParticipatingTotal().getAndAdd(participating);
        monitorDto.getAdvertiserUseTimeTotal().getAndAdd(use);
        monitorDto.updateMinConcurrentValue(use);
        monitorDto.updateMaxConcurrentValue(use);
    }

    public void monitorMediaAdvRequest(Long strategyId, RtbAdvDto adv, MediaTag tag, int mediaReq, int mediaInvalid, int mediaRespFail, int participating,int useTime) {
        long second = System.currentTimeMillis() / 1000L;
        String key = strategyId + "-" + adv.getStrategyTagAdvId() + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId() + "-" + adv.getAdvertiserId() + "-" + adv.getAppId() + "-" + adv.getTagId();
        MediaAdvertiserReqMonitorDto monitorDto = mediaAdvRequestMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(key, k -> new MediaAdvertiserReqMonitorDto(
                strategyId,
                adv.getStrategyTagAdvId(),
                tag.getMediaId(),
                tag.getMediaAppId(),
                tag.getId(),
                adv.getAdvertiserId(),
                adv.getAppId(),
                adv.getTagId()
        ));
        monitorDto.getMediaReqTotal().getAndAdd(mediaReq);
        monitorDto.getMediaReqInvalidTotal().getAndAdd(mediaInvalid);
        monitorDto.getMediaRespFailTotal().getAndAdd(mediaRespFail);
        monitorDto.getMediaParticipatingTotal().getAndAdd(participating);
        monitorDto.getMediaUseTimeTotal().getAndAdd(useTime);
        monitorDto.updateMediaMinConcurrentValue(useTime);
        monitorDto.updateMediaMaxConcurrentValue(useTime);
    }


    public void monitorMediaRequest(Long strategyId, MediaTag tag, int req, int invalid, int respFail, int pp,int useTime) {
        long second = System.currentTimeMillis() / 1000L;
        String key = strategyId + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId();
        Map<String, MediaReqMonitorDto> media = mediaRequestMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>());
        MediaReqMonitorDto monitorDto = media.computeIfAbsent(key, k -> new MediaReqMonitorDto(strategyId,tag.getMediaId(),tag.getMediaAppId(),tag.getId()));
        monitorDto.getMediaReqTotal().getAndAdd(req);
        monitorDto.getMediaReqInvalidTotal().getAndAdd(invalid);
        monitorDto.getMediaParticipatingTotal().getAndAdd(pp);
        monitorDto.getMediaRespFailTotal().getAndAdd(respFail);
        monitorDto.getUseTimeTotal().getAndAdd(useTime);
        monitorDto.updateMinConcurrentValue(useTime);
        monitorDto.updateMaxConcurrentValue(useTime);
    }

    public void monitorMediaErrorCode(Long strategyId, MediaTag tag, String code) {
        long second = System.currentTimeMillis() / 1000L;
        String key = strategyId + "-" + tag.getMediaId() + "-" + tag.getMediaAppId() + "-" + tag.getId() + "-" + code;
        Map<String, AtomicInteger> media = mediaErrorCodeMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>());
        media.computeIfAbsent(key, k -> new AtomicInteger()).incrementAndGet();
    }

    public void monitorAdvertiserErrorCode(StrategyTagAdvertiser adv, String code) {
        long second = System.currentTimeMillis() / 1000L;
        String key = adv.getAdvertiserId() + "-" + adv.getAdvertiserAppId() + "-" + adv.getAdvertiserTagId() + "-" + code;
        advErrorCodeMonitor.computeIfAbsent(second, k -> new ConcurrentHashMap<>()).computeIfAbsent(key, k -> new AtomicInteger()).incrementAndGet();
    }


    public Map<Long, Map<String, MediaAdvertiserReqMonitorDto>> getMediaAdvRequestMonitor() {
        return mediaAdvRequestMonitor;
    }

    public Map<Long, Map<String, MediaReqMonitorDto>> getMediaRequestMonitor() {
        return mediaRequestMonitor;
    }

    public Map<Long, Map<String, AtomicInteger>> getMediaErrorCodeMonitor() {
        return mediaErrorCodeMonitor;
    }

    public Map<Long, Map<String, AtomicInteger>> getAdvErrorCodeMonitor() {
        return advErrorCodeMonitor;
    }
}
