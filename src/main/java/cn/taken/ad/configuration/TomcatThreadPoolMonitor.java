package cn.taken.ad.configuration;

import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.context.event.EventListener;

/**
 * 打印Tomcat线程池信息
 */
//@Component
public class TomcatThreadPoolMonitor {

    private ThreadPoolExecutor tomcatThreadPool;

    public Logger log = LoggerFactory.getLogger(TomcatThreadPoolMonitor.class);

    @EventListener(WebServerInitializedEvent.class)
    public void init(WebServerInitializedEvent event) {
        if (event.getWebServer() instanceof TomcatWebServer) {
            initThreadPool((TomcatWebServer) event.getWebServer());
        }
    }
    private void initThreadPool(TomcatWebServer tomcat) {
        try {
            this.tomcatThreadPool = (ThreadPoolExecutor)
                    tomcat.getTomcat().getConnector().getProtocolHandler().getExecutor();
            log.info("Tomcat线程池监控初始化成功");
        } catch (Exception e) {
            log.error("线程池初始化失败", e);
        }
    }

    @SuperScheduled(fixedDelay = 2000L)
    public void logThreadPoolStatus() {
        int activeCount = tomcatThreadPool.getActiveCount();
        int corePoolSize = tomcatThreadPool.getCorePoolSize();
        int maximumPoolSize = tomcatThreadPool.getMaximumPoolSize();
        int poolSize = tomcatThreadPool.getPoolSize();
        int queueSize = tomcatThreadPool.getQueue().size();
        long completedTaskCount = tomcatThreadPool.getCompletedTaskCount();
        long taskCount = tomcatThreadPool.getTaskCount();

        log.info("当前活跃:{},核心线程数:{},最大线程数:{},总线程数:{},队列大小:{},已完成任务:{},总任务数:{}",activeCount,corePoolSize,maximumPoolSize,poolSize,queueSize,completedTaskCount,taskCount);
    }
}
