package cn.taken.ad.configuration.cache;

import cn.taken.ad.component.redis.RedisClient;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二级缓存
 *
 * <AUTHOR>
 */
@Component
public class BaseRedisL2CacheConfiguration {

    @Resource(name = "BaseRedis")
    private RedisClient redis;

    @Bean(name = "BaseRedisL2Cache", destroyMethod = "close")
    public BaseRedisL2Cache genMemoryCache() {
        return new BaseRedisL2Cache(redis, 10_0000, 60);
    }

}
