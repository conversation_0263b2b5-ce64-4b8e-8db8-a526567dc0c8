package cn.taken.ad.configuration.web.xss;

import cn.taken.ad.constant.web.XssIgnore;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 运营端，拦截没有权限的请求
 *
 * <AUTHOR>
 */
@Component
public class XssInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 不符合继承关系，通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        // 处理 XssIgnore 注解 ,存在注解就不过
        Method method = ((HandlerMethod) handler).getMethod();
        XssIgnore xss = method.getAnnotation(XssIgnore.class);
        if (xss == null) {
            XssState.setEnable();
        } else {
            XssState.setDisable();
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        XssState.remove();
    }

    @Override
    public void afterConcurrentHandlingStarted(HttpServletRequest request, HttpServletResponse response, Object handler) {
        XssState.remove();
    }

}
