# redis
redis:
  base:
    single:
      connectionTimeoutMillis: 10000
      soTimeoutMillis: 10000
      maxIdle: 8
      maxTotal: 32
      minIdle: 1
      maxWaitMillis: 10000
      datePattern: yyyy-MM-dd HH:mm:ss SSS
      password: "@@90!S0Q@vX@O@b3T3n5"
      host: ***************
      port: 6777

# 定时任务
scheduler:
  poolSize: 256
  threadNamePrefix: "ssp-rtb-service-"
  awaitTerminationSeconds: 60

# 自定义参数
properties:
  noticeDomain: ***************:9090
  trackDomain: ***************:9090
  dmpDir: /opt/dmp
  storeDir: /opt/store

dsp:
  resourceDomain: ssp-static.3fahudong.com
  downloadDomain: ssp-download.3fahudong.com

huoshan-clb:
  open: false
  serverGroupId: rsp-13ftsi81ziw3k3n6nu5bgk2it
  region: cn-beijing
  accessKey: AKLTODU0Zjg5NTM0ZDM2NGU2OGI0OGY4ZDdmZjk0NGU3Nzk
  secretKey: WWpneVltVXdOR1l3TXpVMU5HWTRNamt6WVdSak5ETTJaV0U0WmpnMU5XRQ==

# DMP
dmp:
  tos:
    endpoint: tos-cn-beijing.volces.com
    region: cn-beijing
    accessKey: AKLTZmYyYmFhOGMxNTU5NGUyYWE3ZmVhNzdhZjY0OTcxZmY
    secretKey: T1RrMk16bGlOelZpTjJNME5ETTVPRGhpWTJVeVlXWTNObU0yTVRNMVlqUQ==
    bucketName: dmp-jw
    connectTimeoutMills: 30000
