import request from '@/utils/HttpUtils'

const baseUri = 'o/financial/media/'

export function pageApi(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function infoApi(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function modifyApi(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function releaseFinancialApi(data) {
  return request({
    url: baseUri + 'releaseFinancial',
    method: 'post',
    data: data
  })
}

export function updateRatioApi(data) {
  return request({
    url: baseUri + 'updateRatio',
    method: 'post',
    data: data
  })
}

export function mediaPushAllApi(data) {
  return request({
    url: baseUri + 'batchPublish',
    method: 'post',
    data: data
  })
}
