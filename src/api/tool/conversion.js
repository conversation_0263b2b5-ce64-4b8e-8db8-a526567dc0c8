import request from '@/utils/HttpUtils'
import uploadRequest from '@/utils/UploadHttpUtils'

const baseUri = '/o/tool/conversion/'

export function pageApi(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function infoApi(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function modifyApi(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function addApi(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function importApi(data) {
  return uploadRequest({
    url: baseUri + 'import',
    method: 'post',
    data: data
  })
}
