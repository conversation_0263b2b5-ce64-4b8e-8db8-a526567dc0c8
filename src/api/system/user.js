import request from '@/utils/HttpUtils'

const baseUri = 'o/oper/user/'

export function listUser(data) {
  return request({
    url: baseUri + 'list',
    method: 'post',
    data: data
  })
}

export function addUser(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function infoUser(userId) {
  return request({
    url: baseUri + 'info/' + userId,
    method: 'post'
  })
}

export function modifyUser(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function deleteUser(userId) {
  return request({
    url: baseUri + 'oper/delete/' + userId,
    method: 'post'
  })
}

export function onUser(userId) {
  return request({
    url: baseUri + 'oper/on/' + userId,
    method: 'post'
  })
}

export function offUser(userId) {
  return request({
    url: baseUri + 'oper/off/' + userId,
    method: 'post'
  })
}

export function resetPassword(userId) {
  return request({
    url: baseUri + 'password/reset/' + userId,
    method: 'post'
  })
}
