// 用户登录信息--用户信息、权限信息、Token信息
export default {
  state: {
    sessionId: null,
    resources: null,
    userId: null,
    realname: null,
    username: null
  },
  mutations: {
    LOGIN(state, webToken) {
      state.sessionId = webToken.sessionId
      state.resources = webToken.resources
      state.userId = webToken.userId
      state.username = webToken.username
      state.realname = webToken.realname
    },
    LOGOUT(state) {
      state.sessionId = null
      state.resources = null
      state.userId = null
      state.username = null
      state.realname = null
    }
  },
  actions: {
    logIn({ commit }, webToken) {
      commit('LOGIN', webToken)
    },
    logOut({ commit }) {
      commit('LOGOUT')
    }
  }
}
