[2025-06-27 11:02:00.507] [cn.taken.ad.RtbApplication.logStarting,50] INFO  - Starting RtbApplication on chang with PID 44588 (D:\ideaProjects\ssc\ssp-rtb-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-06-27 11:02:00.516] [cn.taken.ad.RtbApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-06-27 11:02:02.148] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - Tomcat initialized with port(s): 9090 (http)
[2025-06-27 11:02:02.173] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-9090"]
[2025-06-27 11:02:02.184] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-06-27 11:02:02.190] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-06-27 11:02:02.300] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-06-27 11:02:02.300] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 1742 ms
[2025-06-27 11:02:03.240] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:02:04.514] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-9090"]
[2025-06-27 11:02:04.934] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 9090 (http) with context path ''
[2025-06-27 11:02:04.952] [cn.taken.ad.RtbApplication.logStarted,59] INFO  - Started RtbApplication in 4.961 seconds (JVM running for 6.224)
[2025-06-27 11:02:04.966] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-06-27 11:02:04.968] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-06-27 11:02:05.016] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-06-27 11:02:05.017] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.startLoadBalance,60] INFO  - not need change load balance
[2025-06-27 11:02:11.559] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-27 11:02:11.560] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-06-27 11:02:11.568] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 8 ms
[2025-06-27 11:02:23.413] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.reqAdv,58] INFO  - request:{"id":"17509933226160049","imp":{"tagId":"70fb1be2b17da29d","w":1080,"h":1920,"bidFloor":100},"app":{"name":"句读","bundle":"tech.caicheng.judourili","ver":"1.1.0","storeUrl":""},"user":{"id":"9000","yob":"2005","gender":1,"keywords":"读书,唱歌"},"device":{"ip":"*************","ua":"Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36","os":"Android","osv":"14","deviceType":1,"geo":{"lat":24.69673728942871,"lon":108.0301742553711,"type":1},"network":{"conType":6,"carrier":1,"mcc":"460","mnc":"00","mac":"02:00:00:00:00:00","macMd5":"0f607264fc6318a92b9e13c65db7cd3c"},"brand":"Xiaomi","model":"2206122SC","orientation":2,"dw":1440,"dh":3036,"density":3.0,"ppi":480,"screenSize":6.699999809265137,"serialno":"","anId":"213e52b37dd4abd6","anIdMd5":"fb28fc6902f655681ad8461c5d7e820c","imei":"867719069081567","imeiMd5":"bae1d38d072f4d214bbd240d2896b774","oaid":"a96133ce3a4e08b4","oaidMd5":"c5d05513e8ea0ee0a3b3ad74e680059f","apiLevel":"34","paid":"c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07","idfa":"","idfaMd5":"","openUdid":"","deviceName":"2206122SC","deviceNameMd5":"","language":"zh","country":"CN","romVer":"","sysComplingTime":"1705591478.444164583","bootTime":0,"updateTime":0,"initTime":"1702568592.000000000","diskSize":-1,"memorySize":-2,"cpuFre":22.8,"timeZone":"28800","bootMark":"7370f11e-e1f8-431a-a20e-4e2d1007a97a","updateMark":"1736182055.678019164","appStoreVer":"","hmsVer":"","bootTimeNano":"1705591478.444164583","updateTimeNano":"1705591478.444164583"}}
[2025-06-27 11:03:10.516] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:03:10.516] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:03:10.516] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:03:10.516] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:03:10.516] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:03:20.702] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.parseResponse,81] INFO  - response:{"code":204,"msg":"无广告返回"}
[2025-06-27 11:03:25.489] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:03:55.576] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:04:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:04:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:04:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:04:01.058] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"1-1-1-1-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271103","mediaId":1,"mediaAppId":1,"mediaTagId":1,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":1}},advertiserErrorCode:{}
[2025-06-27 11:04:01.190] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"1-112-1-1-1-46-46-69":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271103","strategyId":1,"strategyTagAdvId":112,"mediaId":1,"mediaAppId":1,"mediaTagId":1,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":73859,"mediaAvgTime":73859,"mediaMinTime":0,"mediaUseTimeTotal":73859,"advertiserId":46,"advertiserAppId":46,"advertiserTagId":69,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":68364,"advertiserAvgTime":68364,"advertiserMinTime":0,"advertiserUseTimeTotal":68364}},minuteMediaReq:{"1-1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271103","mediaId":1,"mediaAppId":1,"mediaTagId":1,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":1,"maxTime":73859,"avgTime":73859,"minTime":0,"useTimeTotal":73859}},minuteAdvReq:{"46-46-69":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271103","advertiserId":46,"advertiserAppId":46,"advertiserTagId":69,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":68364,"avgTime":68364,"minTime":0,"useTimeTotal":68364}}
[2025-06-27 11:04:25.662] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:04:55.744] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:05:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:05:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:05:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:05:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:05:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:05:25.852] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:05:55.946] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:06:01.000] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:06:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:06:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:06:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:06:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:06:26.027] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:06:56.107] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:07:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:07:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:07:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:07:01.124] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-101-1-9-23-39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271106","strategyId":8,"strategyTagAdvId":101,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":2,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":19699,"mediaAvgTime":11089,"mediaMinTime":0,"mediaUseTimeTotal":22179,"advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"advertiserReqTotal":2,"advertiserReqSuccessTotal":2,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":17430,"advertiserAvgTime":8770,"advertiserMinTime":0,"advertiserUseTimeTotal":17541}},minuteMediaReq:{"8-1-9-23":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271106","mediaId":1,"mediaAppId":9,"mediaTagId":23,"reqTotal":2,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":8,"maxTime":19699,"avgTime":11089,"minTime":0,"useTimeTotal":22179}},minuteAdvReq:{"39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271106","advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"reqTotal":2,"reqSuccessTotal":2,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":17430,"avgTime":8770,"minTime":0,"useTimeTotal":17541}}
[2025-06-27 11:07:01.167] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"8-1-9-23-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271106","mediaId":1,"mediaAppId":9,"mediaTagId":23,"code":"SUCCESS_NON_PARTICIPATION","total":2,"strategyId":8}},advertiserErrorCode:{}
[2025-06-27 11:07:26.187] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:07:56.281] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:08:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:08:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:08:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:08:01.040] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"8-1-9-23-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271107","mediaId":1,"mediaAppId":9,"mediaTagId":23,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":8}},advertiserErrorCode:{}
[2025-06-27 11:08:01.161] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-101-1-9-23-39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271107","strategyId":8,"strategyTagAdvId":101,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":11408,"mediaAvgTime":11408,"mediaMinTime":0,"mediaUseTimeTotal":11408,"advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":104,"advertiserAvgTime":104,"advertiserMinTime":0,"advertiserUseTimeTotal":104}},minuteMediaReq:{"8-1-9-23":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271107","mediaId":1,"mediaAppId":9,"mediaTagId":23,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":8,"maxTime":11408,"avgTime":11408,"minTime":0,"useTimeTotal":11408}},minuteAdvReq:{"39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271107","advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":104,"avgTime":104,"minTime":0,"useTimeTotal":104}}
[2025-06-27 11:08:26.371] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:09:02.314] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:09:02.315] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:09:02.315] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:09:02.316] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:09:10.174] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:09:10.175] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-101-1-9-23-39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271108","strategyId":8,"strategyTagAdvId":101,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":0,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":0,"mediaAvgTime":null,"mediaMinTime":0,"mediaUseTimeTotal":0,"advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":122,"advertiserAvgTime":122,"advertiserMinTime":0,"advertiserUseTimeTotal":122}},minuteMediaReq:{},minuteAdvReq:{"39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271108","advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":122,"avgTime":122,"minTime":0,"useTimeTotal":122}}
[2025-06-27 11:09:40.261] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:10:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:10:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:10:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:10:01.054] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"8-1-9-23-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271109","mediaId":1,"mediaAppId":9,"mediaTagId":23,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":8}},advertiserErrorCode:{}
[2025-06-27 11:10:01.175] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-101-1-9-23-39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271109","strategyId":8,"strategyTagAdvId":101,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":17999,"mediaAvgTime":17999,"mediaMinTime":0,"mediaUseTimeTotal":17999,"advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"advertiserReqTotal":0,"advertiserReqSuccessTotal":0,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":0,"advertiserAvgTime":null,"advertiserMinTime":0,"advertiserUseTimeTotal":0}},minuteMediaReq:{"8-1-9-23":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271109","mediaId":1,"mediaAppId":9,"mediaTagId":23,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":8,"maxTime":17999,"avgTime":17999,"minTime":0,"useTimeTotal":17999}},minuteAdvReq:{"39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271109","advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"reqTotal":0,"reqSuccessTotal":0,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":0,"avgTime":null,"minTime":0,"useTimeTotal":0}}
[2025-06-27 11:10:10.343] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:10:40.432] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:11:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:11:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:11:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:11:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:11:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:11:10.526] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:11:42.367] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:12:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:12:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:12:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:12:01.050] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"8-1-9-23-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271111","mediaId":1,"mediaAppId":9,"mediaTagId":23,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":8}},advertiserErrorCode:{}
[2025-06-27 11:12:01.170] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-101-1-9-23-39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271111","strategyId":8,"strategyTagAdvId":101,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":5861,"mediaAvgTime":5861,"mediaMinTime":0,"mediaUseTimeTotal":5861,"advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":144,"advertiserAvgTime":144,"advertiserMinTime":0,"advertiserUseTimeTotal":144}},minuteMediaReq:{"8-1-9-23":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271111","mediaId":1,"mediaAppId":9,"mediaTagId":23,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":8,"maxTime":5861,"avgTime":5861,"minTime":0,"useTimeTotal":5861}},minuteAdvReq:{"39-39-61":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271111","advertiserId":39,"advertiserAppId":39,"advertiserTagId":61,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":144,"avgTime":144,"minTime":0,"useTimeTotal":144}}
[2025-06-27 11:12:12.459] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:12:42.542] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:13:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:13:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:13:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:13:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:13:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:13:12.635] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 49
[2025-06-27 11:13:42.805] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:14:00.454] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.reqAdv,58] INFO  - request:{"id":"17509933226160653","imp":{"tagId":"a5b9967500554127","w":1080,"h":1920,"bidFloor":100},"app":{"name":"句读","bundle":"tech.caicheng.judourili","ver":"1.1.0","storeUrl":""},"user":{"id":"9000","yob":"2005","gender":1,"keywords":"读书,唱歌"},"device":{"ip":"*************","ua":"Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36","os":"Android","osv":"14","deviceType":1,"geo":{"lat":24.69673728942871,"lon":108.0301742553711,"type":1},"network":{"conType":6,"carrier":1,"mcc":"460","mnc":"00","mac":"02:00:00:00:00:00","macMd5":"0f607264fc6318a92b9e13c65db7cd3c"},"brand":"Xiaomi","model":"2206122SC","orientation":2,"dw":1440,"dh":3036,"density":3.0,"ppi":480,"screenSize":6.699999809265137,"serialno":"","anId":"213e52b37dd4abd6","anIdMd5":"fb28fc6902f655681ad8461c5d7e820c","imei":"867719069081567","imeiMd5":"bae1d38d072f4d214bbd240d2896b774","oaid":"a96133ce3a4e08b4","oaidMd5":"c5d05513e8ea0ee0a3b3ad74e680059f","apiLevel":"34","paid":"c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07","idfa":"","idfaMd5":"","openUdid":"","deviceName":"2206122SC","deviceNameMd5":"","language":"zh","country":"CN","romVer":"","sysComplingTime":"1705591478.444164583","bootTime":0,"updateTime":0,"initTime":"1702568592.000000000","diskSize":-1,"memorySize":-2,"cpuFre":22.8,"timeZone":"28800","bootMark":"7370f11e-e1f8-431a-a20e-4e2d1007a97a","updateMark":"1736182055.678019164","appStoreVer":"","hmsVer":"","bootTimeNano":"1705591478.444164583","updateTimeNano":"1705591478.444164583"}}
[2025-06-27 11:14:01.380] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:14:01.380] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:14:01.380] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:14:01.381] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:14:01.380] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:14:01.402] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.parseResponse,81] INFO  - response:{"code":204,"msg":"无广告返回"}
[2025-06-27 11:14:12.887] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:14:42.976] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:15:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:15:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:15:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:15:01.047] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"8-1-9-23-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271114","mediaId":1,"mediaAppId":9,"mediaTagId":23,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":8}},advertiserErrorCode:{}
[2025-06-27 11:15:01.170] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"8-113-1-9-23-46-46-70":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271114","strategyId":8,"strategyTagAdvId":113,"mediaId":1,"mediaAppId":9,"mediaTagId":23,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":6475,"mediaAvgTime":6475,"mediaMinTime":0,"mediaUseTimeTotal":6475,"advertiserId":46,"advertiserAppId":46,"advertiserTagId":70,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":4109,"advertiserAvgTime":4109,"advertiserMinTime":0,"advertiserUseTimeTotal":4109}},minuteMediaReq:{"8-1-9-23":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271114","mediaId":1,"mediaAppId":9,"mediaTagId":23,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":8,"maxTime":6475,"avgTime":6475,"minTime":0,"useTimeTotal":6475}},minuteAdvReq:{"46-46-70":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271114","advertiserId":46,"advertiserAppId":46,"advertiserTagId":70,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":4109,"avgTime":4109,"minTime":0,"useTimeTotal":4109}}
[2025-06-27 11:15:13.059] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:15:43.151] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:16:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:16:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:16:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:16:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:16:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:16:13.245] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:16:43.339] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:17:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:17:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:17:01.016] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:17:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:17:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:17:13.431] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:17:43.527] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:18:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:18:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:18:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:18:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:18:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:18:13.610] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:18:42.443] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.reqAdv,58] INFO  - request:{"id":"17509933226160753","imp":{"tagId":"929b7d7e66632712","w":1080,"h":1920,"bidFloor":110},"app":{"name":"句读","bundle":"tech.caicheng.judourili","ver":"1.1.0","storeUrl":""},"user":{"id":"9000","yob":"2005","gender":1,"keywords":"读书,唱歌"},"device":{"ip":"*************","ua":"Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36","os":"Android","osv":"14","deviceType":1,"geo":{"lat":24.69673728942871,"lon":108.0301742553711,"type":1},"network":{"conType":6,"carrier":1,"mcc":"460","mnc":"00","mac":"02:00:00:00:00:00","macMd5":"0f607264fc6318a92b9e13c65db7cd3c"},"brand":"Xiaomi","model":"2206122SC","orientation":2,"dw":1440,"dh":3036,"density":3.0,"ppi":480,"screenSize":6.699999809265137,"serialno":"","anId":"213e52b37dd4abd6","anIdMd5":"fb28fc6902f655681ad8461c5d7e820c","imei":"867719069081567","imeiMd5":"bae1d38d072f4d214bbd240d2896b774","oaid":"a96133ce3a4e08b4","oaidMd5":"c5d05513e8ea0ee0a3b3ad74e680059f","apiLevel":"34","paid":"c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07","idfa":"","idfaMd5":"","openUdid":"","deviceName":"2206122SC","deviceNameMd5":"","language":"zh","country":"CN","romVer":"","sysComplingTime":"1705591478.444164583","bootTime":0,"updateTime":0,"initTime":"1702568592.000000000","diskSize":-1,"memorySize":-2,"cpuFre":22.8,"timeZone":"28800","bootMark":"7370f11e-e1f8-431a-a20e-4e2d1007a97a","updateMark":"1736182055.678019164","appStoreVer":"","hmsVer":"","bootTimeNano":"1705591478.444164583","updateTimeNano":"1705591478.444164583"}}
[2025-06-27 11:18:43.047] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.parseResponse,81] INFO  - response:{"id":"17509933226160753","code":200,"msg":"SUCCESS","bid":{"bidId":"1388116336600653827","tagId":"929b7d7e66632712","title":"","desc":"","iconUrl":"https://qh-material.taobao.com/dsp/img/1b5bfc0293517a19e3fd5a56144e3b48.jpg?x-oss-process\u003dimage/resize,w_720,h_1280,limit_0,m_fill\u0026","imgUrls":["https://qh-material.taobao.com/dsp/img/1b5bfc0293517a19e3fd5a56144e3b48.jpg?x-oss-process\u003dimage/resize,w_720,h_1280,limit_0,m_fill\u0026"],"w":720,"h":1280,"landingUrl":"https://h5.m.taobao.com/tnode/index.htm?tabid\u003dvideo\u0026spm\u003d2014.ugdhh\u0026sLaunch\u003d0\u0026skipPreload\u003d1\u0026sModuleName\u003dtnode\u0026sKeep\u003d1\u0026extParams\u003d%7B%22goodItemId%22%3A%22614624098327%22%2C%22sceneSource%22%3A%22guangguang_rtb%22%2C%22iconStreams%22%3A%20%22%5B%7B%5C%22cardType%5C%22%3A%5C%22content%5C%22%7D%5D%22%7D\u0026source\u003doutside\u0026tnode\u003dpage_guangguang%3FnavbarHide%3Dtrue%26initDataKey%3Dguangguang%26immersive%3Dtrue%26pageTrack%3Dfalse\u0026slk_actid\u003d100000000207\u0026dhh_route\u003d4\u0026bc_fl_src\u003dgrowth_dhh_2200803434232_100-1792807-385764718-188062078-cd5e470f080ffd668be929430836a903-53001-0-ADX_XINSHU-wm_fs_1_1\u0026spm\u003d2014.ugdhh.2200803434232.100-1792807-385764718\u0026bootImage\u003d0\u0026force_no_smb\u003dtrue\u0026wh_biz\u003dtm\u0026dpa_Inid\u003d38576471860002\u0026dpa_material_id\u003d614624098327\u0026dpa_source_code\u003d60002\u0026itemIds\u003d614624098327","deeplink":"tbopen://m.taobao.com/tbopen/index.html?source\u003dauto\u0026action\u003dali.open.nav\u0026module\u003dh5\u0026bootImage\u003d0\u0026module\u003dh5\u0026afc_route\u003d1\u0026source\u003dauto\u0026bootImage\u003d0\u0026h5Url\u003dhttps%3A%2F%2Fh5.m.taobao.com%2Ftnode%2Findex.htm%3Ftabid%3Dvideo%26spm%3D2014.ugdhh%26sLaunch%3D0%26skipPreload%3D1%26sModuleName%3Dtnode%26sKeep%3D1%26extParams%3D%257B%2522goodItemId%2522%253A%2522614624098327%2522%252C%2522sceneSource%2522%253A%2522guangguang_rtb%2522%252C%2522iconStreams%2522%253A%2520%2522%255B%257B%255C%2522cardType%255C%2522%253A%255C%2522content%255C%2522%257D%255D%2522%257D%26source%3Doutside%26tnode%3Dpage_guangguang%253FnavbarHide%253Dtrue%2526initDataKey%253Dguangguang%2526immersive%253Dtrue%2526pageTrack%253Dfalse%26slk_actid%3D100000000207%26dhh_route%3D4%26bc_fl_src%3Dgrowth_dhh_2200803434232_100-1792807-385764718-188062078-cd5e470f080ffd668be929430836a903-53001-0-ADX_XINSHU-wm_fs_1_1%26spm%3D2014.ugdhh.2200803434232.100-1792807-385764718%26bootImage%3D0%26force_no_smb%3Dtrue%26wh_biz%3Dtm%26dpa_Inid%3D38576471860002%26dpa_material_id%3D614624098327%26dpa_source_code%3D60002%26itemIds%3D614624098327\u0026bc_fl_src\u003dgrowth_dhh_2200803434232_100-1792807-385764718-188062078-cd5e470f080ffd668be929430836a903-53001-0-ADX_XINSHU-wm_fs_1_1\u0026spm\u003d2014.ugdhh.2200803434232.100-1792807-385764718\u0026dpa_material_type\u003d1\u0026force_no_smb\u003dtrue\u0026slk_actid\u003d100000000207\u0026wh_biz\u003dtm\u0026dpa_Inid\u003d38576471860002\u0026dpa_material_id\u003d614624098327\u0026dpa_source_code\u003d60002\u0026itemIds\u003d614624098327\u0026pageId\u003dscene_1310\u0026strategy_route\u003d0","downloadUrl":"","bidFloor":2000,"winUrls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dvKY-7wm61sBThtB3-10Ta3lDcvsqFAg2rOzA2GHQGRURxmvtIRWI6b27PoVqpdTOBxxycxE1ihEaUvUQFGZ5TVtBbYcHW7V3lPfVJIOwkQ2tlrC6TEGSnsNQJ7bwm2p8f5ruESIgoCdiiOZ4p6WGmLnjGNgt9cM84ynhG6t25uyGHn9pBMAPfuPYymW-33e7yQE5tjSUO3n8JWu3PEKIpu4ky1AIfqODfvMFHiYICyU250FF0xpCy8Oi-eCGFneA_50t3fAWgm6k7xrxA8hq7YvxMU7FntSYmc-SBSe52wInFP8l4-4SxUOVqi9BZZLWZ_fQ4ndsYDOlbnWjnRUHVDE9fOQENTyX11YMctXOaMc5W41SGKg7VECmQksok5SnSvjIVJfIzO3RbFpkLqHfd-zKP6xZoUXX_gRLhV6HEO44TIYhvakPZWqn2KNjII2GxJuOsQydMCZsbqFy2ps9Um-9Lb648n3bcUXLx47GrZWX8OQ6v7oI3loQtsyLC4c4nOCa_VPo-677mB-7yvq3CD1jwlgYt4MowthjCpP-INLhrxsNIvGMJczB4D4YWllkulGop96cJCA56IwxfRTb8_UFSPvV_kIlgZEy4Q3Ye9MstlqRPQKN89FvWNwv24zz7BDLkHTVwnvgj7CJrkTUceU9ITEEH5_0S6so7Mk0JF4EjRUV-PE1CjGm-XCC5qQxFVjLN323RoU-P59dTO1V3Cxm0kNPwN55YFi2qHdMVOosFmRdIa6_fx_aQl-lXK14tV1iyAc69ooqUGJ9JVuSFKDnSa-pGQglyu2YnemnXcufGEh73NB6RvR6M5i_pz7cY7WZsB1qKAZHUOPm-1zm5UvOu1Hp8w6pKHwik6krpFY8pnr4_GLdJyp1n5Y-EGtkHZrAydgaKaYaiVAOhH4zonXUyZq1t4oBycOFmz_5IoBSSyXO6LPOZaQKH2vI-aCj\u0026adType\u003d3001\u0026time\u003d1750994324599\u0026timeInterval\u003d3\u0026sign\u003d6R3BwQ760EBMDoAJxtMT0fSGmtySQxkKns8GoRSQ9CYx4-CHhF1URdO0POC8uR6R\u0026enc\u003d1"],"loseUrls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dvKY-7wm61sBThtB3-10Ta3lDcvsqFAg2rOzA2GHQGRURxmvtIRWI6b27PoVqpdTOBxxycxE1ihEaUvUQFGZ5TVtBbYcHW7V3lPfVJIOwkQ2tlrC6TEGSnsNQJ7bwm2p8f5ruESIgoCdiiOZ4p6WGmLnjGNgt9cM84ynhG6t25uyGHn9pBMAPfuPYymW-33e7yQE5tjSUO3n8JWu3PEKIpu4ky1AIfqODfvMFHiYICyU250FF0xpCy8Oi-eCGFneA_50t3fAWgm6k7xrxA8hq7YvxMU7FntSYmc-SBSe52wInFP8l4-4SxUOVqi9BZZLWZ_fQ4ndsYDOlbnWjnRUHVDE9fOQENTyX11YMctXOaMc5W41SGKg7VECmQksok5SnSvjIVJfIzO3RbFpkLqHfd-zKP6xZoUXX_gRLhV6HEO44TIYhvakPZWqn2KNjII2GxJuOsQydMCZsbqFy2ps9Um-9Lb648n3bcUXLx47GrZWX8OQ6v7oI3loQtsyLC4c4nOCa_VPo-677mB-7yvq3CD1jwlgYt4MowthjCpP-INLhrxsNIvGMJczB4D4YWllkulGop96cJCA56IwxfRTb8_UFSPvV_kIlgZEy4Q3Ye9MstlqRPQKN89FvWNwv24zz7BDLkHTVwnvgj7CJrkTUceU9ITEEH5_0S6so7Mk0JF4EjRUV-PE1CjGm-XCC5qQxFVjLN323RoU-P59dTO1V3Cxm0kNPwN55YFi2qHdMVOosFmRdIa6_fx_aQl-lXK14tV1iyAc69ooqUGJ9JVuSFKDnSa-pGQglyu2YnemnXcufGEh73NB6RvR6M5i_pz7cY7WZsB1qKAZHUOPm-1zm5UvOu1Hp8w6pKHwik6krpFY8pnr4_GLdJyp1n5Y-EGtkHZrAydgaKaYaiVAOhH4zonXUyZq1t4oBycOFmz_5IoBSSyXO6LPOZaQKH2vI-aCj\u0026adType\u003d3002\u0026time\u003d1750994324599\u0026timeInterval\u003d3\u0026sign\u003d6R3BwQ760EBMDoAJxtMT0fSGmtySQxkKns8GoRSQ9CYx4-CHhF1URdO0POC8uR6R\u0026enc\u003d1"],"cType":3,"ciType":2,"app":{"name":"心动日常","bundle":"com.youloft.icloser"},"trackers":[{"type":1,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dvKY-7wm61sBThtB3-10Ta3lDcvsqFAg2rOzA2GHQGRURxmvtIRWI6b27PoVqpdTOBxxycxE1ihEaUvUQFGZ5TVtBbYcHW7V3lPfVJIOwkQ2tlrC6TEGSnsNQJ7bwm2p8f5ruESIgoCdiiOZ4p6WGmLnjGNgt9cM84ynhG6t25uyGHn9pBMAPfuPYymW-33e7yQE5tjSUO3n8JWu3PEKIpu4ky1AIfqODfvMFHiYICyU250FF0xpCy8Oi-eCGFneA_50t3fAWgm6k7xrxA8hq7YvxMU7FntSYmc-SBSe52wInFP8l4-4SxUOVqi9BZZLWZ_fQ4ndsYDOlbnWjnRUHVDE9fOQENTyX11YMctXOaMc5W41SGKg7VECmQksok5SnSvjIVJfIzO3RbFpkLqHfd-zKP6xZoUXX_gRLhV6HEO44TIYhvakPZWqn2KNjII2GxJuOsQydMCZsbqFy2ps9Um-9Lb648n3bcUXLx47GrZWX8OQ6v7oI3loQtsyLC4c4nOCa_VPo-677mB-7yvq3CD1jwlgYt4MowthjCpP-INLhrxsNIvGMJczB4D4YWllkulGop96cJCA56IwxfRTb8_UFSPvV_kIlgZEy4Q3Ye9MstlqRPQKN89FvWNwv24zz7BDLkHTVwnvgj7CJrkTUceU9ITEEH5_0S6so7Mk0JF4EjRUV-PE1CjGm-XCC5qQxFVjLN323RoU-P59dTO1V3Cxm0kNPwN55YFi2qHdMVOosFmRdIa6_fx_aQl-lXK14tV1iyAc69ooqUGJ9JVuSFKDnSa-pGQglyu2YnemnXcufGEh73NB6RvR6M5i_pz7cY7WZsB1qKAZHUOPm-1zm5UvOu1Hp8w6pKHwik6krpFY8pnr4_GLdJyp1n5Y-EGtkHZrAydgaKaYaiVAOhH4zonXUyZq1t4oBycOFmz_5IoBSSyXO6LPOZaQKH2vI-aCj\u0026adType\u003d1\u0026time\u003d1750994324599\u0026timeInterval\u003d3\u0026sign\u003d6R3BwQ760EBMDoAJxtMT0fSGmtySQxkKns8GoRSQ9CYx4-CHhF1URdO0POC8uR6R\u0026enc\u003d1\u0026width\u003d__WIDTH__\u0026height\u003d__HEIGHT__\u0026time_start\u003d__TIME_START__\u0026time_end\u003d__TIME_END__\u0026time_start_se\u003d__TIME_START_SE__\u0026time_end_se\u003d__TIME_END_SE__\u0026price\u003d__PRICE__\u0026display_lux\u003d__DISPLAY_LUX__\u0026display_luy\u003d__DISPLAY_LUY__\u0026display_rdx\u003d__DISPLAY_RDX__\u0026display_rdy\u003d__DISPLAY_RDY__\u0026button_lux\u003d__BUTTON_LUX__\u0026button_luy\u003d__BUTTON_LUY__\u0026button_rbx\u003d__BUTTON_RDX__\u0026button_rby\u003d__BUTTON_RDY__\u0026encoding\u003d%3D%3D"]},{"type":2,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dvKY-7wm61sBThtB3-10Ta3lDcvsqFAg2rOzA2GHQGRURxmvtIRWI6b27PoVqpdTOBxxycxE1ihEaUvUQFGZ5TVtBbYcHW7V3lPfVJIOwkQ2tlrC6TEGSnsNQJ7bwm2p8f5ruESIgoCdiiOZ4p6WGmLnjGNgt9cM84ynhG6t25uyGHn9pBMAPfuPYymW-33e7yQE5tjSUO3n8JWu3PEKIpu4ky1AIfqODfvMFHiYICyU250FF0xpCy8Oi-eCGFneA_50t3fAWgm6k7xrxA8hq7YvxMU7FntSYmc-SBSe52wInFP8l4-4SxUOVqi9BZZLWZ_fQ4ndsYDOlbnWjnRUHVDE9fOQENTyX11YMctXOaMc5W41SGKg7VECmQksok5SnSvjIVJfIzO3RbFpkLqHfd-zKP6xZoUXX_gRLhV6HEO44TIYhvakPZWqn2KNjII2GxJuOsQydMCZsbqFy2ps9Um-9Lb648n3bcUXLx47GrZWX8OQ6v7oI3loQtsyLC4c4nOCa_VPo-677mB-7yvq3CD1jwlgYt4MowthjCpP-INLhrxsNIvGMJczB4D4YWllkulGop96cJCA56IwxfRTb8_UFSPvV_kIlgZEy4Q3Ye9MstlqRPQKN89FvWNwv24zz7BDLkHTVwnvgj7CJrkTUceU9ITEEH5_0S6so7Mk0JF4EjRUV-PE1CjGm-XCC5qQxFVjLN323RoU-P59dTO1V3Cxm0kNPwN55YFi2qHdMVOosFmRdIa6_fx_aQl-lXK14tV1iyAc69ooqUGJ9JVuSFKDnSa-pGQglyu2YnemnXcufGEh73NB6RvR6M5i_pz7cY7WZsB1qKAZHUOPm-1zm5UvOu1Hp8w6pKHwik6krpFY8pnr4_GLdJyp1n5Y-EGtkHZrAydgaKaYaiVAOhH4zonXUyZq1t4oBycOFmz_5IoBSSyXO6LPOZaQKH2vI-aCj\u0026adType\u003d2\u0026time\u003d1750994324599\u0026timeInterval\u003d3\u0026sign\u003d6R3BwQ760EBMDoAJxtMT0fSGmtySQxkKns8GoRSQ9CYx4-CHhF1URdO0POC8uR6R\u0026enc\u003d1\u0026width\u003d__WIDTH__\u0026height\u003d__HEIGHT__\u0026down_x\u003d__DOWN_X__\u0026down_y\u003d__DOWN_Y__\u0026up_x\u003d__UP_X__\u0026up_y\u003d__UP_Y__\u0026adown_x\u003d__ADOWN_X__\u0026adown_y\u003d__ADOWN_Y__\u0026aup_x\u003d__AUP_X__\u0026aup_y\u003d__AUP_Y__\u0026click_id\u003d__CLICK_ID__\u0026time_start\u003d__TIME_START__\u0026time_end\u003d__TIME_END__\u0026time_start_se\u003d__TIME_START_SE__\u0026time_end_se\u003d__TIME_END_SE__\u0026dpname\u003d__DPNAME__\u0026dp_width\u003d__DP_WIDTH__\u0026dp_height\u003d__DP_HEIGHT__\u0026dp_down_x\u003d__DP_DOWN_X__\u0026dp_down_y\u003d__DP_DOWN_Y__\u0026dp_up_x\u003d__DP_UP_X__\u0026dp_up_y\u003d__DP_UP_Y__\u0026max_accx\u003d__X_MAX_ACC__\u0026max_accy\u003d__Y_MAX_ACC__\u0026max_accz\u003d__Z_MAX_ACC__\u0026turnx\u003d__TURN_X__\u0026turny\u003d__TURN_Y__\u0026turnz\u003d__TURN_Z__\u0026turntime\u003d__TURN_TIME__\u0026sld\u003d__SLD__\u0026display_lux\u003d__DISPLAY_LUX__\u0026display_luy\u003d__DISPLAY_LUY__\u0026display_rdx\u003d__DISPLAY_RDX__\u0026display_rdy\u003d__DISPLAY_RDY__\u0026button_lux\u003d__BUTTON_LUX__\u0026button_luy\u003d__BUTTON_LUY__\u0026button_rbx\u003d__BUTTON_RDX__\u0026button_rby\u003d__BUTTON_RDY__\u0026clickarea\u003d__CLICKAREA__\u0026dplink\u003d__DPLINK__\u0026dpresult\u003d__DP_RESULT__\u0026dpreason\u003d__DP_REASON__"]},{"type":17,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dvKY-7wm61sBThtB3-10Ta3lDcvsqFAg2rOzA2GHQGRURxmvtIRWI6b27PoVqpdTOBxxycxE1ihEaUvUQFGZ5TVtBbYcHW7V3lPfVJIOwkQ2tlrC6TEGSnsNQJ7bwm2p8f5ruESIgoCdiiOZ4p6WGmLnjGNgt9cM84ynhG6t25uyGHn9pBMAPfuPYymW-33e7yQE5tjSUO3n8JWu3PEKIpu4ky1AIfqODfvMFHiYICyU250FF0xpCy8Oi-eCGFneA_50t3fAWgm6k7xrxA8hq7YvxMU7FntSYmc-SBSe52wInFP8l4-4SxUOVqi9BZZLWZ_fQ4ndsYDOlbnWjnRUHVDE9fOQENTyX11YMctXOaMc5W41SGKg7VECmQksok5SnSvjIVJfIzO3RbFpkLqHfd-zKP6xZoUXX_gRLhV6HEO44TIYhvakPZWqn2KNjII2GxJuOsQydMCZsbqFy2ps9Um-9Lb648n3bcUXLx47GrZWX8OQ6v7oI3loQtsyLC4c4nOCa_VPo-677mB-7yvq3CD1jwlgYt4MowthjCpP-INLhrxsNIvGMJczB4D4YWllkulGop96cJCA56IwxfRTb8_UFSPvV_kIlgZEy4Q3Ye9MstlqRPQKN89FvWNwv24zz7BDLkHTVwnvgj7CJrkTUceU9ITEEH5_0S6so7Mk0JF4EjRUV-PE1CjGm-XCC5qQxFVjLN323RoU-P59dTO1V3Cxm0kNPwN55YFi2qHdMVOosFmRdIa6_fx_aQl-lXK14tV1iyAc69ooqUGJ9JVuSFKDnSa-pGQglyu2YnemnXcufGEh73NB6RvR6M5i_pz7cY7WZsB1qKAZHUOPm-1zm5UvOu1Hp8w6pKHwik6krpFY8pnr4_GLdJyp1n5Y-EGtkHZrAydgaKaYaiVAOhH4zonXUyZq1t4oBycOFmz_5IoBSSyXO6LPOZaQKH2vI-aCj\u0026adType\u003d17\u0026time\u003d1750994324599\u0026timeInterval\u003d3\u0026sign\u003d6R3BwQ760EBMDoAJxtMT0fSGmtySQxkKns8GoRSQ9CYx4-CHhF1URdO0POC8uR6R\u0026enc\u003d1"]}]}}
[2025-06-27 11:18:44.736] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:19:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:19:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:19:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:19:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:19:01.122] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"5-114-1-1-24-46-46-72":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271118","strategyId":5,"strategyTagAdvId":114,"mediaId":1,"mediaAppId":1,"mediaTagId":24,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":1,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":6437,"mediaAvgTime":6437,"mediaMinTime":0,"mediaUseTimeTotal":6437,"advertiserId":46,"advertiserAppId":46,"advertiserTagId":72,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":1,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":2594,"advertiserAvgTime":2594,"advertiserMinTime":0,"advertiserUseTimeTotal":2594}},minuteMediaReq:{"5-1-1-24":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271118","mediaId":1,"mediaAppId":1,"mediaTagId":24,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":1,"winTotal":0,"amount":0,"strategyId":5,"maxTime":6437,"avgTime":6437,"minTime":0,"useTimeTotal":6437}},minuteAdvReq:{"46-46-72":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271118","advertiserId":46,"advertiserAppId":46,"advertiserTagId":72,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":1,"winTotal":0,"amount":0,"maxTime":2594,"avgTime":2594,"minTime":0,"useTimeTotal":2594}}
[2025-06-27 11:19:14.829] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:19:44.911] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:20:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:20:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:20:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:20:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:20:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:20:15.000] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:20:45.093] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:21:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:21:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:21:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:21:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:21:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:21:15.185] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:21:45.279] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:22:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:22:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:22:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:22:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:22:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:22:15.362] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:22:45.445] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:23:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:23:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:23:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:23:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:23:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:23:15.527] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:23:45.612] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:24:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:24:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:24:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:24:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:24:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:24:15.694] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:24:45.775] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:25:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:25:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:25:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:25:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:25:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:25:15.863] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:25:45.953] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:26:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:26:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:26:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:26:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:26:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:26:16.041] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:26:46.134] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:27:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:27:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:27:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:27:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:27:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:27:16.220] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:27:46.310] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:28:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:28:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:28:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:28:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:28:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:28:16.404] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:28:46.489] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:29:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:29:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:29:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:29:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:29:01.016] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:29:16.582] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:29:46.676] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:30:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:30:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:30:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:30:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:30:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:30:16.764] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:30:46.847] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:31:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:31:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:31:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:31:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:31:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:31:16.944] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:31:47.034] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:32:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:32:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:32:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:32:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:32:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:32:17.119] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:32:47.201] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:33:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:33:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:33:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:33:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:33:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:33:17.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:33:47.381] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:34:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:34:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:34:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:34:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:34:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:34:17.469] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:34:47.557] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:35:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:35:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:35:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:35:17.646] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:35:47.736] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:36:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:36:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:36:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:36:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:36:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:36:17.825] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:36:47.911] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:37:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:37:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:37:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:37:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:37:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:37:17.997] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:37:48.082] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:38:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:38:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:38:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:38:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:38:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:38:18.164] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:38:48.242] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:39:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:39:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:39:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:39:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:39:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:39:18.333] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:39:48.424] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:40:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:40:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:40:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:40:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:40:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:40:18.507] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:40:48.590] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:41:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:41:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:41:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:41:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:41:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:41:18.678] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:41:48.767] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:42:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:42:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:42:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:42:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:42:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:42:18.857] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:42:48.952] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:43:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:43:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:43:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:43:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:43:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:43:19.032] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:43:49.120] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:44:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:44:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:44:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:44:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:44:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:44:19.202] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:44:49.292] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:45:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:45:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:45:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:45:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:45:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:45:19.374] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:45:49.463] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:46:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:46:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:46:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:46:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:46:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:46:19.546] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:46:49.634] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:47:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:47:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:47:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:47:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:47:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:47:19.724] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:47:49.815] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:48:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:48:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:48:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:48:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:48:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:48:19.904] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:48:49.993] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:49:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:49:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:49:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:49:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:49:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:49:20.082] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:49:50.176] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:50:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:50:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:50:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:50:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:50:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:50:20.262] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:50:50.354] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:51:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:51:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:51:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:51:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:51:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:51:20.449] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:51:50.538] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:52:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:52:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:52:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:52:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:52:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:52:20.619] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:52:50.708] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:53:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:53:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:53:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:53:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:53:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:53:20.792] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:53:50.875] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:54:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:54:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:54:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:54:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:54:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:54:20.959] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:54:51.047] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:55:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:55:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:55:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:55:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:55:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:55:21.148] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:55:51.236] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:56:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:56:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:56:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:56:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:56:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:56:21.331] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:56:51.425] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:57:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:57:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:57:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:57:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:57:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:57:21.518] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:57:51.602] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:58:01.000] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:58:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:58:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:58:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:58:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:58:21.683] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:58:51.766] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:59:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 11:59:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 11:59:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 11:59:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 11:59:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 11:59:21.846] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 11:59:51.940] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:00:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:00:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:00:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:00:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:00:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:00:22.021] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:00:52.114] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:01:00.016] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-06-27 12:01:00.020] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-06-27 12:01:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:01:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:01:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:01:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:01:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:01:22.195] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:01:52.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:02:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:02:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:02:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:02:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:02:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:02:22.379] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:02:52.471] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:03:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:03:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:03:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:03:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:03:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:03:22.558] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:03:52.639] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:04:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:04:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:04:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:04:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:04:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:04:22.735] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:04:52.828] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:05:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:05:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:05:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:05:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:05:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:05:22.917] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:05:53.009] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:06:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:06:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:06:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:06:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:06:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:06:23.098] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:06:53.190] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:07:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:07:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:07:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:07:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:07:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:07:23.279] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:07:53.364] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:08:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:08:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:08:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:08:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:08:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:08:23.451] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:08:53.536] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:09:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:09:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:09:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:09:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:09:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:09:23.637] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:09:53.723] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:10:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:10:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:10:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:10:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:10:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:10:23.808] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:10:53.889] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:11:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:11:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:11:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:11:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:11:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:11:23.977] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:11:54.058] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:12:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:12:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:12:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:12:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:12:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:12:24.145] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:12:54.231] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:13:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:13:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:13:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:13:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:13:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:13:24.323] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:13:54.406] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:14:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:14:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:14:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:14:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:14:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:14:24.500] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:14:54.593] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:15:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:15:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:15:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:15:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:15:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:15:24.687] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:15:54.779] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:16:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:16:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:16:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:16:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:16:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:16:24.871] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:16:54.966] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:17:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:17:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:17:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:17:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:17:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:17:25.052] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:17:55.151] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:18:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:18:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:18:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:18:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:18:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:18:25.243] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:18:55.421] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:19:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:19:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:19:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:19:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:19:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:19:25.506] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:19:55.600] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:20:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:20:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:20:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:20:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:20:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:20:25.688] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:20:55.768] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:21:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:21:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:21:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:21:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:21:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:21:25.861] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:21:55.945] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:22:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:22:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:22:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:22:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:22:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:22:26.031] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:22:56.126] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:23:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:23:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:23:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:23:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:23:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:23:26.216] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:23:56.294] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:24:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:24:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:24:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:24:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:24:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:24:26.366] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:24:56.459] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:25:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:25:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:25:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:25:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:25:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:25:26.563] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:25:56.645] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:26:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:26:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:26:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:26:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:26:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:26:26.733] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:26:56.830] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:27:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:27:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:27:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:27:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:27:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:27:26.911] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:27:57.158] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:28:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:28:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:28:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:28:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:28:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:28:27.248] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:28:57.335] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:29:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:29:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:29:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:29:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:29:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:29:27.419] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:29:57.506] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:30:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:30:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:30:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:30:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:30:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:30:27.589] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:30:57.672] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:31:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:31:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:31:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:31:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:31:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:31:27.761] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:31:57.853] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:32:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:32:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:32:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:32:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:32:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:32:27.935] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:32:58.027] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:33:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:33:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:33:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:33:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:33:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:33:28.109] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:33:58.216] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:34:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:34:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:34:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:34:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:34:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:34:28.297] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:34:58.391] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:35:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:35:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:35:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:35:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:35:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:35:28.473] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:35:58.563] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:36:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:36:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:36:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:36:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:36:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:36:28.648] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:36:58.738] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:37:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:37:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:37:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:37:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:37:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:37:28.831] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:37:58.919] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:38:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:38:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:38:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:38:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:38:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:38:29.011] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:38:59.102] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:39:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:39:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:39:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:39:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:39:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:39:29.186] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:39:59.277] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:40:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:40:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:40:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:40:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:40:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:40:29.368] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:40:59.455] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:41:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:41:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:41:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:41:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:41:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:41:29.538] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:41:59.634] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:42:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:42:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:42:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:42:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:42:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:42:29.725] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:42:59.813] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:43:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:43:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:43:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:43:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:43:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:43:29.898] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:43:59.981] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:44:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:44:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:44:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:44:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:44:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:44:30.072] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:45:00.151] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:45:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:45:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:45:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:45:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:45:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:45:30.242] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:46:00.348] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:46:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:46:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:46:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:46:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:46:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:46:30.442] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:47:00.534] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:47:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:47:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:47:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:47:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:47:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:47:30.615] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:48:00.700] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:48:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:48:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:48:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:48:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:48:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:48:30.781] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:49:00.864] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:49:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:49:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:49:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:49:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:49:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:49:30.955] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:50:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:50:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:50:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:50:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:50:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:50:01.042] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:50:31.130] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:51:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:51:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:51:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:51:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:51:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:51:01.221] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:51:31.321] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:52:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:52:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:52:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:52:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:52:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:52:01.402] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:52:31.482] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:53:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:53:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:53:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:53:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:53:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:53:01.570] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:53:31.658] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:54:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:54:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:54:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:54:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:54:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:54:01.740] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:54:31.829] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:55:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:55:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:55:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:55:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:55:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:55:01.911] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:55:31.994] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:56:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:56:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:56:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:56:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:56:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:56:02.074] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:56:32.170] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:57:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:57:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:57:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:57:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:57:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:57:02.254] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:57:32.339] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:58:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:58:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:58:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:58:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:58:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:58:02.430] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:58:32.521] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:59:01.000] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 12:59:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 12:59:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 12:59:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 12:59:01.002] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 12:59:02.615] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 12:59:32.704] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:00:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:00:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:00:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:00:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:00:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:00:02.785] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:00:32.872] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:01:00.008] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-06-27 13:01:00.009] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-06-27 13:01:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:01:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:01:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:01:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:01:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:01:02.959] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:01:33.046] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:02:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:02:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:02:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:02:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:02:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:02:03.130] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:02:33.218] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:03:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:03:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:03:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:03:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:03:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:03:03.313] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:03:33.396] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:04:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:04:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:04:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:04:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:04:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:04:03.483] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:04:33.568] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:05:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:05:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:05:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:05:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:05:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:05:03.654] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:05:33.744] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:06:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:06:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:06:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:06:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:06:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:06:03.837] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:06:33.930] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:07:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:07:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:07:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:07:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:07:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:07:04.012] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:07:34.099] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:08:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:08:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:08:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:08:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:08:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:08:04.181] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:08:34.271] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:09:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:09:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:09:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:09:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:09:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:09:04.365] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:09:34.457] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:10:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:10:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:10:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:10:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:10:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:10:04.540] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:10:34.630] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:11:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:11:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:11:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:11:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:11:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:11:04.722] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:11:34.808] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:12:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:12:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:12:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:12:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:12:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:12:04.898] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:12:34.985] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:13:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:13:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:13:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:13:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:13:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:13:05.066] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:13:35.153] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:14:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:14:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:14:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:14:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:14:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:14:05.247] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:14:35.330] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:15:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:15:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:15:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:15:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:15:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:15:05.421] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:15:35.507] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:16:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:16:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:16:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:16:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:16:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:16:05.590] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:16:35.681] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:17:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:17:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:17:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:17:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:17:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:17:05.767] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:17:35.849] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:18:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:18:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:18:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:18:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:18:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:18:05.934] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:18:36.016] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:19:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:19:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:19:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:19:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:19:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:19:06.111] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:19:36.202] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:20:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:20:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:20:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:20:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:20:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:20:06.294] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:20:36.383] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:21:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:21:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:21:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:21:01.016] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:21:01.016] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:21:06.472] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:21:36.562] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:22:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:22:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:22:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:22:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:22:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:22:06.642] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:22:36.729] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:23:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:23:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:23:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:23:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:23:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:23:06.817] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:23:36.904] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:24:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:24:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:24:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:24:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:24:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:24:06.987] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:24:37.070] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:25:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:25:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:25:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:25:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:25:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:25:07.158] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:25:37.240] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:26:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:26:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:26:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:26:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:26:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:26:07.324] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:26:37.411] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:27:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:27:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:27:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:27:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:27:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:27:07.501] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:27:37.593] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:28:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:28:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:28:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:28:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:28:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:28:07.684] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:28:37.778] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:29:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:29:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:29:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:29:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:29:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:29:07.870] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:29:37.963] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:30:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:30:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:30:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:30:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:30:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:30:08.057] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:30:38.146] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:31:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:31:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:31:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:31:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:31:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:31:08.233] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:31:38.325] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:32:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:32:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:32:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:32:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:32:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:32:08.411] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:32:38.780] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:33:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:33:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:33:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:33:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:33:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:33:08.866] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:33:38.952] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:34:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:34:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:34:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:34:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:34:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:34:09.035] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:34:39.124] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:35:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:35:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:35:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:35:09.205] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:35:39.288] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:36:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:36:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:36:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:36:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:36:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:36:09.370] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:36:39.463] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:37:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:37:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:37:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:37:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:37:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:37:09.555] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:37:39.640] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:38:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:38:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:38:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:38:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:38:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:38:09.725] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:38:39.812] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:39:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:39:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:39:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:39:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:39:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:39:09.897] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:39:39.983] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:40:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:40:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:40:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:40:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:40:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:40:10.073] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:40:40.163] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:41:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:41:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:41:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:41:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:41:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:41:10.251] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:41:40.344] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:42:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:42:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:42:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:42:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:42:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:42:10.438] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:42:40.520] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:43:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:43:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:43:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:43:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:43:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:43:10.614] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:43:40.711] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:44:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:44:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:44:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:44:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:44:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:44:10.798] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:44:40.888] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:45:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:45:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:45:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:45:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:45:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:45:10.975] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:45:41.064] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:46:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:46:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:46:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:46:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:46:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:46:11.143] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:46:41.224] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:47:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:47:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:47:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:47:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:47:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:47:11.309] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:47:41.390] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:48:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:48:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:48:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:48:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:48:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:48:11.482] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:48:41.567] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:49:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:49:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:49:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:49:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:49:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:49:11.659] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:49:41.739] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:50:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:50:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:50:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:50:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:50:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:50:11.821] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:50:41.913] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:51:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:51:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:51:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:51:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:51:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:51:11.998] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:51:42.081] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:52:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:52:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:52:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:52:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:52:01.000] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:52:12.165] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:52:42.256] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:53:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:53:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:53:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:53:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:53:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:53:12.338] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:53:42.422] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:54:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:54:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:54:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:54:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:54:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:54:12.509] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:54:42.597] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:55:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:55:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:55:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:55:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:55:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:55:12.686] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:55:42.768] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:56:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:56:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:56:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:56:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:56:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:56:12.851] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:56:42.937] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:57:01.002] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:57:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:57:01.002] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:57:01.002] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:57:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:57:13.025] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:57:43.119] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:58:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:58:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:58:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:58:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:58:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:58:13.205] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:58:43.299] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:59:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 13:59:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 13:59:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 13:59:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 13:59:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 13:59:13.392] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 13:59:43.472] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:00:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:00:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:00:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:00:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:00:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:00:13.553] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:00:43.635] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:01:00.001] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-06-27 14:01:00.002] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-06-27 14:01:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:01:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:01:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:01:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:01:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:01:13.724] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:01:43.810] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:02:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:02:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:02:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:02:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:02:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:02:13.901] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:02:46.271] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:03:03.291] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:03:03.292] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:03:03.292] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:03:03.292] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:03:03.292] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:03:16.365] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:03:46.457] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:04:01.000] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:04:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:04:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:04:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:04:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:04:16.562] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:04:46.654] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:05:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:05:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:05:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:05:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:05:01.016] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:05:16.737] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:05:46.827] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:06:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:06:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:06:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:06:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:06:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:06:16.908] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:06:46.989] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:07:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:07:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:07:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:07:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:07:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:07:17.074] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:07:47.163] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:08:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:08:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:08:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:08:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:08:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:08:17.256] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:08:47.337] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:09:01.007] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:09:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:09:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:09:01.007] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:09:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:09:17.425] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:09:47.516] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:10:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:10:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:10:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:10:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:10:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:10:17.600] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:10:47.684] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:11:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:11:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:11:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:11:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:11:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:11:17.763] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:11:47.853] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:12:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:12:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:12:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:12:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:12:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:12:17.934] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:12:48.025] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:13:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:13:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:13:01.004] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:13:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:13:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:13:18.116] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:13:48.207] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:14:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:14:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:14:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:14:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:14:01.013] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:14:18.289] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:14:48.373] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:15:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:15:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:15:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:15:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:15:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:15:18.462] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:15:48.552] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:16:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:16:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:16:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:16:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:16:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:16:18.639] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:16:48.720] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:17:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:17:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:17:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:17:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:17:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:17:18.813] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:17:48.896] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:18:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:18:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:18:01.008] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:18:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:18:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:18:19.001] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:18:49.083] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:19:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:19:01.004] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:19:01.004] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:19:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:19:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:19:19.162] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:19:49.253] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:20:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:20:01.012] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:20:01.012] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:20:01.012] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:20:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:20:07.148] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.reqAdv,58] INFO  - request:{"id":"17509933226160853","imp":{"tagId":"70fb1be2b17da29d","w":1080,"h":1920,"bidFloor":100},"app":{"name":"句读","bundle":"tech.caicheng.judourili","ver":"1.1.0","storeUrl":""},"user":{"id":"9000","yob":"2005","gender":1,"keywords":"读书,唱歌"},"device":{"ip":"*************","ua":"Mozilla/5.0 (Linux; Android 14; 2206122SC Build/UKQ1.231003.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/118.0.5993.80 Mobile Safari/537.36","os":"Android","osv":"14","deviceType":1,"geo":{"lat":24.69673728942871,"lon":108.0301742553711,"type":1},"network":{"conType":6,"carrier":1,"mcc":"460","mnc":"00","mac":"02:00:00:00:00:00","macMd5":"0f607264fc6318a92b9e13c65db7cd3c"},"brand":"Xiaomi","model":"2206122SC","orientation":2,"dw":1440,"dh":3036,"density":3.0,"ppi":480,"screenSize":6.699999809265137,"serialno":"","anId":"213e52b37dd4abd6","anIdMd5":"fb28fc6902f655681ad8461c5d7e820c","imei":"867719069081567","imeiMd5":"bae1d38d072f4d214bbd240d2896b774","oaid":"a96133ce3a4e08b4","oaidMd5":"c5d05513e8ea0ee0a3b3ad74e680059f","apiLevel":"34","paid":"c6ee80fe406ed95d9a41b5e24bd07190-5302568d903d8646e855912318369edf-b1d16263cca07c4001d8459597cd7e07","idfa":"","idfaMd5":"","openUdid":"","deviceName":"2206122SC","deviceNameMd5":"","language":"zh","country":"CN","romVer":"","sysComplingTime":"1705591478.444164583","bootTime":0,"updateTime":0,"initTime":"1702568592.000000000","diskSize":-1,"memorySize":-2,"cpuFre":22.8,"timeZone":"28800","bootMark":"7370f11e-e1f8-431a-a20e-4e2d1007a97a","updateMark":"1736182055.678019164","appStoreVer":"","hmsVer":"","bootTimeNano":"1705591478.444164583","updateTimeNano":"1705591478.444164583"}}
[2025-06-27 14:20:07.270] [cn.taken.ad.logic.adv.tianzao.TianZaoAdvProcessor.parseResponse,81] INFO  - response:{"id":"17509933226160853","code":200,"msg":"SUCCESS","bid":{"bidId":"1388161981629562880","tagId":"70fb1be2b17da29d","title":"“大姐,咱们未婚夫下山啦”快,去机场,千万别让小祖宗给跑了","desc":"https://huichuan-mc.sm.cn/function/desc/com.kmxs.reader/84891baccacbc53ed6f01c485c71bb60.html","iconUrl":"https://huichuan-mc.sm.cn/211308269/241114b39e34093a7d36ecbc877c69c5921146.png","imgUrls":["https://content-understand-strategy.sm.cn/4_6_cover/20250107/211308269_197121129/0_1024/250107586fc977b1822cec98a2ae959b582417_ColorfulBig.jpg"],"w":720,"h":1280,"landingUrl":"https://site.u-mob.cn/211308269/6373009/2412217056378335ea42329aabfea09dc74527.html?uctrackid\u003dczoxNzY0Nzc4OTA5NDUwNDE0Mzc3NTtjOjE5NzEyMTEyOTtkOmRtcF82NjI0NDE4ODAyMTg1MDkyODE7cDpoYw","deeplink":"freereader://xiaoshuo.km.com/adload?","downloadUrl":"https://huichuan-mc.sm.cn/apk/59d4eec469c5b1db3bda529bba577194.apk","bidFloor":0,"cType":3,"ciType":2,"app":{"name":"七猫免费小说","bundle":"com.kmxs.reader"},"trackers":[{"type":1,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d1\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1\u0026width\u003d__WIDTH__\u0026height\u003d__HEIGHT__\u0026time_start\u003d__TIME_START__\u0026time_end\u003d__TIME_END__\u0026time_start_se\u003d__TIME_START_SE__\u0026time_end_se\u003d__TIME_END_SE__\u0026price\u003d__PRICE__\u0026display_lux\u003d__DISPLAY_LUX__\u0026display_luy\u003d__DISPLAY_LUY__\u0026display_rdx\u003d__DISPLAY_RDX__\u0026display_rdy\u003d__DISPLAY_RDY__\u0026button_lux\u003d__BUTTON_LUX__\u0026button_luy\u003d__BUTTON_LUY__\u0026button_rbx\u003d__BUTTON_RDX__\u0026button_rby\u003d__BUTTON_RDY__\u0026encoding\u003d%3D%3D"]},{"type":2,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d2\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1\u0026width\u003d__WIDTH__\u0026height\u003d__HEIGHT__\u0026down_x\u003d__DOWN_X__\u0026down_y\u003d__DOWN_Y__\u0026up_x\u003d__UP_X__\u0026up_y\u003d__UP_Y__\u0026adown_x\u003d__ADOWN_X__\u0026adown_y\u003d__ADOWN_Y__\u0026aup_x\u003d__AUP_X__\u0026aup_y\u003d__AUP_Y__\u0026click_id\u003d__CLICK_ID__\u0026time_start\u003d__TIME_START__\u0026time_end\u003d__TIME_END__\u0026time_start_se\u003d__TIME_START_SE__\u0026time_end_se\u003d__TIME_END_SE__\u0026dpname\u003d__DPNAME__\u0026dp_width\u003d__DP_WIDTH__\u0026dp_height\u003d__DP_HEIGHT__\u0026dp_down_x\u003d__DP_DOWN_X__\u0026dp_down_y\u003d__DP_DOWN_Y__\u0026dp_up_x\u003d__DP_UP_X__\u0026dp_up_y\u003d__DP_UP_Y__\u0026max_accx\u003d__X_MAX_ACC__\u0026max_accy\u003d__Y_MAX_ACC__\u0026max_accz\u003d__Z_MAX_ACC__\u0026turnx\u003d__TURN_X__\u0026turny\u003d__TURN_Y__\u0026turnz\u003d__TURN_Z__\u0026turntime\u003d__TURN_TIME__\u0026sld\u003d__SLD__\u0026display_lux\u003d__DISPLAY_LUX__\u0026display_luy\u003d__DISPLAY_LUY__\u0026display_rdx\u003d__DISPLAY_RDX__\u0026display_rdy\u003d__DISPLAY_RDY__\u0026button_lux\u003d__BUTTON_LUX__\u0026button_luy\u003d__BUTTON_LUY__\u0026button_rbx\u003d__BUTTON_RDX__\u0026button_rby\u003d__BUTTON_RDY__\u0026clickarea\u003d__CLICKAREA__\u0026dplink\u003d__DPLINK__\u0026dpresult\u003d__DP_RESULT__\u0026dpreason\u003d__DP_REASON__"]},{"type":3,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d3\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":4,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d4\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":5,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d5\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":6,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d6\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":7,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d7\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":20,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d20\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":16,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d16\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":17,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d17\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]},{"type":18,"urls":["http://madx.sweet-data.com/callv2/notifyv2?context\u003dCo5isl4l6-x2oqs5kgmE99WFADhJJxT-37b5iQXne4qFvA5TF2EZnliyd8vEmO6wSYgoZ-a23dMz68DprGVsWAy0pMJC2wliYO5LcRAcu6W_woGwp4KhAU8fxvpb1o4J20PgrLCdJ1EJNcLS4m_2OUNRtHmjjuR3i1exkQmO1fbd4sASVk0g124m1PP1hDj9pqimh4KImmyZhWCnqkIKGSEE4gyl2VgE7DOdATyVaU7n6k8zRIwF0onGmFjg_Nl0u9UBGMd2BxTQRp3FVA6RJVMubgGdTPOjs3daDi3j9X6pI8ToR_aCCdbbzNEf3vNl3iUnIVDrS25S56citQCyUMyOuvbIiPJsmg9ROyob1VdNq4UXtT0gDYNNSXOioF-GNq1oCY5zzbuuzye1lrXGiMh68wuYkxUUXXgq5eJLChUKQpLT0SzzlwxS9xOMO8vRMkRA5H4SKnzYkN0tJ-P7va8wNGnrfw9BCuLBmeLmdel-4IzPvfSNO8rc82xR4lw4S0B7vYypxvJl9TctJBmYZrp5PRjhFmTG5s6wR4na0nossvWOc7jqS7C-iLyBgdx_x8Q-ChSKg-jACNVnYBAtPk27bFI8jpbM4ZGNFdWRG4-yxZAAcTcmmjFm8l8tb1AUMO2dGIXGvwzU_mGNOXAGQqaGtND_1-U8DAirqDw_thg0R14KUqZ2hp-wjWg7_vWegIftN7B7NMbV8S2Zjl-c2eML-YYtBSZI5F8neqIsw1aFnTHr8zUk8o8Ln23nmn79rt1efH0TINvj7ujYv7Hw0me8gP_e3SvqaTSdY5CPnTbbWUor42kl2bXrVFQcGMvakNAJck8PQ8Nbz1l9W5wQz42RdZiW4sLnVKk3NdVis9o3HDe0M9Q1ukkpSd2pnqQc14DsrnOX3L2rrDUSO3Qnbw\u0026adType\u003d18\u0026time\u003d1751005207222\u0026timeInterval\u003d3\u0026sign\u003dRkExHsogstOfuH1CSKZZdFvjdY91yrcTc7Lin9QYmtx904JEmrr1ka-Wc7XiAqAI\u0026enc\u003d1"]}],"universalLink":""}}
[2025-06-27 14:20:19.339] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:20:49.419] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:21:01.013] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:21:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:21:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:21:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:21:01.134] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"1-112-1-1-1-46-46-69":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271420","strategyId":1,"strategyTagAdvId":112,"mediaId":1,"mediaAppId":1,"mediaTagId":1,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":1,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":4220,"mediaAvgTime":4220,"mediaMinTime":0,"mediaUseTimeTotal":4220,"advertiserId":46,"advertiserAppId":46,"advertiserTagId":69,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":1,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":212,"advertiserAvgTime":212,"advertiserMinTime":0,"advertiserUseTimeTotal":212}},minuteMediaReq:{"1-1-1-1":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271420","mediaId":1,"mediaAppId":1,"mediaTagId":1,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":1,"winTotal":0,"amount":0,"strategyId":1,"maxTime":4220,"avgTime":4220,"minTime":0,"useTimeTotal":4220}},minuteAdvReq:{"46-46-69":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506271420","advertiserId":46,"advertiserAppId":46,"advertiserTagId":69,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":1,"winTotal":0,"amount":0,"maxTime":212,"avgTime":212,"minTime":0,"useTimeTotal":212}}
[2025-06-27 14:21:19.500] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:21:49.589] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:22:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:22:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:22:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:22:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:22:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:22:19.680] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:22:49.761] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:23:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:23:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:23:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:23:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:23:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:23:19.855] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:23:49.947] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:24:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:24:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:24:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:24:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:24:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:24:20.037] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:24:50.127] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:25:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:25:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:25:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:25:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:25:01.001] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:25:20.209] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:25:50.293] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:26:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:26:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:26:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:26:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:26:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:26:20.381] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:26:50.464] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:27:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:27:01.009] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:27:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:27:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:27:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:27:20.570] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:27:50.654] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:28:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:28:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:28:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:28:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:28:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:28:20.739] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:28:50.847] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:29:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:29:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:29:01.011] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:29:01.011] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:29:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:29:20.933] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:29:51.020] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:30:00.999] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:30:00.999] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:30:00.999] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:30:00.999] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:30:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:30:21.115] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:30:51.203] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:31:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:31:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:31:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:31:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:31:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:31:21.292] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:31:51.383] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:32:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:32:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:32:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:32:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:32:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:32:21.467] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:32:51.549] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:33:01.000] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:33:01.000] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:33:01.001] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:33:01.001] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:33:01.001] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:33:21.629] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:33:51.719] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:34:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:34:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:34:01.016] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:34:01.016] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:34:01.016] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:34:21.807] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:34:51.890] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:35:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:35:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:35:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:35:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:35:21.974] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:35:52.057] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:36:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:36:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:36:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:36:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:36:01.011] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:36:22.139] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:36:52.230] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
[2025-06-27 14:37:01.003] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-27 14:37:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-27 14:37:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-27 14:37:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-27 14:37:01.003] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-27 14:37:22.318] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 709b4b01ad0b4ebbbe0bcdeb4d085235 , 53
