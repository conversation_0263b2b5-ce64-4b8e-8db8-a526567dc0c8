[2025-06-19 19:52:43.539] [cn.taken.ad.configuration.web.log.LogAspect.log,182] ERROR - ip[127.0.0.1] user[changyw] uri[/web/o/financial/advertiser/page] stack[cn.taken.ad.api.oper.financial.FinancialAdvertiserApi.page] time[2025-06-19 19:52:43 to 2025-06-19 19:52:43] params[req={"beginTime":"20250612","endTime":"20250619","advertiserId":null,"advertiserAppId":null,"advertiserTagId":null,"state":null,"settlementType":null,"realData":null,"start":0,"limit":20} ; ] response[]  ERROR
org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [select count(*)  from financial_advertiser a  left join advertiser b on b.id = a.advertiser_id  left join advertiser_app c on c.id = a.advertiser_app_id  left join advertiser_tag d on d.id = a.advertiser_tag_id  left join statistics_advertiser_request smr on smr.advertiser_id = a.advertiser_id and smr.advertiser_app_id = a.advertiser_app_id and smr.advertiser_tag_id = a.advertiser_tag_id and smr.statistics_time = a.report_time and smr.statistics_type='DAY' and smr.strategy_id != 0 where 1=1  and a.report_time >= ?  and a.report_time <= ? ]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'smr.strategy_id' in 'on clause'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:633) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:669) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:700) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:712) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:783) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:804) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageCountInMysql(JdbcTemplateUtils.java:179) ~[classes/:?]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageByMapperInMysql(JdbcTemplateUtils.java:133) ~[classes/:?]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageByClassInMysql(JdbcTemplateUtils.java:118) ~[classes/:?]
	at cn.taken.ad.component.orm.AbstractSqlDaoSupport.findObjectPageByClassInMysql(AbstractSqlDaoSupport.java:168) ~[classes/:?]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl.page(FinancialAdvertiserDaoImpl.java:63) ~[classes/:?]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl$$FastClassBySpringCGLIB$$4118ba3d.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl$$EnhancerBySpringCGLIB$$4ab7a1a9.page(<generated>) ~[classes/:?]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl.findPage(FinancialAdvertiserServiceImpl.java:34) ~[classes/:?]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl$$FastClassBySpringCGLIB$$cad21b43.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl$$EnhancerBySpringCGLIB$$46862c55.findPage(<generated>) ~[classes/:?]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi.page(FinancialAdvertiserApi.java:62) ~[classes/:?]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi$$FastClassBySpringCGLIB$$9bc534f6.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:55) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:62) [spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93) [spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) [spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi$$EnhancerBySpringCGLIB$$10561212.page(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:893) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:798) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at cn.taken.ad.configuration.web.xss.XssFilter.doFilter(XssFilter.java:16) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_152]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_152]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_152]
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'smr.strategy_id' in 'on clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1003) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at org.apache.tomcat.jdbc.pool.StatementFacade$StatementProxy.invoke(StatementFacade.java:114) ~[tomcat-jdbc-9.0.36.jar:?]
	at com.sun.proxy.$Proxy128.executeQuery(Unknown Source) ~[?:?]
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:678) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:617) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	... 88 more
[2025-06-19 19:52:43.574] [cn.taken.ad.configuration.web.exception.GlobalExceptionHandler.jsonErrorHandler,75] ERROR - PreparedStatementCallback; bad SQL grammar [select count(*)  from financial_advertiser a  left join advertiser b on b.id = a.advertiser_id  left join advertiser_app c on c.id = a.advertiser_app_id  left join advertiser_tag d on d.id = a.advertiser_tag_id  left join statistics_advertiser_request smr on smr.advertiser_id = a.advertiser_id and smr.advertiser_app_id = a.advertiser_app_id and smr.advertiser_tag_id = a.advertiser_tag_id and smr.statistics_time = a.report_time and smr.statistics_type='DAY' and smr.strategy_id != 0 where 1=1  and a.report_time >= ?  and a.report_time <= ? ]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'smr.strategy_id' in 'on clause'
org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [select count(*)  from financial_advertiser a  left join advertiser b on b.id = a.advertiser_id  left join advertiser_app c on c.id = a.advertiser_app_id  left join advertiser_tag d on d.id = a.advertiser_tag_id  left join statistics_advertiser_request smr on smr.advertiser_id = a.advertiser_id and smr.advertiser_app_id = a.advertiser_app_id and smr.advertiser_tag_id = a.advertiser_tag_id and smr.statistics_time = a.report_time and smr.statistics_type='DAY' and smr.strategy_id != 0 where 1=1  and a.report_time >= ?  and a.report_time <= ? ]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'smr.strategy_id' in 'on clause'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:633) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:669) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:700) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:712) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:783) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:804) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageCountInMysql(JdbcTemplateUtils.java:179) ~[classes/:?]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageByMapperInMysql(JdbcTemplateUtils.java:133) ~[classes/:?]
	at cn.taken.ad.component.orm.JdbcTemplateUtils.findObjectPageByClassInMysql(JdbcTemplateUtils.java:118) ~[classes/:?]
	at cn.taken.ad.component.orm.AbstractSqlDaoSupport.findObjectPageByClassInMysql(AbstractSqlDaoSupport.java:168) ~[classes/:?]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl.page(FinancialAdvertiserDaoImpl.java:63) ~[classes/:?]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl$$FastClassBySpringCGLIB$$4118ba3d.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.dao.financial.impl.FinancialAdvertiserDaoImpl$$EnhancerBySpringCGLIB$$4ab7a1a9.page(<generated>) ~[classes/:?]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl.findPage(FinancialAdvertiserServiceImpl.java:34) ~[classes/:?]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl$$FastClassBySpringCGLIB$$cad21b43.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:295) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98) ~[spring-tx-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.core.service.financial.impl.FinancialAdvertiserServiceImpl$$EnhancerBySpringCGLIB$$46862c55.findPage(<generated>) ~[classes/:?]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi.page(FinancialAdvertiserApi.java:62) ~[classes/:?]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi$$FastClassBySpringCGLIB$$9bc534f6.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:752) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:55) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:62) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) ~[spring-aop-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at cn.taken.ad.api.oper.financial.FinancialAdvertiserApi$$EnhancerBySpringCGLIB$$10561212.page(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) ~[spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:893) ~[spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:798) ~[spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at cn.taken.ad.configuration.web.xss.XssFilter.doFilter(XssFilter.java:16) [classes/:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_152]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_152]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.36.jar:9.0.36]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_152]
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'smr.strategy_id' in 'on clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1003) ~[mysql-connector-java-8.0.20.jar:8.0.20]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_152]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_152]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_152]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_152]
	at org.apache.tomcat.jdbc.pool.StatementFacade$StatementProxy.invoke(StatementFacade.java:114) ~[tomcat-jdbc-9.0.36.jar:?]
	at com.sun.proxy.$Proxy128.executeQuery(Unknown Source) ~[?:?]
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:678) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:617) ~[spring-jdbc-5.1.16.RELEASE.jar:5.1.16.RELEASE]
	... 88 more
