ALTER TABLE `ssp`.`statistics_advertiser_event`
ADD COLUMN `repeat_total` bigint(0) NOT NULL DEFAULT 0 COMMENT '重复数量' AFTER `total`;

ALTER TABLE `ssp`.`statistics_media_advertiser_event`
ADD COLUMN `repeat_total` bigint(0) NOT NULL DEFAULT 0 COMMENT '重复事件数' AFTER `total`;

ALTER TABLE `ssp`.`statistics_media_event`
ADD COLUMN `repeat_total` bigint(0) NOT NULL DEFAULT 0 COMMENT '重复事件数' AFTER `strategy_id`;

ALTER TABLE `ssp`.`advertiser_tag`
ADD COLUMN `filter_repeat_event` tinyint(4) NULL DEFAULT 0 COMMENT '是否过滤重复事件0否1是' AFTER `filter_pack_id`;

ALTER TABLE `ssp`.`statistics_dsp_adv_ad_event`
ADD COLUMN `repeat_total` bigint(0) NOT NULL DEFAULT 0 COMMENT '重复事件数' AFTER `total`;

ALTER TABLE `ssp`.`statistics_dsp_ad_event`
ADD COLUMN `repeat_total` bigint(0) NOT NULL DEFAULT 0 COMMENT '重复事件数' AFTER `total`;